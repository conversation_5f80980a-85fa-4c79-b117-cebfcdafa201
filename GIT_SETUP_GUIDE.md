# Git Setup Guide for Indoor Autonomous Vehicle System

## 🎯 Overview
This guide explains how to properly set up the git repository to avoid missing files when cloning.

## ❌ Common Issues After Git Clone
When you clone this repository, you might encounter these errors:
- `Failed to find the following files: package.xml`
- `No such file or directory: maps/`
- Build failures due to missing dependencies

## 🔧 Quick Fix for Missing Files
If you encounter missing files after cloning:

```bash
# Run this command to create all missing files automatically
./fix_system.sh --setup-missing-files

# Then build the system
source /opt/ros/humble/setup.bash
colcon build --packages-select lidar_node ultrasonic_array sensor_fusion mapping web_interface indoor_navigation
colcon build --packages-select indoor_nav_bringup
```

## 📁 Essential Files That Must Be in Git

### ✅ Files That SHOULD Be Tracked
These files are essential and must be included in the repository:

```
src/
├── indoor_nav_bringup/
│   ├── package.xml ✅
│   ├── CMakeLists.txt ✅
│   ├── launch/ ✅
│   ├── config/ ✅
│   ├── urdf/ ✅
│   ├── worlds/ ✅
│   ├── scripts/ ✅
│   └── maps/ ✅ (directory with basic config files)
├── perception/
│   ├── mapping/
│   │   ├── package.xml ✅ (CRITICAL - was missing)
│   │   ├── setup.py ✅
│   │   └── mapping/ ✅
│   └── sensor_fusion/
│       ├── package.xml ✅ (CRITICAL - was missing)
│       ├── setup.py ✅
│       └── sensor_fusion/ ✅
├── sensors/
│   ├── lidar_node/
│   │   ├── package.xml ✅
│   │   └── setup.py ✅
│   └── ultrasonic_array/
│       ├── package.xml ✅
│       └── setup.py ✅
├── navigation/
│   └── indoor_navigation/
│       ├── package.xml ✅
│       └── setup.py ✅
└── interfaces/
    └── web_interface/
        ├── package.xml ✅
        └── setup.py ✅
```

### ❌ Files That Should NOT Be Tracked
These files are generated and should remain in `.gitignore`:

```
build/ ❌ (ROS2 build artifacts)
install/ ❌ (ROS2 install artifacts)
log/ ❌ (ROS2 log files)
node_modules/ ❌ (npm dependencies)
__pycache__/ ❌ (Python cache)
*.pyc ❌ (Python compiled files)
```

## 🔧 Fixed .gitignore Issues

### Before (Problematic)
```gitignore
maps/          # ❌ This excluded essential map config files
*.xml          # ❌ This excluded package.xml files
```

### After (Fixed)
```gitignore
# maps/ - COMMENTED OUT: maps directory contains essential navigation files
# *.xml - COMMENTED OUT: package.xml files are essential for ROS2 packages
```

## 🚀 Proper Setup Workflow

### For Repository Maintainers
1. **Ensure all essential files are tracked**:
   ```bash
   git add src/perception/mapping/package.xml
   git add src/perception/sensor_fusion/package.xml
   git add src/indoor_nav_bringup/maps/
   ```

2. **Verify .gitignore is not too aggressive**:
   ```bash
   git check-ignore src/perception/mapping/package.xml  # Should return nothing
   git check-ignore src/indoor_nav_bringup/maps/       # Should return nothing
   ```

3. **Test the repository**:
   ```bash
   # Clone to a fresh directory and test
   git clone <repository> test_clone
   cd test_clone
   ./fix_system.sh --setup-missing-files  # Should create minimal files
   colcon build  # Should work without errors
   ```

### For Users Cloning the Repository
1. **Clone the repository**:
   ```bash
   git clone <repository>
   cd indoor_autonomous_vehicle
   ```

2. **Fix any missing files**:
   ```bash
   ./fix_system.sh --setup-missing-files
   ```

3. **Follow the installation guide**:
   ```bash
   # See INSTALLATION_GUIDE.md for complete setup
   ```

## 🔍 Verification Commands

### Check for Missing Files
```bash
# Check if essential package.xml files exist
find src -name "package.xml" | wc -l  # Should be 6 or more

# Check if maps directory exists
ls -la src/indoor_nav_bringup/maps/   # Should contain yaml files

# Test build
source /opt/ros/humble/setup.bash
colcon build --packages-select mapping sensor_fusion  # Should succeed
```

### Verify Git Tracking
```bash
# Check what files are tracked
git ls-files | grep -E "(package\.xml|maps/)"

# Check what files are ignored
git status --ignored
```

## 📋 Checklist for Repository Health

- [ ] All `package.xml` files are tracked in git
- [ ] `maps/` directory with basic config files is tracked
- [ ] `.gitignore` doesn't exclude essential files
- [ ] Fresh clone + `./fix_system.sh --setup-missing-files` works
- [ ] Build succeeds after fresh clone
- [ ] `./start_system.sh` works after fresh setup

## 🆘 Troubleshooting

### "package.xml not found" after git clone
```bash
./fix_system.sh --setup-missing-files
```

### "maps directory missing" error
```bash
./fix_system.sh --setup-missing-files
```

### Build fails with dependency errors
```bash
# Build packages in correct order
colcon build --packages-select lidar_node ultrasonic_array sensor_fusion mapping web_interface indoor_navigation
colcon build --packages-select indoor_nav_bringup
```

## 📞 Support
If you encounter issues after following this guide:
1. Check the terminal output for specific error messages
2. Run `./fix_system.sh --check-system` for diagnostics
3. Ensure you're following the INSTALLATION_GUIDE.md completely

---

*This guide ensures the repository works correctly for all users after git clone operations.*
