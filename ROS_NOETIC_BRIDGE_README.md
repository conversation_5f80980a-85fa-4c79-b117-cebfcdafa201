# ROS Noetic to Backend Bridge

This bridge connects your **real ROS Noetic system** to the existing web interface backend, allowing you to control and monitor your real robot through the web interface.

## 🎯 Overview

The bridge acts as a translator between:
- **Your Real ROS Noetic Robot** ↔ **Web Interface Backend**
- Subscribes to your robot's ROS topics
- Publishes commands from web interface to your robot
- Provides real-time data streaming via WebSocket

## 📋 Prerequisites

### On Your ROS Noetic Machine:
1. **ROS Noetic** installed and working
2. **Your robot system** running with these topics:
   - `/scan` (LiDAR data)
   - `/odom_from_laser` (Robot odometry from laser)
   - `/cmd_vel` (Velocity commands)
   - `/move_base_simple/goal` (Navigation goals)
   - `/map` (Optional: SLAM map)

3. **Python dependencies**:
   ```bash
   pip install websocket-client requests
   ```

### On Your Backend Machine:
- The existing web interface backend running
- WebSocket server on port 8001 (or configured port)

## 🚀 Quick Start

### 1. Setup Your ROS Noetic System
First, make sure your real robot system is running:

```bash
# On your ROS Noetic machine
roscore

# Start your robot drivers (example)
roslaunch your_robot_package robot.launch

# Start SLAM/navigation (example)
roslaunch your_robot_package navigation.launch
```

### 2. Configure the Bridge
Edit `bridge_config.yaml` to match your setup:

```yaml
# Backend server (where your web interface runs)
backend_host: "*************"  # IP of your backend server
backend_port: 8000             # Backend port

# ROS topics (adjusted to match your robot)
ros_topics:
  scan: "/scan"                 # Your LiDAR topic
  odom: "/odom_from_laser"      # Your odometry topic (from laser)
  cmd_vel: "/cmd_vel"           # Your velocity command topic
```

### 3. Start the Bridge
```bash
# Basic usage (backend on localhost)
./start_bridge.sh

# Connect to remote backend
./start_bridge.sh --host *************

# Custom port
./start_bridge.sh --host ************* --port 3000
```

### 4. Verify Connection
Check bridge status:
```bash
curl http://localhost:9090/status
```

## 📡 Data Flow

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Real ROS Robot    │    │   Bridge (This)     │    │   Web Interface     │
│   (ROS Noetic)      │    │                     │    │   Backend           │
├─────────────────────┤    ├─────────────────────┤    ├─────────────────────┤
│ /scan        ──────►│    │ ──────► WebSocket ──│───►│ Real-time Display   │
│ /odom        ──────►│    │ ──────► HTTP API ───│───►│ Robot Status        │
│ /map         ──────►│    │                     │    │                     │
│              ◄──────│    │ ◄────── WebSocket ──│◄───│ Movement Commands   │
│ /cmd_vel     ◄──────│    │ ◄────── HTTP API ───│◄───│ Navigation Goals    │
│ /goal        ◄──────│    │                     │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## 🔧 Configuration Options

### Command Line Options
```bash
./start_bridge.sh [OPTIONS]

Options:
  --host HOST     Backend server IP (default: localhost)
  --port PORT     Backend server port (default: 8000)
  --config FILE   Configuration file (default: bridge_config.yaml)
  --help          Show help message
```

### Configuration File (`bridge_config.yaml`)
```yaml
# Backend connection
backend_host: "localhost"
backend_port: 8000
websocket_port: 8001

# ROS topic mappings
ros_topics:
  scan: "/scan"
  odom: "/odom"
  cmd_vel: "/cmd_vel"
  goal: "/move_base_simple/goal"

# Update rates
update_rate: 10  # Hz

# Robot parameters
robot:
  max_linear_velocity: 0.5   # m/s
  max_angular_velocity: 1.0  # rad/s
```

## 📊 Monitoring

### Bridge Status Endpoint
```bash
# Check bridge health
curl http://localhost:9090/status

# Response example:
{
  "bridge_status": "connected",
  "ros_connected": true,
  "websocket_connected": true,
  "last_scan": true,
  "last_odom": true,
  "robot_status": {
    "position": {"x": 1.2, "y": 0.5, "theta": 0.1},
    "linear_velocity": 0.0,
    "angular_velocity": 0.0
  }
}
```

### ROS Topic Monitoring
```bash
# Check if bridge is receiving data
rostopic echo /scan | head -5
rostopic echo /odom | head -5

# Check if bridge is publishing commands
rostopic echo /cmd_vel
```

## 🔍 Troubleshooting

### Bridge Won't Start
1. **Check ROS Noetic installation**:
   ```bash
   source /opt/ros/noetic/setup.bash
   roscore
   ```

2. **Check Python dependencies**:
   ```bash
   python -c "import rospy, websocket, requests"
   ```

3. **Check required topics**:
   ```bash
   rostopic list | grep -E "(scan|odom|cmd_vel)"
   ```

### Bridge Connects But No Data
1. **Verify topics are publishing**:
   ```bash
   rostopic hz /scan
   rostopic hz /odom
   ```

2. **Check topic names match your robot**:
   - Edit `bridge_config.yaml`
   - Update topic names to match your system

### Web Interface Shows "Disconnected"
1. **Check backend connectivity**:
   ```bash
   curl http://YOUR_BACKEND_IP:8000
   ```

2. **Check WebSocket connection**:
   ```bash
   # Bridge logs should show "WebSocket connection opened"
   ```

3. **Verify firewall settings**:
   - Ensure ports 8000, 8001, 9090 are open

### Commands Not Reaching Robot
1. **Check if bridge receives commands**:
   - Bridge logs should show "Received command: move_robot"

2. **Verify cmd_vel topic**:
   ```bash
   rostopic echo /cmd_vel
   ```

3. **Check robot driver**:
   - Ensure your robot subscribes to `/cmd_vel`

## 🔧 Advanced Configuration

### Custom Topic Names
If your robot uses different topic names:

```yaml
ros_topics:
  scan: "/your_robot/laser_scan"
  odom: "/your_robot/odometry"
  cmd_vel: "/your_robot/cmd_vel"
  goal: "/your_robot/move_base_simple/goal"
```

### Multiple Robots
Run separate bridge instances with different configurations:

```bash
# Robot 1
./start_bridge.sh --config robot1_config.yaml

# Robot 2  
./start_bridge.sh --config robot2_config.yaml
```

### Network Setup
For remote backend:

```yaml
backend_host: "*************"  # Backend server IP
backend_port: 8000
websocket_port: 8001

network:
  connection_timeout: 10
  auto_reconnect: true
  reconnect_interval: 5
```

## 📝 Files Overview

- `ros_noetic_bridge.py` - Main bridge script
- `start_bridge.sh` - Startup script with checks
- `bridge_config.yaml` - Configuration file
- `ROS_NOETIC_BRIDGE_README.md` - This documentation

## 🆘 Support

If you encounter issues:

1. **Check bridge logs** for error messages
2. **Verify ROS topics** are publishing data
3. **Test backend connectivity** manually
4. **Check firewall/network** settings
5. **Ensure topic names match** your robot configuration

The bridge provides detailed logging to help diagnose connection and data flow issues.
