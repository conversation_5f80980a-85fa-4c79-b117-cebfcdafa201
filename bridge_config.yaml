# ROS Noetic to Backend Bridge Configuration
# Configure this file to match your setup

# Backend server configuration
backend_host: "localhost"        # IP address of your backend server
backend_port: 8000              # HTTP port of your backend
websocket_port: 8001            # WebSocket port for real-time communication

# Bridge update rate
update_rate: 10                 # Hz - how often to send status updates

# ROS topic mappings (adjusted to match your real robot)
ros_topics:
  # Input topics (from your real robot)
  scan: "/scan_forward"         # LiDAR data topic (your system uses /scan_forward)
  odom: "/odom_from_laser"      # Odometry topic (your system uses /odom_from_laser)
  map: "/map"                   # SLAM map topic (if available)
  
  # Output topics (to your real robot)
  cmd_vel: "/cmd_vel"           # Velocity commands
  goal: "/move_base_simple/goal" # Navigation goals

# Robot configuration
robot:
  # Robot physical parameters
  max_linear_velocity: 0.5      # m/s
  max_angular_velocity: 1.0     # rad/s
  
  # Safety parameters
  emergency_stop_distance: 0.2  # m
  obstacle_threshold: 0.5       # m
  
  # Battery simulation (if not available from real robot)
  simulate_battery: true
  battery_level: 100            # %

# Network configuration
network:
  # Connection timeout
  connection_timeout: 5         # seconds
  
  # Reconnection settings
  auto_reconnect: true
  reconnect_interval: 5         # seconds
  max_reconnect_attempts: 10
  
  # HTTP status server
  status_server_port: 9090      # Port for bridge status endpoint

# Logging configuration
logging:
  level: "INFO"                 # DEBUG, INFO, WARN, ERROR
  log_ros_messages: false       # Log all ROS messages (verbose)
  log_websocket_messages: false # Log WebSocket traffic (verbose)

# Data filtering (to reduce bandwidth)
data_filters:
  # LiDAR scan filtering
  scan_decimation: 1            # Send every Nth scan (1 = all scans)
  scan_range_filter: true       # Filter out invalid ranges
  
  # Odometry filtering
  odom_decimation: 1            # Send every Nth odometry message
  
  # Map filtering
  map_decimation: 10            # Send every Nth map update (maps are large)

# Coordinate frame transformations (if needed)
transforms:
  # If your robot uses different coordinate frames
  base_frame: "base_link"
  odom_frame: "odom"
  map_frame: "map"
  laser_frame: "laser_frame"
