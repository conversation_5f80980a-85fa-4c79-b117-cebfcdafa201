# 🚗 Indoor Autonomous Vehicle - Deployment Guide

## 📋 Table of Contents
1. [System Requirements](#system-requirements)
2. [Installation Guide](#installation-guide)
3. [Quick Start](#quick-start)
4. [System Components](#system-components)
5. [Running the System](#running-the-system)
6. [Testing & Validation](#testing--validation)
7. [Troubleshooting](#troubleshooting)

## 🖥️ System Requirements

### Minimum Requirements:
- **OS**: Ubuntu 22.04 LTS (recommended) or Ubuntu 20.04 LTS
- **RAM**: 8GB minimum, 16GB recommended
- **CPU**: 4 cores minimum, 8 cores recommended
- **GPU**: Not required (CPU-only simulation)
- **Storage**: 20GB free space
- **Network**: Internet connection for package installation

### Virtual Machine Requirements:
- **VMware Workstation/Player**: 16.0+ or **VirtualBox**: 6.0+
- **Allocated RAM**: 8GB minimum, 12GB recommended
- **Allocated CPU**: 4 cores minimum
- **3D Acceleration**: Enabled (for Gazebo GUI)
- **Shared Folders**: Optional (for file transfer)

## 🔧 Installation Guide

### Step 1: Install ROS2 Humble

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y curl gnupg2 lsb-release software-properties-common

# Add ROS2 repository
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null

# Install ROS2 Humble
sudo apt update
sudo apt install -y ros-humble-desktop
sudo apt install -y ros-humble-gazebo-ros-pkgs
sudo apt install -y ros-humble-navigation2 ros-humble-nav2-bringup
sudo apt install -y python3-colcon-common-extensions
sudo apt install -y python3-rosdep python3-pip

# Initialize rosdep
sudo rosdep init
rosdep update

# Setup environment
echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### Step 2: Clone and Build the Project

```bash
# Create workspace
mkdir -p ~/indoor_autonomous_vehicle
cd ~/indoor_autonomous_vehicle

# Clone the project (if from repository)
# git clone <repository_url> .

# Install dependencies
rosdep install --from-paths src --ignore-src -r -y

# Build the project
colcon build

# Source the workspace
echo "source ~/indoor_autonomous_vehicle/install/setup.bash" >> ~/.bashrc
source ~/indoor_autonomous_vehicle/install/setup.bash
```

### Step 3: Verify Installation

```bash
# Check ROS2 installation
ros2 --version

# Check available packages
ros2 pkg list | grep indoor

# Test Gazebo
gazebo --version
```

## 🚀 Quick Start

### Option 1: Full System Launch (Recommended)

```bash
# Navigate to workspace
cd ~/indoor_autonomous_vehicle
source install/setup.bash

# Launch complete autonomous vehicle system
ros2 launch test_navigation_simple.launch.py map_file:=$(pwd)/maps/simple_map.yaml
```

### Option 2: Step-by-Step Launch

```bash
# Terminal 1: Launch Gazebo simulation
ros2 launch indoor_nav_bringup gazebo_simulation.launch.py

# Terminal 2: Launch localization system
ros2 launch test_localization_working.launch.py map_file:=$(pwd)/maps/simple_map.yaml

# Terminal 3: Launch path planning
ros2 launch path_planning path_planning.launch.py

# Terminal 4: Launch decision making
ros2 launch decision_making decision_making.launch.py
```

## 🧩 System Components

### Core Components:
1. **🏠 Gazebo Simulation**: Virtual environment with indoor house
2. **🤖 Robot Model**: Differential drive robot with LiDAR
3. **🗺️ Mapping System**: SLAM and static map support
4. **📍 Localization**: AMCL-based pose estimation
5. **🧠 Path Planning**: A* global + Pure Pursuit local planning
6. **🛡️ Safety System**: Real-time obstacle avoidance
7. **📋 Mission Management**: Multi-waypoint autonomous missions

### Available Maps:
- `simple_map.yaml`: Basic indoor environment
- `complex_map.yaml`: Advanced multi-room layout

## 🎮 Running the System

### 1. Basic Navigation Test

```bash
# Send a navigation goal
ros2 topic pub /move_base_simple/goal geometry_msgs/PoseStamped '{
  header: {frame_id: "map"}, 
  pose: {
    position: {x: 2.0, y: 2.0, z: 0.0}, 
    orientation: {w: 1.0}
  }
}' --once
```

### 2. Mission Execution

```bash
# Start predefined mission
ros2 service call /start_mission std_srvs/Empty

# Go to next waypoint
ros2 service call /next_goal std_srvs/Empty

# Go to random goal
ros2 service call /random_goal std_srvs/Empty
```

### 3. Manual Control

```bash
# Manual robot control
ros2 run teleop_twist_keyboard teleop_twist_keyboard --ros-args --remap cmd_vel:=/cmd_vel
```

### 4. System Monitoring

```bash
# Monitor system status
ros2 topic echo /behavior_coordinator/status
ros2 topic echo /safety_monitor/status
ros2 topic echo /path_executor/status

# View robot pose
ros2 topic echo /amcl_pose

# Monitor planned path
ros2 topic echo /global_path
```

## 🧪 Testing & Validation

### Automated Testing

```bash
# Run navigation system test
python3 test_navigation.py

# Run perception test
python3 test_perception.py

# Run localization test
python3 test_localization.py
```

### Manual Testing Checklist

- [ ] ✅ Gazebo launches successfully
- [ ] ✅ Robot spawns in simulation
- [ ] ✅ Map loads correctly
- [ ] ✅ AMCL localizes robot
- [ ] ✅ Path planning generates paths
- [ ] ✅ Robot follows planned paths
- [ ] ✅ Obstacle avoidance works
- [ ] ✅ Emergency stop functions
- [ ] ✅ Mission execution completes

### Performance Monitoring

```bash
# Check system resource usage
htop

# Monitor ROS2 node performance
ros2 node list
ros2 topic hz /scan
ros2 topic hz /amcl_pose

# Check for errors
ros2 doctor
```

## 🔧 Configuration

### Robot Parameters
Edit `src/indoor_nav_bringup/config/robot_params.yaml`:
```yaml
robot:
  max_linear_velocity: 0.5  # m/s
  max_angular_velocity: 1.0  # rad/s
  wheel_separation: 0.32     # meters
  wheel_radius: 0.05         # meters
```

### Navigation Parameters
Edit `src/navigation/path_planning/config/planning_params.yaml`:
```yaml
global_planner:
  planning_algorithm: 'astar'  # or 'dijkstra'
  heuristic_weight: 1.0
  obstacle_inflation: 0.2      # meters

local_planner:
  max_linear_velocity: 0.5     # m/s
  min_obstacle_distance: 0.5   # meters
  lookahead_distance: 1.0      # meters
```

### Mission Configuration
Edit `missions.json`:
```json
{
  "missions": [
    {
      "name": "Room Patrol",
      "waypoints": [
        {"x": 2.0, "y": 2.0, "yaw": 0.0, "wait_time": 5.0},
        {"x": -2.0, "y": 2.0, "yaw": 1.57, "wait_time": 5.0}
      ],
      "repeat": false
    }
  ]
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Gazebo Won't Start
```bash
# Check if Gazebo is already running
killall gzserver gzclient

# Clear Gazebo cache
rm -rf ~/.gazebo/log/*

# Restart with verbose output
gazebo --verbose
```

#### 2. AMCL Not Localizing
```bash
# Set initial pose manually
ros2 topic pub /initialpose geometry_msgs/PoseWithCovarianceStamped '{
  header: {frame_id: "map"},
  pose: {
    pose: {position: {x: 0.0, y: 0.0, z: 0.0}, orientation: {w: 1.0}},
    covariance: [0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.068]
  }
}' --once
```

#### 3. Path Planning Fails
```bash
# Check if map is loaded
ros2 topic echo /map --once

# Verify robot pose
ros2 topic echo /amcl_pose --once

# Check goal validity
ros2 topic echo /move_base_simple/goal --once
```

#### 4. Performance Issues
```bash
# Reduce Gazebo graphics quality
export GAZEBO_MODEL_PATH=$GAZEBO_MODEL_PATH:$(pwd)/models
gazebo --verbose -s libgazebo_ros_init.so -s libgazebo_ros_factory.so --minimal_comms
```

### Log Analysis
```bash
# View ROS2 logs
ros2 log level set /global_planner DEBUG
ros2 log level set /local_planner DEBUG

# Check system logs
journalctl -f | grep ros
```

### Virtual Machine Optimization
```bash
# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable cups

# Optimize graphics
export LIBGL_ALWAYS_SOFTWARE=1  # For software rendering
export GAZEBO_IP=127.0.0.1      # Local simulation only
```

## 📊 System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gazebo Sim    │    │   Robot Model   │    │   Sensors       │
│   Environment   │◄──►│   URDF/SDF     │◄──►│   LiDAR/Camera  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mapping       │    │  Localization   │    │   Perception    │
│   SLAM/Static   │◄──►│   AMCL/EKF     │◄──►│   Processing    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Path Planning   │    │ Decision Making │    │ Safety Monitor  │
│ Global + Local  │◄──►│ Behavior Brain  │◄──►│ Emergency Stop  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Motion Control  │    │ Mission Manager │    │ User Interface  │
│ Cmd Velocity    │◄──►│ Waypoint Exec   │◄──►│ RViz/Topics     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 Success Indicators

When the system is running correctly, you should see:

1. **✅ Gazebo**: 3D simulation environment with robot
2. **✅ RViz**: Visualization with map, robot, and planned paths
3. **✅ Console Output**: No critical errors, successful component initialization
4. **✅ Topics Active**: All required topics publishing data
5. **✅ Navigation**: Robot successfully navigates to goals
6. **✅ Safety**: Emergency stop activates near obstacles

## 📞 Support

For issues and questions:
- Check the troubleshooting section above
- Review ROS2 logs for error messages
- Verify all dependencies are installed
- Ensure sufficient system resources

---

**🎉 Congratulations! Your Indoor Autonomous Vehicle is ready for virtual deployment!**
