# 🌐 Indoor Autonomous Vehicle - Web Interface Guide

## 📋 Tổng quan

Web Interface cho Indoor Autonomous Vehicle bao gồm:
- **Backend**: FastAPI server làm cầu nối ROS2 ↔ Web
- **Frontend**: React application với Material-UI
- **WebSocket**: Real-time communication
- **REST API**: HTTP endpoints cho control và monitoring

## 🚀 Quick Start

### 1. Launch Complete System
```bash
# Start everything (ROS2 + Backend + Frontend)
./launch_web_interface.sh

# Or start specific components
./launch_web_interface.sh web      # Only web interface
./launch_web_interface.sh backend  # Only backend API
./launch_web_interface.sh frontend # Only frontend
```

### 2. Access Web Interface
- **Web Interface**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🏗️ Architecture

### Backend (FastAPI + Python)
```
web_interface/backend/
├── app/
│   └── main.py                 # FastAPI application
├── api/
│   ├── robot_control.py        # Robot movement APIs
│   ├── navigation.py           # Navigation APIs
│   ├── sensors.py              # Sensor data APIs
│   └── system.py               # System status APIs
├── ros_bridge/
│   └── ros_interface.py        # ROS2 ↔ Web bridge
├── websocket/
│   └── websocket_manager.py    # WebSocket management
├── requirements.txt            # Python dependencies
└── start_backend.py            # Backend launcher
```

### Frontend (React + TypeScript)
```
web_interface/frontend/
├── src/
│   ├── components/
│   │   ├── RobotControl.tsx    # Robot control panel
│   │   ├── NavigationPanel.tsx # Navigation controls
│   │   ├── SensorDisplay.tsx   # Sensor visualization
│   │   ├── SystemStatus.tsx    # System monitoring
│   │   ├── MapViewer.tsx       # Map and path display
│   │   └── LogViewer.tsx       # System logs
│   ├── hooks/
│   │   └── useWebSocket.ts     # WebSocket hook
│   └── App.tsx                 # Main application
├── package.json                # Node.js dependencies
└── build/                      # Production build
```

## 🎮 Features

### 1. Robot Control Panel
- **Manual Control**: Arrow keys for movement
- **Speed Settings**: Adjustable linear/angular speed
- **Emergency Stop**: Immediate stop button
- **Home Navigation**: Return to origin
- **Initial Pose**: Set AMCL initial position

### 2. Navigation Panel
- **Goal Setting**: Click on map to set navigation goals
- **Predefined Locations**: Quick navigation to common areas
- **Mission Planning**: Multi-waypoint missions (future)
- **Path Visualization**: Real-time path display

### 3. Sensor Display
- **LiDAR Visualization**: Real-time scan data
- **Ultrasonic Sensors**: Proximity readings
- **Battery Status**: Power level monitoring
- **Obstacle Detection**: Real-time obstacle alerts

### 4. System Status
- **Node Monitoring**: ROS2 node status
- **Performance Metrics**: CPU, memory, disk usage
- **Diagnostics**: System health information
- **Connection Status**: Real-time connectivity

### 5. Map Viewer
- **Interactive Map**: Click to navigate
- **Robot Position**: Real-time robot pose
- **Path Planning**: Visualize planned paths
- **Obstacle Overlay**: Show detected obstacles

### 6. Log Viewer
- **System Logs**: Real-time ROS2 logs
- **Filtering**: Filter by log level
- **Search**: Find specific log entries
- **Export**: Save logs to file

## 🔌 API Endpoints

### Robot Control
```bash
# Movement commands
POST /api/robot/move
POST /api/robot/stop
POST /api/robot/home
POST /api/robot/set_initial_pose

# Predefined movements
POST /api/robot/move/forward
POST /api/robot/move/backward
POST /api/robot/rotate/left
POST /api/robot/rotate/right

# Status
GET /api/robot/pose
GET /api/robot/status
```

### Navigation
```bash
# Navigation goals
POST /api/navigation/goal
POST /api/navigation/cancel
GET /api/navigation/status
GET /api/navigation/map

# Predefined locations
POST /api/navigation/goto/kitchen
POST /api/navigation/goto/living_room
POST /api/navigation/goto/bedroom
```

### Sensors
```bash
# Sensor data
GET /api/sensors/scan
GET /api/sensors/ultrasonic
GET /api/sensors/battery
GET /api/sensors/all

# Processed data
GET /api/sensors/scan/obstacles
GET /api/sensors/scan/summary
```

### System
```bash
# System information
GET /api/system/status
GET /api/system/nodes
GET /api/system/diagnostics
GET /api/system/logs
GET /api/system/performance
GET /api/system/health
```

## 🔄 WebSocket Communication

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
```

### Subscribe to Data
```javascript
ws.send(JSON.stringify({
  type: 'subscribe',
  topics: ['pose', 'scan', 'battery']
}));
```

### Send Commands
```javascript
ws.send(JSON.stringify({
  type: 'command',
  command: 'move',
  params: { linear_x: 0.2, angular_z: 0.0 }
}));
```

### Receive Data
```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log('Received:', message.data_type, message.data);
};
```

## 🛠️ Development

### Backend Development
```bash
cd web_interface/backend

# Install dependencies
pip3 install -r requirements.txt

# Start development server
python3 start_backend.py

# Test API
curl http://localhost:8000/api/system/status
```

### Frontend Development
```bash
cd web_interface/frontend

# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build
```

### Adding New Features

#### 1. Add Backend API
```python
# In api/new_feature.py
from fastapi import APIRouter
router = APIRouter()

@router.get("/new_endpoint")
async def new_endpoint():
    return {"message": "New feature"}
```

#### 2. Add Frontend Component
```typescript
// In components/NewComponent.tsx
import React from 'react';

const NewComponent: React.FC = () => {
  return <div>New Component</div>;
};

export default NewComponent;
```

#### 3. Add WebSocket Data Type
```python
# In ros_bridge/ros_interface.py
def new_data_callback(self, msg):
    data = {"value": msg.data}
    self.notify_websocket('new_data', data)
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Backend Won't Start
```bash
# Check ROS2 environment
source /opt/ros/humble/setup.bash

# Check dependencies
pip3 install -r web_interface/backend/requirements.txt

# Check port availability
lsof -i :8000
```

#### 2. Frontend Build Fails
```bash
# Clear cache and reinstall
cd web_interface/frontend
rm -rf node_modules package-lock.json
npm install

# Fix TypeScript issues
npm install --save-dev @types/node
```

#### 3. WebSocket Connection Failed
```bash
# Check backend is running
curl http://localhost:8000/health

# Check firewall
sudo ufw allow 8000
sudo ufw allow 3000
```

#### 4. ROS2 Bridge Not Working
```bash
# Check ROS2 nodes
ros2 node list

# Check topics
ros2 topic list

# Test ROS2 communication
ros2 topic echo /scan
```

### Performance Optimization

#### 1. Reduce WebSocket Data Rate
```python
# In websocket_manager.py
self.min_broadcast_interval = {
    'scan': 0.5,  # Reduce from 0.2 to 0.5 seconds
    'pose': 0.2,  # Reduce from 0.1 to 0.2 seconds
}
```

#### 2. Optimize Frontend Rendering
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{data}</div>;
});
```

## 📊 Monitoring

### System Health
- **Backend Health**: http://localhost:8000/health
- **API Status**: http://localhost:8000/api/system/status
- **Performance**: http://localhost:8000/api/system/performance

### Logs
- **Backend Logs**: Console output from start_backend.py
- **Frontend Logs**: Browser developer console
- **ROS2 Logs**: Available via /api/system/logs

## 🔒 Security

### Production Deployment
1. **Change default ports**
2. **Add authentication**
3. **Use HTTPS/WSS**
4. **Configure CORS properly**
5. **Add rate limiting**

### Example Production Config
```python
# In main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Specific origins
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

---

**🎉 The Indoor Autonomous Vehicle Web Interface is ready for real-time robot control and monitoring!**
