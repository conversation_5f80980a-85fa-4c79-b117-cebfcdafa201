# 🚀 Quick Start Guide - Indoor Autonomous Vehicle

## ⚡ TL;DR - Start Immediately

```bash
# Start web interface
./scripts/launch_working_web.sh backend

# Open browser: http://localhost:8000
# Control your robot! 🤖
```

## 🎯 Common Commands

### **Start Backend:**
```bash
./scripts/launch_working_web.sh backend
```
**Result:** Web interface available at http://localhost:8000

### **Test Backend:**
```bash
./scripts/launch_working_web.sh test
```
**Note:** Only works if backend is already running

### **Start Complete System:**
```bash
./scripts/launch_working_web.sh full
```
**Result:** ROS2 navigation + Web interface

## ❓ Understanding Test Messages

### **When Backend is NOT Running:**
```
[WARNING] Backend is not currently running on port 8000

[INFO] This is normal if you haven't started the backend yet.
[INFO] To start the backend, run:
   ./scripts/launch_working_web.sh backend
```

**This is NORMAL behavior, not an error!** 
The test command is telling you how to start the backend.

### **When Backend IS Running:**
```
[SUCCESS] Backend health check passed
[SUCCESS] Robot API working
[SUCCESS] All tests passed!
```

## 🎮 Typical Usage Flow

### **Option 1: Single Terminal**
```bash
# Start backend (runs in foreground)
./scripts/launch_working_web.sh backend

# Open browser: http://localhost:8000
# Use web interface to control robot
```

### **Option 2: Multiple Terminals**
```bash
# Terminal 1: Start backend
./scripts/launch_working_web.sh backend

# Terminal 2: Test backend
./scripts/launch_working_web.sh test

# Terminal 3: Other commands
curl http://localhost:8000/health
```

### **Option 3: Background Mode**
```bash
# Start backend in background
./scripts/launch_working_web.sh backend &

# Wait for startup
sleep 5

# Test backend
./scripts/launch_working_web.sh test

# Use web interface
open http://localhost:8000
```

## 🌐 Web Interface Features

Once backend is running, access **http://localhost:8000** for:

- **🎮 Robot Control** - Move forward/backward, turn, stop
- **🗺️ Navigation** - Click on map to set goals
- **📊 Monitoring** - Real-time sensor data
- **🔧 System Status** - Health and diagnostics
- **📚 API Docs** - http://localhost:8000/docs

## 🔧 Troubleshooting

### **"Backend is not running" Message**
**This is normal!** Just start the backend:
```bash
./scripts/launch_working_web.sh backend
```

### **Port 8000 Already in Use**
```bash
# Kill existing processes
pkill -f uvicorn
pkill -f simple_backend

# Try again
./scripts/launch_working_web.sh backend
```

### **Cannot Access Web Interface**
```bash
# Check if backend is running
curl http://localhost:8000/health

# If not working, restart backend
./scripts/launch_working_web.sh backend
```

## 📚 More Information

- **Complete Documentation:** `docs/`
- **Deployment Guide:** `docs/DEPLOYMENT_GUIDE.md`
- **Web Interface Guide:** `docs/WEB_INTERFACE_GUIDE.md`
- **Backend Troubleshooting:** `docs/BACKEND_FIXED_GUIDE.md`

## 🎉 Success Indicators

### **Backend Started Successfully:**
```
[SUCCESS] Backend started successfully
[INFO] Web Interface: http://localhost:8000
[INFO] API Documentation: http://localhost:8000/docs
```

### **Web Interface Working:**
- Browser shows robot control interface
- Buttons respond to clicks
- Real-time data updates

### **System Healthy:**
```bash
curl http://localhost:8000/health
# Response: {"status":"healthy","robot_status":"idle"}
```

---

## 🎯 Remember:

1. **Start backend first** with `./scripts/launch_working_web.sh backend`
2. **Test messages are helpful**, not errors
3. **Web interface** is at http://localhost:8000
4. **API docs** are at http://localhost:8000/docs

**🚀 Happy robot controlling!** 🤖✨
