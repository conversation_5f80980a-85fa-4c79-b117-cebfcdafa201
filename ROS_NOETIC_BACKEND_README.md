# ROS Noetic Backend

This is a **ROS Noetic version** of the web interface backend that connects directly to your real ROS Noetic system without needing the separate bridge.

## 🎯 Overview

Instead of using the ROS2 backend + bridge setup, this provides:
- **Direct ROS Noetic Integration**: Backend runs with ROS Noetic Python bindings
- **Same Web Interface**: Uses the same frontend and API endpoints
- **Real-time Data**: WebSocket streaming of robot data
- **Full Control**: Send commands directly to your ROS Noetic robot

## 📋 Prerequisites

### System Requirements:
1. **ROS Noetic** installed and working
2. **Python 3** (compatible with ROS Noetic)
3. **Your robot system** running with standard topics

### Required ROS Topics:
- `/scan` - LiDAR data (sensor_msgs/LaserScan)
- `/odom` - Robot odometry (nav_msgs/Odometry)
- `/cmd_vel` - Velocity commands (geometry_msgs/Twist)
- `/move_base_simple/goal` - Navigation goals (geometry_msgs/PoseStamped)
- `/amcl_pose` - Robot pose (geometry_msgs/PoseWithCovarianceStamped)
- `/map` - SLAM map (nav_msgs/OccupancyGrid) [optional]

## 🚀 Quick Start

### 1. Start Your ROS Noetic System
```bash
# Terminal 1: Start ROS master
roscore

# Terminal 2: Start your robot drivers
roslaunch your_robot_package robot.launch

# Terminal 3: Start navigation (if using)
roslaunch your_robot_package navigation.launch
```

### 2. Start the ROS Noetic Backend
```bash
# In the project root directory
./start_backend_noetic.sh
```

### 3. Verify Backend is Working
```bash
# Test the backend
python3 test_backend_noetic.py

# Check health
curl http://localhost:8000/health

# Check ROS connection
curl http://localhost:8000/api/status
```

### 4. Access Web Interface
Open your browser to: **http://localhost:8000**

## 📡 Data Flow

```
Your ROS Noetic Robot ←→ Backend (ROS Noetic) ←→ Web Interface
```

**From Robot to Web:**
- LiDAR scans → Real-time visualization
- Odometry → Robot position tracking  
- Map data → Map display
- Battery status → Status monitoring

**From Web to Robot:**
- Movement commands → `/cmd_vel`
- Navigation goals → `/move_base_simple/goal`
- Initial pose → `/initialpose`

## 🔧 Configuration

### Backend Configuration
The backend automatically detects and connects to:
- ROS master at `$ROS_MASTER_URI` (default: http://localhost:11311)
- Standard ROS topics (configurable in `ros_interface_noetic.py`)

### Custom Topic Names
If your robot uses different topic names, edit `web_interface/backend/ros_bridge/ros_interface_noetic.py`:

```python
# Change these lines to match your robot
self.scan_sub = rospy.Subscriber('/your_scan_topic', LaserScan, ...)
self.odom_sub = rospy.Subscriber('/your_odom_topic', Odometry, ...)
self.cmd_vel_pub = rospy.Publisher('/your_cmd_vel_topic', Twist, ...)
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:8000/health
```

Response:
```json
{
  "status": "healthy",
  "ros_bridge": "connected",
  "ros_distro": "noetic",
  "timestamp": **********.123
}
```

### ROS Connection Status
```bash
curl http://localhost:8000/api/status
```

Response:
```json
{
  "status": "connected",
  "ros_distro": "noetic",
  "data_available": {
    "pose": true,
    "odom": true,
    "scan": true,
    "battery": false,
    "map": true
  },
  "timestamp": **********.123
}
```

### Real-time Data
Connect to WebSocket: `ws://localhost:8000/ws`

## 🔍 Troubleshooting

### Backend Won't Start

1. **Check ROS Noetic environment**:
   ```bash
   source /opt/ros/noetic/setup.bash
   python3 -c "import rospy; print('ROS OK')"
   ```

2. **Check Python dependencies**:
   ```bash
   pip3 install fastapi uvicorn websockets pydantic psutil
   ```

3. **Check port availability**:
   ```bash
   lsof -i :8000  # Should be empty
   ```

### ROS Connection Issues

1. **Check ROS master**:
   ```bash
   rostopic list  # Should show topics
   ```

2. **Check required topics**:
   ```bash
   rostopic echo /scan | head -5
   rostopic echo /odom | head -5
   ```

3. **Check ROS environment**:
   ```bash
   echo $ROS_MASTER_URI  # Should be set
   ```

### Web Interface Shows "Disconnected"

1. **Check backend health**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Check browser console** for WebSocket errors

3. **Check backend logs** for connection issues

### Commands Not Reaching Robot

1. **Verify cmd_vel topic**:
   ```bash
   rostopic echo /cmd_vel
   ```

2. **Check robot driver** is subscribing to `/cmd_vel`

3. **Test manual command**:
   ```bash
   rostopic pub /cmd_vel geometry_msgs/Twist "linear: {x: 0.1}" --once
   ```

## 🆚 Comparison with Bridge Setup

| Feature | ROS Noetic Backend | Bridge Setup |
|---------|-------------------|--------------|
| **Setup Complexity** | Simple (1 script) | Complex (2 systems) |
| **Performance** | Direct connection | Network overhead |
| **Maintenance** | Single system | Two systems to maintain |
| **Flexibility** | ROS Noetic only | Any ROS system |
| **Use Case** | Dedicated ROS Noetic | Mixed environments |

## 📁 File Structure

```
web_interface/backend/
├── app/
│   ├── main_noetic.py              # ROS Noetic backend main
│   └── main.py                     # Original ROS2 backend
├── ros_bridge/
│   ├── ros_interface_noetic.py     # ROS Noetic bridge
│   └── ros_interface.py            # Original ROS2 bridge
└── ...

start_backend_noetic.sh             # ROS Noetic backend startup
test_backend_noetic.py              # Backend test script
```

## 🎯 Next Steps

1. **Start your ROS Noetic system**
2. **Run the backend**: `./start_backend_noetic.sh`
3. **Test functionality**: `python3 test_backend_noetic.py`
4. **Access web interface**: http://localhost:8000
5. **Control your robot** through the web interface!

## 🔄 Switching Between Backends

- **For ROS2**: Use `./start_backend.sh` (original)
- **For ROS Noetic**: Use `./start_backend_noetic.sh` (this version)

Both backends provide the same web interface - just choose the one that matches your ROS system!
