#!/bin/bash

# =============================================================================
# ROS NOETIC TO BACKEND BRIDGE STARTUP SCRIPT
# =============================================================================
# Run this script on your ROS Noetic machine to bridge to the web backend
# Make sure your ROS Noetic system is already running before starting this bridge
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    if [ "$CLEANUP_DONE" = "true" ]; then
        return
    fi
    CLEANUP_DONE=true

    print_header "SHUTTING DOWN BRIDGE"
    print_status "Stopping bridge processes..."
    
    # Kill bridge process
    if [ ! -z "$BRIDGE_PID" ]; then
        kill $BRIDGE_PID 2>/dev/null || true
    fi
    
    print_success "Bridge stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM EXIT

# Parse command line arguments
BACKEND_HOST="localhost"
BACKEND_PORT="8000"
CONFIG_FILE="bridge_config.yaml"

while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            BACKEND_HOST="$2"
            shift 2
            ;;
        --port)
            BACKEND_PORT="$2"
            shift 2
            ;;
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --host HOST     Backend server IP address (default: localhost)"
            echo "  --port PORT     Backend server port (default: 8000)"
            echo "  --config FILE   Configuration file (default: bridge_config.yaml)"
            echo "  --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Connect to localhost:8000"
            echo "  $0 --host *************              # Connect to remote backend"
            echo "  $0 --host ************* --port 3000  # Custom host and port"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "ROS NOETIC TO BACKEND BRIDGE"
print_status "Connecting ROS Noetic system to web backend"

# Environment checks
print_header "ENVIRONMENT CHECKS"

# Check if ROS Noetic is available
if [ ! -f "/opt/ros/noetic/setup.bash" ]; then
    print_error "ROS Noetic not found! Please install ROS Noetic first."
    exit 1
fi

print_status "Sourcing ROS Noetic environment..."
source /opt/ros/noetic/setup.bash

# Check if roscore is running
if ! pgrep -f roscore > /dev/null; then
    print_error "ROS master (roscore) is not running!"
    print_status "Please start your ROS Noetic system first:"
    echo "  1. Start roscore: roscore"
    echo "  2. Start your robot drivers"
    echo "  3. Start SLAM/navigation if needed"
    echo "  4. Then run this bridge"
    exit 1
fi

print_success "ROS master detected"

# Check if required topics exist
print_status "Checking ROS topics..."

REQUIRED_TOPICS=("/scan" "/odom" "/cmd_vel")
MISSING_TOPICS=()

for topic in "${REQUIRED_TOPICS[@]}"; do
    if ! rostopic list | grep -q "^${topic}$"; then
        MISSING_TOPICS+=("$topic")
    fi
done

if [ ${#MISSING_TOPICS[@]} -gt 0 ]; then
    print_warning "Some required topics are missing:"
    for topic in "${MISSING_TOPICS[@]}"; do
        echo "  ❌ $topic"
    done
    print_warning "Bridge will start but some features may not work until these topics are available"
else
    print_success "All required topics found"
fi

# Check if Python dependencies are available
print_status "Checking Python dependencies..."

PYTHON_DEPS=("rospy" "websocket" "requests")
MISSING_DEPS=()

for dep in "${PYTHON_DEPS[@]}"; do
    if ! python -c "import $dep" 2>/dev/null; then
        MISSING_DEPS+=("$dep")
    fi
done

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    print_error "Missing Python dependencies:"
    for dep in "${MISSING_DEPS[@]}"; do
        echo "  ❌ $dep"
    done
    print_status "Install missing dependencies:"
    echo "  pip install websocket-client requests"
    exit 1
fi

print_success "All Python dependencies found"

# Check backend connectivity
print_status "Testing backend connectivity..."

if curl -s --connect-timeout 5 "http://${BACKEND_HOST}:${BACKEND_PORT}" > /dev/null; then
    print_success "Backend server is reachable"
else
    print_warning "Cannot reach backend server at ${BACKEND_HOST}:${BACKEND_PORT}"
    print_warning "Bridge will start but may not connect until backend is available"
fi

print_success "Environment checks completed"

# Start the bridge
print_header "STARTING BRIDGE"

print_status "Configuration:"
echo "  🌐 Backend Host: ${BACKEND_HOST}"
echo "  🔌 Backend Port: ${BACKEND_PORT}"
echo "  📄 Config File: ${CONFIG_FILE}"
echo "  📡 Status Server: http://localhost:9090/status"

print_status "Starting ROS Noetic to Backend Bridge..."

# Start bridge with parameters
python ros_noetic_bridge.py \
    _backend_host:=${BACKEND_HOST} \
    _backend_port:=${BACKEND_PORT} \
    _websocket_port:=${BACKEND_PORT} \
    _update_rate:=10 &

BRIDGE_PID=$!

# Wait a moment for bridge to start
sleep 3

# Check if bridge is running
if ps -p $BRIDGE_PID > /dev/null; then
    print_success "Bridge started successfully!"
    print_header "BRIDGE STATUS"
    print_status "Bridge is running with PID: $BRIDGE_PID"
    print_status "Monitoring ROS topics:"
    echo "  📡 /scan (LiDAR data)"
    echo "  📍 /odom_from_laser (Robot position from laser odometry)"
    echo "  🗺️  /map (SLAM map)"
    print_status "Publishing commands to:"
    echo "  🎮 /cmd_vel (Movement commands)"
    echo "  🎯 /move_base_simple/goal (Navigation goals)"
    print_status "Bridge endpoints:"
    echo "  🌐 Backend: http://${BACKEND_HOST}:${BACKEND_PORT}"
    echo "  📊 Status: http://localhost:9090/status"
    echo ""
    print_status "Bridge is ready! Your ROS Noetic robot is now connected to the web interface."
    print_status "Press Ctrl+C to stop the bridge"
    
    # Wait for bridge process
    wait $BRIDGE_PID
else
    print_error "Failed to start bridge!"
    exit 1
fi
