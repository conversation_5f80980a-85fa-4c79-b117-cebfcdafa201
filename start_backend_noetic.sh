#!/bin/bash

# =============================================================================
# BACKEND + ROS BRIDGE STARTUP SCRIPT - ROS NOETIC VERSION
# =============================================================================
# Starts both the web interface backend and ROS Noetic bridge
# This script runs both services on the same machine for integrated operation
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    if [ "$CLEANUP_DONE" = "true" ]; then
        return
    fi
    CLEANUP_DONE=true

    print_header "SHUTTING DOWN SERVICES"

    # Kill ROS bridge process
    if [ ! -z "$BRIDGE_PID" ]; then
        print_status "Stopping ROS bridge..."
        kill $BRIDGE_PID 2>/dev/null || true
        wait $BRIDGE_PID 2>/dev/null || true
        print_success "ROS bridge stopped"
    fi

    # Kill backend process
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend server..."
        kill $BACKEND_PID 2>/dev/null || true
        wait $BACKEND_PID 2>/dev/null || true
        print_success "Backend stopped"
    fi

    print_success "All services stopped"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

print_header "BACKEND + ROS BRIDGE STARTUP - ROS NOETIC"

# Check if we're in the right directory
if [ ! -f "web_interface/backend/app/main_noetic.py" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: web_interface/backend/app/main_noetic.py"
    exit 1
fi

if [ ! -f "ros_noetic_bridge.py" ]; then
    print_error "ROS bridge script not found!"
    print_error "Expected to find: ros_noetic_bridge.py"
    exit 1
fi

# Environment checks
print_header "ENVIRONMENT CHECKS"

# Check if ROS Noetic is available
print_status "Checking ROS Noetic environment..."
if [ ! -f "/opt/ros/noetic/setup.bash" ]; then
    print_error "ROS Noetic not found!"
    print_error "Please install ROS Noetic first:"
    print_error "  http://wiki.ros.org/noetic/Installation"
    exit 1
fi

print_status "Sourcing ROS Noetic environment..."
source /opt/ros/noetic/setup.bash

# Check if roscore is running
if ! pgrep -f roscore > /dev/null; then
    print_warning "ROS master (roscore) is not running!"
    print_warning "Backend will start but ROS features will be limited"
    print_warning "To enable full functionality:"
    echo "  1. Start roscore: roscore"
    echo "  2. Start your robot drivers"
    echo "  3. Restart this backend"
else
    print_success "ROS master detected"
fi

# Check Python dependencies
print_status "Checking Python dependencies..."

PYTHON_DEPS=("fastapi" "uvicorn" "websockets" "pydantic" "psutil" "requests")
ROS_PYTHON_DEPS=("rospy" "std_msgs" "sensor_msgs" "nav_msgs" "geometry_msgs" "tf")
MISSING_DEPS=()

for dep in "${PYTHON_DEPS[@]}"; do
    if ! python3 -c "import $dep" 2>/dev/null; then
        MISSING_DEPS+=("$dep")
    fi
done

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    print_warning "Missing Python dependencies:"
    for dep in "${MISSING_DEPS[@]}"; do
        echo "  ❌ $dep"
    done
    print_status "Installing missing dependencies..."
    
    # Check if requirements.txt exists
    if [ -f "web_interface/backend/requirements.txt" ]; then
        pip3 install -r web_interface/backend/requirements.txt
    else
        pip3 install fastapi uvicorn websockets pydantic psutil requests
    fi

    print_success "Dependencies installed"
else
    print_success "All Python dependencies found"
fi

# Check ROS Python bindings
print_status "Checking ROS Python bindings..."
for dep in "${ROS_PYTHON_DEPS[@]}"; do
    if ! python3 -c "import $dep" 2>/dev/null; then
        print_error "ROS Python binding '$dep' not found!"
        print_error "Please ensure ROS Noetic is properly installed and sourced"
        exit 1
    fi
done
print_success "All ROS Noetic Python bindings found"

# Check if port 8000 is available
print_status "Checking port availability..."
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null; then
    print_warning "Port 8000 is already in use"
    print_status "Attempting to free port 8000..."
    
    # Kill processes using port 8000
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    sleep 2
    
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null; then
        print_error "Could not free port 8000"
        print_error "Please manually stop the process using port 8000"
        exit 1
    else
        print_success "Port 8000 freed"
    fi
else
    print_success "Port 8000 is available"
fi

print_success "Environment checks completed"

# Start the services
print_header "STARTING SERVICES"

print_status "Configuration:"
echo "  🌐 Backend Host: 0.0.0.0"
echo "  🔌 Backend Port: 8000"
echo "  🤖 ROS Distribution: Noetic"
echo "  📡 API Documentation: http://localhost:8000/docs"
echo "  🔗 WebSocket: ws://localhost:8000/ws"
echo "  ❤️  Health Check: http://localhost:8000/health"
echo "  🌉 ROS Bridge: Integrated"

print_status "Starting backend server..."

# Start the backend with minimal configuration
# Ensure ROS environment is available to the backend process
export ROS_PACKAGE_PATH=$ROS_PACKAGE_PATH:$(pwd)
export PYTHONPATH=web_interface/backend:$PYTHONPATH
source /opt/ros/noetic/setup.bash
source ../devel/setup.bash
python3 -m uvicorn web_interface.backend.app.main_noetic:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 5

# Check if backend is running
if ps -p $BACKEND_PID > /dev/null; then
    print_success "Backend server started successfully!"

    print_status "Starting ROS bridge..."

    # Start ROS bridge with ROS environment
    source /opt/ros/noetic/setup.bash
    source ../devel/setup.bash
    python3 ros_noetic_bridge.py &
    BRIDGE_PID=$!

    # Wait a moment for bridge to start
    sleep 3

    # Check if bridge is running
    if ps -p $BRIDGE_PID > /dev/null; then
        print_success "ROS bridge started successfully!"

        print_header "SERVICES STATUS"
        print_status "Backend is running with PID: $BACKEND_PID"
        print_status "ROS Bridge is running with PID: $BRIDGE_PID"

        print_status "Server endpoints:"
        echo "  🌐 Web Interface: http://localhost:8000"
        echo "  📚 API Docs: http://localhost:8000/docs"
        echo "  🔗 WebSocket: ws://localhost:8000/ws"
        echo "  ❤️  Health: http://localhost:8000/health"
        echo "  📊 Status: http://localhost:8000/api/status"
        echo "  🌉 Bridge Status: http://localhost:9090/status"

        print_status "ROS Topics Integration:"
        echo "  📡 Subscribes to:"
        echo "    - /battery (Float32) - Battery level"
        echo "    - /initialpose (PoseWithCovarianceStamped) - Robot position"
        echo "    - /odom_fake (Odometry) - Velocity data"
        echo "    - /scan_forward (LaserScan) - LiDAR data"
        echo "    - /odom_from_laser (Odometry) - General odometry"
        echo "    - /map (OccupancyGrid) - Map data"
        echo "  🎮 Publishes to:"
        echo "    - /cmd_vel (Twist) - Robot movement commands"
        echo "    - /move_base/current_goal (PoseStamped) - Navigation goals"
        echo "  🔄 Real-time data via WebSocket"

        print_success "All services are ready!"
        print_status "Connect your frontend to http://localhost:8000"
        print_status "Press Ctrl+C to stop all services"

        # Wait for both processes
        wait $BACKEND_PID $BRIDGE_PID
    else
        print_error "Failed to start ROS bridge!"
        # Kill backend if bridge failed
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
else
    print_error "Failed to start backend server!"
    exit 1
fi
