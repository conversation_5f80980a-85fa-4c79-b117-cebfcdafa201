#!/bin/bash

# Phase 2: Advanced Features Launch Script
# Enhanced SLAM + Advanced Path Planning

echo "🚀 Starting Phase 2: Advanced Features System"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "src/indoor_nav_bringup/package.xml" ]; then
    print_error "Please run this script from the workspace root directory"
    exit 1
fi

# Deactivate conda to avoid Python conflicts
print_status "Deactivating conda environment..."
conda deactivate 2>/dev/null || true

# Source ROS2 environment
print_status "Sourcing ROS2 environment..."
source /opt/ros/humble/setup.bash

# Build the workspace if needed
if [ ! -d "install" ] || [ "$1" = "--build" ]; then
    print_status "Building workspace..."
    colcon build --symlink-install --allow-overriding mapping path_planning
    
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
fi

# Source the workspace
print_status "Sourcing workspace..."
source install/setup.bash

print_success "Environment ready"

# Set parameters
export USE_SIM_TIME=true
export ROS_DOMAIN_ID=0

print_status "Configuration:"
echo "  🗺️  Enhanced SLAM with real-time monitoring"
echo "  🧠  Advanced path planning with multiple algorithms"
echo "  📊  System performance monitoring"
echo "  🔄  Dynamic replanning capabilities"
echo ""

echo -e "${PURPLE}🎯 Phase 2 Features:${NC}"
echo "  🗺️  Real-time SLAM with quality monitoring"
echo "  🧠  Advanced path planning (A*, Dijkstra, RRT, RRT*)"
echo "  🎯  Navigation controller (robot movement execution)"
echo "  📊  System performance monitoring"
echo "  🔄  Dynamic replanning capabilities"
echo "  📈  Enhanced visualization"
echo "  🏠  Web interface 'Go Home' button support"
echo ""

# Function to cleanup on exit
cleanup() {
    print_status "Cleaning up processes..."
    pkill -f "gazebo"
    pkill -f "rviz"
    pkill -f "ros2"
    pkill -f "slam_toolbox"
    pkill -f "python3.*slam"
    pkill -f "python3.*planner"
    pkill -f "python3.*monitor"
    pkill -f "test_navigation.py"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Launch the base system first
print_status "Launching base system..."
ros2 launch indoor_nav_bringup gazebo_simulation.launch.py &
BASE_PID=$!

# Wait for base system to start
sleep 10

# Launch SLAM toolbox first (generates the map)
print_status "Launching SLAM toolbox..."
ros2 run slam_toolbox async_slam_toolbox_node --ros-args --params-file src/perception/mapping/config/slam_params.yaml --remap /map:=/map_slam &
SLAM_TOOLBOX_PID=$!

# Wait for SLAM to start
sleep 5

# Launch enhanced SLAM manager (processes the map)
print_status "Launching enhanced SLAM manager..."
unset CONDA_DEFAULT_ENV && unset CONDA_EXE && unset CONDA_PREFIX && unset CONDA_PROMPT_MODIFIER && unset CONDA_PYTHON_EXE && unset CONDA_SHLVL && export PATH="/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" && /usr/bin/python3 src/perception/mapping/mapping/realtime_slam_manager.py &
SLAM_MANAGER_PID=$!

# Wait a bit
sleep 3

# Launch advanced path planner
print_status "Launching advanced path planner..."
unset CONDA_DEFAULT_ENV && unset CONDA_EXE && unset CONDA_PREFIX && unset CONDA_PROMPT_MODIFIER && unset CONDA_PYTHON_EXE && unset CONDA_SHLVL && export PATH="/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" && /usr/bin/python3 src/navigation/path_planning/path_planning/advanced_global_planner.py &
PLANNER_PID=$!

# Wait a bit
sleep 3

# Launch system monitor
print_status "Launching system monitor..."
unset CONDA_DEFAULT_ENV && unset CONDA_EXE && unset CONDA_PREFIX && unset CONDA_PROMPT_MODIFIER && unset CONDA_PYTHON_EXE && unset CONDA_SHLVL && export PATH="/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" && /usr/bin/python3 src/indoor_nav_bringup/scripts/advanced_system_monitor.py &
MONITOR_PID=$!

# Wait a bit
sleep 3

# Launch navigation controller
print_status "Launching navigation controller..."
unset CONDA_DEFAULT_ENV && unset CONDA_EXE && unset CONDA_PREFIX && unset CONDA_PROMPT_MODIFIER && unset CONDA_PYTHON_EXE && unset CONDA_SHLVL && export PATH="/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" && /usr/bin/python3 test_navigation.py &
NAVIGATION_PID=$!

print_success "Phase 2 Advanced Features System launched successfully!"
print_status "System is running with PIDs:"
echo "  🏠 Base System: $BASE_PID"
echo "  🗺️ SLAM Toolbox: $SLAM_TOOLBOX_PID"
echo "  🗺️ SLAM Manager: $SLAM_MANAGER_PID"
echo "  🧠 Path Planner: $PLANNER_PID"
echo "  📊 System Monitor: $MONITOR_PID"
echo "  🎯 Navigation Controller: $NAVIGATION_PID"
echo ""

print_status "🎮 Control the robot:"
echo "  🏠 Go Home: Click 'Go Home (0,0)' in web interface"
echo "  📍 Set goal: Use RViz '2D Nav Goal' tool or web interface"
echo "  🎯 Manual goal: ros2 topic pub --once /move_base_simple/goal geometry_msgs/msg/PoseStamped ..."
echo "  🔄 Replan: ros2 service call /planner/replan std_srvs/srv/Empty"
echo "  🔧 Change algorithm: ros2 service call /planner/change_algorithm std_srvs/srv/Empty"
echo "  💾 Save map: ros2 service call /slam/save_map std_srvs/srv/Empty"
echo ""

print_status "📊 Monitor system:"
echo "  🗺️ SLAM status: ros2 topic echo /slam/status"
echo "  🧠 Planner stats: ros2 topic echo /planner/statistics"
echo "  🏥 System health: ros2 topic echo /system/health"
echo ""

print_warning "Press Ctrl+C to stop all processes"

# Wait for all background processes
wait
