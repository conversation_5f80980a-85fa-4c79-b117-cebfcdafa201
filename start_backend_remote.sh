#!/bin/bash

# =============================================================================
# REMOTE BACKEND SERVER STARTUP SCRIPT
# =============================================================================
# Starts the web interface backend for remote deployment
# Use this when ROS runs on a different computer than the web interface
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    if [ "$CLEANUP_DONE" = "true" ]; then
        return
    fi
    CLEANUP_DONE=true

    print_header "SHUTTING DOWN BACKEND"
    print_status "Stopping backend server..."
    
    # Kill backend process
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        wait $BACKEND_PID 2>/dev/null || true
    fi
    
    print_success "Backend stopped"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Parse command line arguments
BACKEND_HOST="0.0.0.0"
BACKEND_PORT="8000"

while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            BACKEND_HOST="$2"
            shift 2
            ;;
        --port)
            BACKEND_PORT="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --host HOST     Backend bind address (default: 0.0.0.0)"
            echo "  --port PORT     Backend port (default: 8000)"
            echo "  --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                        # Start on all interfaces, port 8000"
            echo "  $0 --port 3000           # Start on port 3000"
            echo "  $0 --host *************  # Bind to specific IP"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "REMOTE BACKEND SERVER STARTUP"
print_status "Starting web interface backend for remote deployment"

# Check if we're in the right directory
if [ ! -f "web_interface/backend/app/main.py" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: web_interface/backend/app/main.py"
    exit 1
fi

# Environment checks (no ROS checks for remote deployment)
print_header "ENVIRONMENT CHECKS"

print_status "Checking Python environment..."

# Check Python version
PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
print_status "Python version: $PYTHON_VERSION"

# Simple version check without bc dependency
PYTHON_MAJOR=$(python3 -c "import sys; print(sys.version_info.major)")
PYTHON_MINOR=$(python3 -c "import sys; print(sys.version_info.minor)")

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 7 ]); then
    print_error "Python 3.7 or higher required, found $PYTHON_VERSION"
    exit 1
fi

# Check Python dependencies
print_status "Checking Python dependencies..."

PYTHON_DEPS=("fastapi" "uvicorn" "websockets" "pydantic" "psutil")
MISSING_DEPS=()

for dep in "${PYTHON_DEPS[@]}"; do
    if ! python3 -c "import $dep" 2>/dev/null; then
        MISSING_DEPS+=("$dep")
    fi
done

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    print_warning "Missing Python dependencies:"
    for dep in "${MISSING_DEPS[@]}"; do
        echo "  ❌ $dep"
    done
    print_status "Installing missing dependencies..."
    
    # Check if requirements.txt exists
    if [ -f "web_interface/backend/requirements.txt" ]; then
        pip3 install -r web_interface/backend/requirements.txt
    else
        pip3 install fastapi uvicorn websockets pydantic psutil
    fi
    
    print_success "Dependencies installed"
else
    print_success "All Python dependencies found"
fi

# Check if port is available
print_status "Checking port availability..."
if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Port $BACKEND_PORT is already in use"
    print_status "Attempting to free port $BACKEND_PORT..."
    
    # Kill processes using the port
    lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    sleep 2
    
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_error "Could not free port $BACKEND_PORT"
        print_error "Please manually stop the process using port $BACKEND_PORT"
        exit 1
    else
        print_success "Port $BACKEND_PORT freed"
    fi
else
    print_success "Port $BACKEND_PORT is available"
fi

print_success "Environment checks completed"

# Start the backend
print_header "STARTING REMOTE BACKEND SERVER"

print_status "Configuration:"
echo "  🌐 Host: $BACKEND_HOST"
echo "  🔌 Port: $BACKEND_PORT"
echo "  🤖 ROS Mode: Remote (no local ROS required)"
echo "  📡 API Documentation: http://localhost:$BACKEND_PORT/docs"
echo "  🔗 WebSocket: ws://localhost:$BACKEND_PORT/ws"
echo "  ❤️  Health Check: http://localhost:$BACKEND_PORT/health"

print_status "Starting backend server..."

# Change to backend directory
cd web_interface/backend/app

# Set environment variable to indicate remote mode
export REMOTE_MODE=true

# Start the minimal backend (no ROS dependencies)
python3 -m uvicorn main_minimal:app \
    --host "$BACKEND_HOST" \
    --port "$BACKEND_PORT" \
    --reload \
    --log-level info &

BACKEND_PID=$!

# Wait a moment for server to start
sleep 3

# Check if backend is running
if ps -p $BACKEND_PID > /dev/null; then
    print_success "Backend server started successfully!"
    print_header "SERVER STATUS"
    print_status "Backend is running with PID: $BACKEND_PID"
    print_status "Server endpoints:"
    echo "  🌐 Web Interface: http://localhost:$BACKEND_PORT"
    echo "  📚 API Docs: http://localhost:$BACKEND_PORT/docs"
    echo "  🔗 WebSocket: ws://localhost:$BACKEND_PORT/ws"
    echo "  ❤️  Health: http://localhost:$BACKEND_PORT/health"
    echo "  📊 Status: http://localhost:$BACKEND_PORT/api/status"
    
    print_status "Remote mode features:"
    echo "  🔄 Receives data from ROS bridge on remote computer"
    echo "  📡 WebSocket communication for real-time updates"
    echo "  🎮 Sends commands to remote ROS system via bridge"
    
    print_warning "Important notes for remote deployment:"
    echo "  1. Start your ROS system on the remote computer"
    echo "  2. Start the ROS bridge on the remote computer:"
    echo "     ./start_bridge.sh --host $(hostname -I | awk '{print $1}')"
    echo "  3. Configure bridge to connect to this backend"
    echo "  4. Ensure firewall allows connections on port $BACKEND_PORT"
    
    print_status "Backend is ready! Connect your ROS bridge to this server."
    print_status "Press Ctrl+C to stop the server"
    
    # Wait for backend process
    wait $BACKEND_PID
else
    print_error "Failed to start backend server!"
    exit 1
fi
