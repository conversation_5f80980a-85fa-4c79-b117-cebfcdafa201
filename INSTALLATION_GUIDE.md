# Indoor Autonomous Vehicle System - Installation Guide

## 🎯 Overview
This guide provides step-by-step instructions to install and run the complete indoor autonomous vehicle system with **production-ready obstacle avoidance**, ROS2, Gazebo simulation, SLAM mapping, and web interfaces.

## 🖥️ System Requirements
- **OS**: Ubuntu 22.04 LTS
- **RAM**: Minimum 8GB (16GB recommended)
- **Storage**: At least 15GB free space
- **GPU**: Optional but recommended for Gazebo simulation
- **Network**: Internet connection for package installation

## 📦 Prerequisites Installation

### 1. Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### 2. Install ROS2 Humble
```bash
# Add ROS2 repository
sudo apt install software-properties-common curl -y
curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null

# Install ROS2 Humble Desktop
sudo apt update
sudo apt install ros-humble-desktop -y

# Install essential ROS2 packages for navigation
sudo apt install ros-humble-gazebo-ros-pkgs ros-humble-slam-toolbox ros-humble-nav2-map-server ros-humble-joint-state-publisher ros-humble-joint-state-publisher-gui ros-humble-robot-state-publisher ros-humble-urdf ros-humble-xacro -y

# Install colcon build tools
sudo apt install python3-colcon-common-extensions -y
```

### 3. Install Node.js and npm
```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should be v18.x or higher
npm --version   # Should be 8.x or higher
```

### 4. Install Python Dependencies
```bash
# Install Python packages for web interface
python3 -m pip install --user fastapi uvicorn websockets flask flask-socketio numpy lxml
```

## 🔧 Environment Setup

### 1. Source ROS2 Environment
Add ROS2 to your shell environment:
```bash
echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### 2. Handle Conda Conflicts (IMPORTANT!)
If you have Conda/Anaconda installed, it can conflict with ROS2 Python packages. The system automatically handles this, but you can manually deactivate if needed:
```bash
# The start_system.sh script automatically handles conda deactivation
# Manual deactivation (if needed):
conda deactivate
```

### 3. Set Environment Variables
```bash
# Add to ~/.bashrc for persistent settings
echo "export ROS_DOMAIN_ID=0" >> ~/.bashrc
echo "export GAZEBO_MODEL_PATH=\$GAZEBO_MODEL_PATH:\$(pwd)/models" >> ~/.bashrc
source ~/.bashrc
```

## 🚀 Project Setup

### 1. Clone and Build the Project
```bash
# Navigate to your workspace directory
cd /path/to/your/workspace  # e.g., cd /home/<USER>/Downloads/indoor_autonomous_vehicle

# IMPORTANT: Create missing package.xml files (if not present)
# These files are essential for ROS2 packages but might be missing after git clone
if [ ! -f "src/perception/mapping/package.xml" ]; then
    echo "Creating missing package.xml for mapping package..."
    cat > src/perception/mapping/package.xml << 'EOF'
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>mapping</name>
  <version>1.0.0</version>
  <description>Mapping and SLAM for indoor autonomous vehicle</description>
  <maintainer email="<EMAIL>">Indoor Vehicle Team</maintainer>
  <license>MIT</license>
  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>slam_toolbox</depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>
  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
EOF
fi

if [ ! -f "src/perception/sensor_fusion/package.xml" ]; then
    echo "Creating missing package.xml for sensor_fusion package..."
    cat > src/perception/sensor_fusion/package.xml << 'EOF'
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>sensor_fusion</name>
  <version>1.0.0</version>
  <description>Sensor fusion for indoor autonomous vehicle</description>
  <maintainer email="<EMAIL>">Indoor Vehicle Team</maintainer>
  <license>MIT</license>
  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>tf2_ros</depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>
  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
EOF
fi

# Create maps directory if missing
mkdir -p src/indoor_nav_bringup/maps
if [ ! -f "src/indoor_nav_bringup/maps/indoor_house.yaml" ]; then
    echo "Creating basic map configuration..."
    cat > src/indoor_nav_bringup/maps/indoor_house.yaml << 'EOF'
image: indoor_house.pgm
resolution: 0.050000
origin: [-10.000000, -10.000000, 0.000000]
negate: 0
occupied_thresh: 0.65
free_thresh: 0.196
EOF
    echo "# Maps Directory - Generated maps will be saved here" > src/indoor_nav_bringup/maps/README.md
fi

# Build ALL ROS2 packages in correct dependency order
source /opt/ros/humble/setup.bash

# First build the dependency packages
echo "Building dependency packages..."
colcon build --packages-select lidar_node ultrasonic_array sensor_fusion mapping web_interface indoor_navigation

# Then build the main bringup package
echo "Building main bringup package..."
colcon build --packages-select indoor_nav_bringup

# Source the workspace
source install/setup.bash
```

### 2. Install Frontend Dependencies
```bash
cd web_interface/frontend
npm install
# Note: npm run build is not needed for development
```

## 🎮 Running the System

### ⭐ ONE-COMMAND STARTUP (Recommended)
```bash
# From workspace root - THIS IS THE ONLY COMMAND YOU NEED!
./start_system.sh

# For real robot (when you have real hardware):
./start_system.sh --real-robot

# For help:
./start_system.sh --help
```

**That's it! The script automatically starts:**
- ✅ Web frontend (http://localhost:3000)
- ✅ Web backend API (http://localhost:8000)
- ✅ Gazebo simulation with indoor house
- ✅ Robot with LiDAR sensor
- ✅ SLAM real-time mapping
- ✅ **Production-ready obstacle avoidance**
- ✅ Goal-based navigation

### 🛑 Stopping the System
```bash
# Press Ctrl+C in the terminal running start_system.sh
# The script will automatically clean up all processes
```

### 🔧 Troubleshooting Commands
```bash
# When system gets stuck or has issues
./fix_system.sh --kill-all      # Kill all processes
./fix_system.sh --check-system  # Diagnose problems
./fix_system.sh --help          # See all options

# Quick reference for all commands
cat QUICK_COMMANDS.md
```

## 🏗️ System Components

### 🌐 Web Interfaces
- **Main Frontend**: http://localhost:3000 (React-based UI with map interface)
- **Backend API**: http://localhost:8000 (FastAPI with WebSocket for real-time communication)

### 🤖 ROS2 Components
- **Gazebo Simulation**: 3D indoor house environment with realistic physics
- **SLAM Toolbox**: Real-time mapping and localization
- **Obstacle Avoidance**: Production-ready navigation with LiDAR-based obstacle detection
- **Robot Model**: Differential drive robot with LiDAR sensor
- **Map Server**: Serves pre-built and real-time generated maps

## 🎮 How to Use the System

### 1. Start the System
```bash
./start_system.sh
```

### 2. Open Web Interface
- Open browser: http://localhost:3000
- You'll see the indoor house map and robot position

### 3. Navigate the Robot
- **Click anywhere on the map** to set a navigation goal
- Robot will automatically navigate with obstacle avoidance
- Watch real-time SLAM mapping in action

### 4. Monitor System
- **Robot position**: Real-time updates on map
- **LiDAR data**: Visualized as scan points
- **Obstacles**: Automatically detected and avoided
- **Goal status**: Shows when robot reaches destination

## 🔧 Troubleshooting

### ⚠️ Common Issues

#### 1. "start_system.sh: Permission denied"
```bash
chmod +x start_system.sh
```

#### 2. Missing Files After Git Clone
If you get errors about missing `package.xml` files or `maps` directory after cloning from git:
```bash
# Run the setup commands from step 1 above, or use the fix script:
./fix_system.sh --setup-missing-files

# Or manually create missing files (see step 1 in Project Setup section)
```

#### 3. ROS2 Package Not Found
```bash
# Reinstall missing packages
sudo apt install ros-humble-slam-toolbox ros-humble-nav2-map-server
```

#### 4. Python/Conda Conflicts
```bash
# The script handles this automatically, but if issues persist:
conda deactivate
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
```

#### 5. Frontend Won't Start
```bash
cd web_interface/frontend
rm -rf node_modules package-lock.json
npm install
```

#### 6. Gazebo Display Issues
```bash
# For headless systems or remote connections:
export DISPLAY=:0
# Or use VNC/X11 forwarding
```

#### 7. Build Errors After Git Clone
```bash
# If you get "Failed to find the following files" errors:
# This happens when essential files are missing from git repository

# Solution 1: Use the automated fix
./fix_system.sh --setup-missing-files

# Solution 2: Manual fix (run the commands from Project Setup step 1)
# Create missing package.xml files and maps directory as shown above
```

### ✅ Verification Commands

#### Check System Status
```bash
# After running start_system.sh, check if all components are running:
curl http://localhost:3000  # Frontend
curl http://localhost:8000/health  # Backend
ros2 topic list | grep -E "(scan|cmd_vel|odom)"  # ROS2 topics
```

#### Check ROS2 Installation
```bash
ros2 --help
ros2 pkg list | grep slam
ros2 pkg list | grep nav2
```

#### Monitor Robot Navigation
```bash
# In a separate terminal:
source /opt/ros/humble/setup.bash
source install/setup.bash

# Monitor LiDAR data
ros2 topic echo /scan

# Monitor robot movement
ros2 topic echo /cmd_vel

# Check robot position
ros2 topic echo /odom
```

## 🎯 Real Robot Deployment

### ⚠️ IMPORTANT: Real Robot Requirements
**The system CANNOT run on real robot without proper hardware setup!**

See **`REAL_ROBOT_SETUP.md`** for complete hardware requirements and configuration.

### For Real Hardware
When you're ready to deploy on actual robot hardware:

```bash
# Use real robot mode (requires hardware setup first!)
./start_system.sh --real-robot
```

**Critical Hardware Requirements:**
- **LiDAR sensor** with ROS2 driver publishing to `/scan` topic
- **Motor controllers** with ROS2 driver subscribing to `/cmd_vel` topic
- **Odometry system** with ROS2 driver publishing to `/odom` topic
- **Transform publisher** for `odom` → `base_link` transforms
- **Robot URDF** file matching your physical robot
- **Calibrated parameters** for wheel base, speeds, etc.

**📖 Read `REAL_ROBOT_SETUP.md` before attempting real robot deployment!**

### Manual Robot Control (for testing)
```bash
# Emergency stop
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{}'

# Move forward slowly
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{linear: {x: 0.1}}'

# Rotate in place
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{angular: {z: 0.3}}'

# Set navigation goal manually
ros2 topic pub --once /goal_pose geometry_msgs/msg/PoseStamped "{header: {frame_id: 'map'}, pose: {position: {x: 2.0, y: 1.0, z: 0.0}, orientation: {w: 1.0}}}"
```

## ⚡ Performance Tips

1. **Close unnecessary applications** to free up system resources
2. **Use SSD storage** for better I/O performance
3. **Increase swap space** if you have limited RAM (4GB recommended)
4. **Disable visual effects** in Ubuntu for better performance
5. **Use dedicated GPU** for Gazebo if available

## 🔧 Advanced Configuration

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Web Backend   │    │   ROS2 System   │
│  (React/TS)     │◄──►│  (FastAPI/WS)   │◄──►│ (Navigation)    │
│  Port: 3000     │    │  Port: 8000     │    │ Obstacle Avoid. │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │ Gazebo Simulator│
                                               │ (Indoor House)  │
                                               │ + Robot + LiDAR │
                                               └─────────────────┘
```

### Key ROS2 Topics
- `/scan` - LiDAR sensor data
- `/cmd_vel` - Robot velocity commands
- `/odom` - Robot odometry/position
- `/goal_pose` - Navigation goals from web interface
- `/map` - SLAM-generated map data

### Production Features
- ✅ **Real-time obstacle avoidance** with LiDAR
- ✅ **Precise navigation** (10cm goal tolerance)
- ✅ **SLAM mapping** with persistent maps
- ✅ **Web-based control** with real-time visualization
- ✅ **Hardware compatibility** for real robot deployment

## 📞 Support & Help

### 🆘 Getting Help
1. **Check terminal output** where `start_system.sh` is running
2. **Browser console**: Press F12 for web interface issues
3. **ROS2 diagnostics**: `ros2 doctor` for system health check
4. **System logs**: `journalctl -f` for system-level issues

### 📁 Log Locations
- **ROS2 logs**: `~/.ros/log/`
- **Gazebo logs**: `~/.gazebo/`
- **Web backend**: Terminal output from start_system.sh
- **Frontend logs**: Browser developer console (F12)

### 🔍 Quick Diagnostics
```bash
# Check if all components are running
ps aux | grep -E "(gazebo|ros2|npm|python3)"

# Check network ports
netstat -tulpn | grep -E "(3000|8000)"

# Check ROS2 system health
ros2 doctor

# Monitor system resources
htop
```

---

## 🎉 Summary

**This system provides a complete indoor autonomous vehicle solution with:**

✅ **One-command startup**: `./start_system.sh`
✅ **Production-ready obstacle avoidance** using LiDAR
✅ **Real-time SLAM mapping** and localization
✅ **Web-based control interface** with live visualization
✅ **Real robot compatibility** for hardware deployment
✅ **Automatic environment handling** (Conda conflicts resolved)

**Perfect for:**
- 🏠 Indoor navigation research
- 🤖 Robot prototyping and testing
- 🎓 Educational robotics projects
- 🏭 Real-world deployment preparation

**Ready to deploy on real robots with minimal configuration changes!**

---

*Last updated: 2025-06-24 - System tested and verified with production-ready obstacle avoidance*

Im trying to get map data on startup app FE, and when user switch to Map2D page, but it doesnt always work. the map under ros system is currently static, so I use useEffect to fetch api.
Note : 
- I'm currently working on 2 version of ros : humble and noetic
- this issue happens in noetic system
- so don't fix the ros2-humble system until I tell you.
- check your memory to get current running remote system.
- the Frontend is automatically reset , SO DON'T RUN FRONTEND IN YOUR TERMINAL
- the ros noetic is running by me, you can check topic to see them, of course they are all on remote system
- you should only run backend again when you finish fixing issue.
