#!/bin/bash

# =============================================================================
# SYSTEM CLEANUP SCRIPT
# =============================================================================
# Kills all ROS2, Gazebo, and web server processes
# Use this before starting the system to ensure clean startup
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header "SYSTEM CLEANUP"

# Kill Gazebo processes
print_info "Killing Gazebo processes..."
pkill -f gazebo
pkill -f gzserver
pkill -f gzclient
pkill -f gz

# Kill ROS2 processes (but not this script)
print_info "Killing ROS2 processes..."
pkill -f ros2 | grep -v $$ || true
pkill -f launch | grep -v $$ || true
pkill -f slam_toolbox || true
pkill -f robot_state_publisher || true
pkill -f joint_state_publisher || true
pkill -f simple_obstacle_avoidance || true
pkill -f navigation_manager || true
pkill -f map_server || true

# Kill web server processes
print_info "Killing web server processes..."
pkill -f "python.*main.py"
pkill -f "npm.*start"
pkill -f "react-scripts"
pkill -f uvicorn
pkill -f fastapi

# Kill any remaining Python processes related to our system
print_info "Killing remaining Python processes..."
pkill -f "python.*indoor"
pkill -f "python.*nav"
pkill -f "python.*robot"

# Wait for processes to terminate
print_info "Waiting for processes to terminate..."
sleep 3

# Check if any processes are still running
print_info "Checking for remaining processes..."
REMAINING_GAZEBO=$(pgrep -f gazebo | wc -l)
REMAINING_ROS2=$(pgrep -f ros2 | wc -l)
REMAINING_PYTHON=$(pgrep -f "python.*main.py" | wc -l)

if [ $REMAINING_GAZEBO -gt 0 ] || [ $REMAINING_ROS2 -gt 0 ] || [ $REMAINING_PYTHON -gt 0 ]; then
    print_warning "Some processes still running. Force killing..."
    pkill -9 -f gazebo
    pkill -9 -f ros2
    pkill -9 -f "python.*main.py"
    sleep 2
fi

# Clean up any leftover files
print_info "Cleaning up temporary files..."
rm -f /tmp/launch_params_*
rm -f /tmp/.X*-lock 2>/dev/null || true

print_success "System cleanup completed!"
print_info "You can now start the system with:"
print_info "  ./start_ros2.sh    (for ROS2/Gazebo)"
print_info "  ./start_backend.sh (for backend server)"
print_info "  ./start_frontend.sh (for frontend)"
