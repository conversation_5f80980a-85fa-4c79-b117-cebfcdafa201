#!/bin/bash

# =============================================================================
# SYSTEM TROUBLESHOOTING AND FIX SCRIPT
# =============================================================================
# This script provides common fixes for system issues
# Run this when the system has problems or gets stuck
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Troubleshooting options:"
    echo "  --kill-all          Kill all system processes (Gazebo, ROS2, web servers)"
    echo "  --kill-gazebo       Kill only Gazebo processes"
    echo "  --kill-ros2         Kill only ROS2 processes"
    echo "  --kill-web          Kill only web servers (frontend/backend)"
    echo "  --clean-build       Clean and rebuild ROS2 workspace"
    echo "  --fix-permissions   Fix file permissions"
    echo "  --check-system      Check system status and dependencies"
    echo "  --reset-gazebo      Reset Gazebo configuration"
    echo "  --fix-gazebo-models Fix missing Gazebo models"
    echo "  --fix-conda         Fix Conda/Python conflicts"
    echo "  --fix-npm           Fix npm/Node.js issues"
    echo "  --fix-ports         Kill processes using ports 3000/8000"
    echo "  --setup-missing-files Create missing package.xml files and directories"
    echo "  --reinstall-deps    Reinstall all dependencies"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --kill-all       # When system is completely stuck"
    echo "  $0 --kill-gazebo    # When multiple Gazebo instances running"
    echo "  $0 --check-system   # To diagnose issues"
    echo "  $0 --fix-ports      # When ports 3000/8000 are busy"
}

# Kill all system processes
kill_all_processes() {
    print_header "KILLING ALL SYSTEM PROCESSES"
    
    print_status "Killing Gazebo processes..."
    killall -9 gazebo gzserver gzclient 2>/dev/null || true
    pkill -f "gazebo" 2>/dev/null || true
    
    print_status "Killing ROS2 processes..."
    pkill -f "ros2" 2>/dev/null || true
    pkill -f "slam_toolbox" 2>/dev/null || true
    pkill -f "simple_obstacle_avoidance" 2>/dev/null || true
    pkill -f "rviz" 2>/dev/null || true
    
    print_status "Killing web servers..."
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "python3 main.py" 2>/dev/null || true
    pkill -f "node.*react-scripts" 2>/dev/null || true
    
    print_status "Waiting for processes to terminate..."
    sleep 3
    
    print_success "All processes killed"
}

# Kill only Gazebo processes
kill_gazebo() {
    print_header "KILLING GAZEBO PROCESSES"
    
    print_status "Finding Gazebo processes..."
    ps aux | grep -E "(gazebo|gzserver|gzclient)" | grep -v grep
    
    print_status "Killing Gazebo..."
    killall -9 gazebo gzserver gzclient 2>/dev/null || true
    pkill -f "gazebo" 2>/dev/null || true
    
    print_status "Cleaning Gazebo shared memory..."
    rm -rf /tmp/gazebo* 2>/dev/null || true
    
    print_success "Gazebo processes killed"
}

# Kill only ROS2 processes
kill_ros2() {
    print_header "KILLING ROS2 PROCESSES"
    
    print_status "Finding ROS2 processes..."
    ps aux | grep -E "(ros2|slam_toolbox|obstacle_avoidance)" | grep -v grep
    
    print_status "Killing ROS2 nodes..."
    pkill -f "ros2" 2>/dev/null || true
    pkill -f "slam_toolbox" 2>/dev/null || true
    pkill -f "simple_obstacle_avoidance" 2>/dev/null || true
    pkill -f "rviz" 2>/dev/null || true
    
    print_success "ROS2 processes killed"
}

# Kill web servers
kill_web() {
    print_header "KILLING WEB SERVERS"
    
    print_status "Finding web server processes..."
    ps aux | grep -E "(npm|node|python3.*main.py)" | grep -v grep
    
    print_status "Killing frontend server..."
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "node.*react-scripts" 2>/dev/null || true
    
    print_status "Killing backend server..."
    pkill -f "python3 main.py" 2>/dev/null || true
    
    print_success "Web servers killed"
}

# Clean and rebuild workspace
clean_build() {
    print_header "CLEANING AND REBUILDING WORKSPACE"
    
    print_status "Removing build and install directories..."
    rm -rf build/ install/ log/
    
    print_status "Sourcing ROS2 environment..."
    source /opt/ros/humble/setup.bash
    
    print_status "Building workspace..."
    colcon build --packages-select indoor_nav_bringup
    
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
    else
        print_error "Build failed - check error messages above"
        return 1
    fi
}

# Fix file permissions
fix_permissions() {
    print_header "FIXING FILE PERMISSIONS"
    
    print_status "Making scripts executable..."
    chmod +x start_system.sh
    chmod +x fix_system.sh
    chmod +x src/indoor_nav_bringup/scripts/*.py 2>/dev/null || true
    chmod +x src/indoor_nav_bringup/scripts/*.sh 2>/dev/null || true
    
    print_status "Fixing workspace permissions..."
    find . -name "*.py" -exec chmod +x {} \; 2>/dev/null || true
    find . -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
    
    print_success "Permissions fixed"
}

# Check system status
check_system() {
    print_header "SYSTEM STATUS CHECK"
    
    print_status "Checking ROS2 installation..."
    if command -v ros2 &> /dev/null; then
        print_success "ROS2 found: $(ros2 --version)"
    else
        print_error "ROS2 not found or not sourced"
    fi
    
    print_status "Checking Node.js installation..."
    if command -v node &> /dev/null; then
        print_success "Node.js found: $(node --version)"
    else
        print_error "Node.js not found"
    fi
    
    print_status "Checking Python packages..."
    python3 -c "import rclpy" 2>/dev/null && print_success "rclpy: OK" || print_error "rclpy: MISSING"
    python3 -c "import fastapi" 2>/dev/null && print_success "fastapi: OK" || print_error "fastapi: MISSING"
    python3 -c "import numpy" 2>/dev/null && print_success "numpy: OK" || print_error "numpy: MISSING"
    
    print_status "Checking running processes..."
    echo "Gazebo processes:"
    ps aux | grep -E "(gazebo|gzserver|gzclient)" | grep -v grep || echo "  None running"
    echo "ROS2 processes:"
    ps aux | grep -E "(ros2|slam_toolbox)" | grep -v grep || echo "  None running"
    echo "Web processes:"
    ps aux | grep -E "(npm|node.*react|python3.*main)" | grep -v grep || echo "  None running"
    
    print_status "Checking network ports..."
    netstat -tulpn 2>/dev/null | grep -E "(3000|8000)" || echo "  No web servers running"
    
    print_status "Checking workspace..."
    if [ -f "src/indoor_nav_bringup/package.xml" ]; then
        print_success "Workspace structure: OK"
    else
        print_error "Workspace structure: INVALID - run from workspace root"
    fi
}

# Reset Gazebo configuration
reset_gazebo() {
    print_header "RESETTING GAZEBO CONFIGURATION"
    
    print_status "Killing Gazebo processes..."
    killall -9 gazebo gzserver gzclient 2>/dev/null || true
    
    print_status "Removing Gazebo cache and config..."
    rm -rf ~/.gazebo/log/ 2>/dev/null || true
    rm -rf ~/.gazebo/worlds/ 2>/dev/null || true
    rm -rf /tmp/gazebo* 2>/dev/null || true
    
    print_status "Creating missing Gazebo models..."

    # Create car_wheel model
    mkdir -p ~/.gazebo/models/car_wheel
    cat > ~/.gazebo/models/car_wheel/model.config << EOF
<?xml version="1.0"?>
<model>
  <name>car_wheel</name>
  <version>1.0</version>
  <sdf version="1.4">model.sdf</sdf>
  <description>Simple car wheel model</description>
</model>
EOF

    cat > ~/.gazebo/models/car_wheel/model.sdf << EOF
<?xml version="1.0"?>
<sdf version="1.4">
  <model name="car_wheel">
    <static>true</static>
    <link name="link">
      <visual name="visual">
        <geometry>
          <cylinder>
            <radius>0.3</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>0.2 0.2 0.2 1</ambient>
          <diffuse>0.2 0.2 0.2 1</diffuse>
        </material>
      </visual>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.3</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
      </collision>
    </link>
  </model>
</sdf>
EOF

    print_success "Gazebo models created"
    print_success "Gazebo configuration reset"
}

# Fix missing Gazebo models
fix_gazebo_models() {
    print_header "FIXING MISSING GAZEBO MODELS"

    print_status "Creating missing Gazebo model directories..."

    # Create car_wheel model (the one causing the error)
    mkdir -p ~/.gazebo/models/car_wheel

    print_status "Creating car_wheel model.config..."
    cat > ~/.gazebo/models/car_wheel/model.config << EOF
<?xml version="1.0"?>
<model>
  <name>car_wheel</name>
  <version>1.0</version>
  <sdf version="1.4">model.sdf</sdf>
  <description>Simple car wheel model for Gazebo</description>
  <author>
    <name>Indoor Navigation System</name>
    <email><EMAIL></email>
  </author>
</model>
EOF

    print_status "Creating car_wheel model.sdf..."
    cat > ~/.gazebo/models/car_wheel/model.sdf << EOF
<?xml version="1.0"?>
<sdf version="1.4">
  <model name="car_wheel">
    <static>true</static>
    <link name="link">
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <mass>1.0</mass>
        <inertia>
          <ixx>0.1</ixx>
          <ixy>0.0</ixy>
          <ixz>0.0</ixz>
          <iyy>0.1</iyy>
          <iyz>0.0</iyz>
          <izz>0.1</izz>
        </inertia>
      </inertial>
      <visual name="visual">
        <geometry>
          <cylinder>
            <radius>0.3</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>0.2 0.2 0.2 1</ambient>
          <diffuse>0.2 0.2 0.2 1</diffuse>
          <specular>0.1 0.1 0.1 1</specular>
          <emissive>0 0 0 0</emissive>
        </material>
      </visual>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.3</radius>
            <length>0.1</length>
          </cylinder>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>1.0</mu>
              <mu2>1.0</mu2>
            </ode>
          </friction>
        </surface>
      </collision>
    </link>
  </model>
</sdf>
EOF

    # Create other common missing models
    print_status "Creating other common Gazebo models..."

    # Create simple table model
    mkdir -p ~/.gazebo/models/table
    cat > ~/.gazebo/models/table/model.config << EOF
<?xml version="1.0"?>
<model>
  <name>table</name>
  <version>1.0</version>
  <sdf version="1.4">model.sdf</sdf>
  <description>Simple table model</description>
</model>
EOF

    cat > ~/.gazebo/models/table/model.sdf << EOF
<?xml version="1.0"?>
<sdf version="1.4">
  <model name="table">
    <static>true</static>
    <link name="link">
      <visual name="visual">
        <geometry>
          <box>
            <size>1.5 0.8 0.03</size>
          </box>
        </geometry>
        <material>
          <ambient>0.5 0.3 0.1 1</ambient>
          <diffuse>0.5 0.3 0.1 1</diffuse>
        </material>
      </visual>
      <collision name="collision">
        <geometry>
          <box>
            <size>1.5 0.8 0.03</size>
          </box>
        </geometry>
      </collision>
    </link>
  </model>
</sdf>
EOF

    print_success "Missing Gazebo models created successfully"
    print_status "Models created:"
    echo "  ✅ car_wheel - Simple wheel model"
    echo "  ✅ table - Simple table model"
    echo ""
    print_status "Models location: ~/.gazebo/models/"
}

# Fix Conda conflicts
fix_conda() {
    print_header "FIXING CONDA/PYTHON CONFLICTS"
    
    print_status "Deactivating Conda environment..."
    conda deactivate 2>/dev/null || true
    
    print_status "Clearing Conda environment variables..."
    unset CONDA_DEFAULT_ENV 2>/dev/null || true
    unset CONDA_EXE 2>/dev/null || true
    unset CONDA_PREFIX 2>/dev/null || true
    unset CONDA_PROMPT_MODIFIER 2>/dev/null || true
    unset CONDA_PYTHON_EXE 2>/dev/null || true
    unset CONDA_SHLVL 2>/dev/null || true
    
    print_status "Setting clean PATH..."
    export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    
    print_status "Testing Python environment..."
    which python3
    python3 --version
    
    print_success "Conda conflicts resolved"
}

# Fix npm/Node.js issues
fix_npm() {
    print_header "FIXING NPM/NODE.JS ISSUES"

    print_status "Checking Node.js installation..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js not found - please install Node.js 18+"
        return 1
    fi

    print_status "Node.js version: $(node --version)"
    print_status "npm version: $(npm --version)"

    if [ -d "web_interface/frontend" ]; then
        print_status "Cleaning frontend dependencies..."
        cd web_interface/frontend

        print_status "Removing node_modules and package-lock.json..."
        rm -rf node_modules package-lock.json

        print_status "Clearing npm cache..."
        npm cache clean --force

        print_status "Reinstalling dependencies..."
        npm install

        if [ $? -eq 0 ]; then
            print_success "Frontend dependencies fixed"
        else
            print_error "Failed to reinstall frontend dependencies"
            return 1
        fi

        cd ../..
    else
        print_error "Frontend directory not found"
        return 1
    fi

    print_success "npm/Node.js issues resolved"
}

# Fix port conflicts
fix_ports() {
    print_header "FIXING PORT CONFLICTS"

    print_status "Checking port usage..."

    # Check port 3000
    if netstat -tulpn 2>/dev/null | grep -q ":3000 "; then
        print_warning "Port 3000 is in use"
        netstat -tulpn 2>/dev/null | grep ":3000 "
        print_status "Killing processes on port 3000..."
        pkill -f "npm start" 2>/dev/null || true
        pkill -f "node.*react-scripts" 2>/dev/null || true
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    else
        print_success "Port 3000 is free"
    fi

    # Check port 8000
    if netstat -tulpn 2>/dev/null | grep -q ":8000 "; then
        print_warning "Port 8000 is in use"
        netstat -tulpn 2>/dev/null | grep ":8000 "
        print_status "Killing processes on port 8000..."
        pkill -f "python3 main.py" 2>/dev/null || true
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    else
        print_success "Port 8000 is free"
    fi

    sleep 2
    print_success "Port conflicts resolved"
}

# Reinstall all dependencies
reinstall_deps() {
    print_header "REINSTALLING ALL DEPENDENCIES"

    print_status "Reinstalling Python dependencies..."
    python3 -m pip install --user --upgrade fastapi uvicorn websockets numpy lxml flask flask-socketio

    print_status "Reinstalling frontend dependencies..."
    if [ -d "web_interface/frontend" ]; then
        cd web_interface/frontend
        rm -rf node_modules package-lock.json
        npm cache clean --force
        npm install
        cd ../..
    fi

    print_status "Rebuilding ROS2 workspace..."
    rm -rf build/ install/ log/
    source /opt/ros/humble/setup.bash
    colcon build --packages-select indoor_nav_bringup

    if [ $? -eq 0 ]; then
        print_success "All dependencies reinstalled successfully"
    else
        print_error "Some dependencies failed to install"
        return 1
    fi
}

# Setup missing files after git clone
setup_missing_files() {
    print_header "SETTING UP MISSING FILES"

    print_status "Creating missing package.xml files..."

    # Create mapping package.xml if missing
    if [ ! -f "src/perception/mapping/package.xml" ]; then
        print_status "Creating package.xml for mapping package..."
        mkdir -p src/perception/mapping
        cat > src/perception/mapping/package.xml << 'EOF'
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>mapping</name>
  <version>1.0.0</version>
  <description>Mapping and SLAM for indoor autonomous vehicle</description>
  <maintainer email="<EMAIL>">Indoor Vehicle Team</maintainer>
  <license>MIT</license>
  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>slam_toolbox</depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>
  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
EOF
        print_success "Created mapping/package.xml"
    else
        print_success "mapping/package.xml already exists"
    fi

    # Create sensor_fusion package.xml if missing
    if [ ! -f "src/perception/sensor_fusion/package.xml" ]; then
        print_status "Creating package.xml for sensor_fusion package..."
        mkdir -p src/perception/sensor_fusion
        cat > src/perception/sensor_fusion/package.xml << 'EOF'
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>sensor_fusion</name>
  <version>1.0.0</version>
  <description>Sensor fusion for indoor autonomous vehicle</description>
  <maintainer email="<EMAIL>">Indoor Vehicle Team</maintainer>
  <license>MIT</license>
  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>tf2_ros</depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>
  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
EOF
        print_success "Created sensor_fusion/package.xml"
    else
        print_success "sensor_fusion/package.xml already exists"
    fi

    # Create maps directory and files if missing
    print_status "Setting up maps directory..."
    mkdir -p src/indoor_nav_bringup/maps

    if [ ! -f "src/indoor_nav_bringup/maps/indoor_house.yaml" ]; then
        cat > src/indoor_nav_bringup/maps/indoor_house.yaml << 'EOF'
image: indoor_house.pgm
resolution: 0.050000
origin: [-10.000000, -10.000000, 0.000000]
negate: 0
occupied_thresh: 0.65
free_thresh: 0.196
EOF
        print_success "Created maps/indoor_house.yaml"
    else
        print_success "maps/indoor_house.yaml already exists"
    fi

    if [ ! -f "src/indoor_nav_bringup/maps/README.md" ]; then
        cat > src/indoor_nav_bringup/maps/README.md << 'EOF'
# Maps Directory

This directory contains map files for the indoor navigation system.

## Files:
- `indoor_house.yaml`: Map configuration file
- `indoor_house.pgm`: Map image file (will be generated by SLAM)

## Usage:
Maps are automatically generated during SLAM operation. The system will create and save maps here during navigation.
EOF
        print_success "Created maps/README.md"
    else
        print_success "maps/README.md already exists"
    fi

    print_success "All missing files have been created!"
    print_status "You can now run: colcon build"
}

# Main script logic
case "$1" in
    --kill-all)
        kill_all_processes
        ;;
    --kill-gazebo)
        kill_gazebo
        ;;
    --kill-ros2)
        kill_ros2
        ;;
    --kill-web)
        kill_web
        ;;
    --clean-build)
        clean_build
        ;;
    --fix-permissions)
        fix_permissions
        ;;
    --check-system)
        check_system
        ;;
    --reset-gazebo)
        reset_gazebo
        ;;
    --fix-gazebo-models)
        fix_gazebo_models
        ;;
    --fix-conda)
        fix_conda
        ;;
    --fix-npm)
        fix_npm
        ;;
    --fix-ports)
        fix_ports
        ;;
    --setup-missing-files)
        setup_missing_files
        ;;
    --reinstall-deps)
        reinstall_deps
        ;;
    --help)
        show_help
        ;;
    "")
        print_error "No option specified"
        show_help
        exit 1
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
