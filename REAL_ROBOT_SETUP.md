# Real Robot Setup Guide

## ⚠️ IMPORTANT: REAL ROBOT REQUIREMENTS

**The system CANNOT run on real robot without proper hardware configuration!**

This guide explains what you need to prepare for real robot deployment.

## 🤖 Hardware Requirements

### 1. Robot Platform
- **Differential drive robot** (2 wheels + caster wheel)
- **Onboard computer** running Ubuntu 22.04 + ROS2 Humble
- **Motor controllers** that can receive ROS2 `/cmd_vel` commands
- **Power system** for motors and computer

### 2. LiDAR Sensor
- **2D LiDAR** (e.g., RPLiDAR A1/A2, Hoku<PERSON>, Sick)
- **USB or Ethernet connection** to robot computer
- **ROS2 driver** that publishes to `/scan` topic
- **360-degree scanning** capability
- **Range**: minimum 0.1m to 8m

### 3. Odometry System
- **Wheel encoders** on both drive wheels
- **IMU sensor** (optional but recommended)
- **ROS2 driver** that publishes to `/odom` topic
- **Transform publisher** for `odom` → `base_link`

## 📡 Required ROS2 Topics

Your real robot MUST publish/subscribe to these topics:

### Robot Must Subscribe To:
```bash
/cmd_vel (geometry_msgs/msg/Twist)
# Velocity commands from navigation system
# linear.x: forward/backward speed (m/s)
# angular.z: rotation speed (rad/s)
```

### Robot Must Publish:
```bash
/scan (sensor_msgs/msg/LaserScan)
# LiDAR data with:
# - angle_min, angle_max: scan range
# - angle_increment: angular resolution
# - ranges[]: distance measurements
# - Header with timestamp

/odom (nav_msgs/msg/Odometry)
# Robot position and velocity with:
# - pose.pose.position: x, y, z coordinates
# - pose.pose.orientation: quaternion rotation
# - twist.twist: linear and angular velocities
# - Header with timestamp

/tf (tf2_msgs/msg/TFMessage)
# Transform from odom → base_link
# Updated at high frequency (50-100 Hz)
```

## 🔧 Hardware Integration Steps

### Step 1: Install LiDAR Driver
```bash
# Example for RPLiDAR A1/A2
sudo apt install ros-humble-rplidar-ros

# Example launch command
ros2 launch rplidar_ros rplidar_a1_launch.py

# Verify LiDAR data
ros2 topic echo /scan
```

### Step 2: Setup Motor Controllers
```bash
# Example for Arduino-based motor controller
# You need to create/install driver that:
# 1. Subscribes to /cmd_vel
# 2. Converts to motor PWM signals
# 3. Sends to motor controllers

# Example driver structure:
ros2 run your_motor_package motor_controller_node
```

### Step 3: Setup Odometry
```bash
# Example for wheel encoders + IMU
# You need driver that:
# 1. Reads encoder counts
# 2. Calculates wheel velocities
# 3. Integrates to get position
# 4. Publishes /odom and /tf

# Example odometry node:
ros2 run your_odometry_package odometry_node
```

### Step 4: Robot Description
Create URDF file describing your robot:
```xml
<!-- robot.urdf.xacro -->
<robot name="your_robot">
  <link name="base_link">
    <!-- Robot base dimensions -->
  </link>
  
  <link name="laser_link">
    <!-- LiDAR sensor position -->
  </link>
  
  <joint name="laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="laser_link"/>
    <!-- LiDAR mounting position/orientation -->
  </joint>
</robot>
```

## ⚙️ Configuration Files

### 1. Real Robot Launch File
```python
# src/indoor_nav_bringup/launch/real_robot.launch.py
def generate_launch_description():
    return LaunchDescription([
        # Robot state publisher
        Node(
            package='robot_state_publisher',
            executable='robot_state_publisher',
            parameters=[{'robot_description': robot_description}]
        ),
        
        # LiDAR driver
        Node(
            package='rplidar_ros',  # or your LiDAR package
            executable='rplidar_composition',
            parameters=[{
                'serial_port': '/dev/ttyUSB0',
                'frame_id': 'laser_link'
            }]
        ),
        
        # Motor controller
        Node(
            package='your_motor_package',
            executable='motor_controller_node'
        ),
        
        # Odometry
        Node(
            package='your_odometry_package', 
            executable='odometry_node'
        ),
        
        # Navigation system (same as simulation)
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([
                PathJoinSubstitution([
                    FindPackageShare('indoor_nav_bringup'),
                    'launch',
                    'simple_nav2.launch.py'
                ])
            ]),
            launch_arguments={
                'use_gazebo': 'false',
                'use_sim_time': 'false'
            }.items()
        )
    ])
```

### 2. Real Robot Parameters
```yaml
# config/real_robot_params.yaml
simple_obstacle_avoidance:
  ros__parameters:
    use_sim_time: false
    max_linear_speed: 0.3      # Adjust for your robot
    max_angular_speed: 1.0     # Adjust for your robot
    obstacle_distance_threshold: 0.8  # Increase for safety
    goal_tolerance: 0.15       # Increase for real-world accuracy
```

## 🚀 Running on Real Robot

### 1. Test Individual Components
```bash
# Test LiDAR
ros2 topic echo /scan

# Test motor control
ros2 topic pub /cmd_vel geometry_msgs/msg/Twist '{linear: {x: 0.1}}'

# Test odometry
ros2 topic echo /odom

# Check transforms
ros2 run tf2_tools view_frames
```

### 2. Launch Navigation System
```bash
# Use real robot mode
./start_system.sh --real-robot

# Or manually:
ros2 launch indoor_nav_bringup real_robot.launch.py
```

## 🔍 Calibration Required

### 1. LiDAR Calibration
- **Mount position**: Measure exact position relative to robot center
- **Orientation**: Ensure 0° points forward
- **Height**: Mount at appropriate height to detect obstacles

### 2. Odometry Calibration
- **Wheel diameter**: Measure precisely
- **Wheel base**: Distance between drive wheels
- **Encoder resolution**: Counts per revolution
- **Gear ratios**: If using geared motors

### 3. Navigation Parameters
- **Robot radius**: For collision avoidance
- **Max speeds**: Safe limits for your robot
- **Acceleration limits**: Based on motor capabilities

## ⚠️ Safety Considerations

### 1. Emergency Stop
```bash
# Always have emergency stop ready
ros2 topic pub /cmd_vel geometry_msgs/msg/Twist '{}'
```

### 2. Speed Limits
```yaml
# Conservative speeds for initial testing
max_linear_speed: 0.2   # 0.2 m/s = 20 cm/s
max_angular_speed: 0.5  # 0.5 rad/s = ~30°/s
```

### 3. Obstacle Detection
```yaml
# Increase safety margins
obstacle_distance_threshold: 1.0  # Stop 1m from obstacles
robot_radius: 0.3  # Add safety margin to robot size
```

## 🛠️ Common Real Robot Issues

### 1. Transform Issues
```bash
# Check transform tree
ros2 run tf2_tools view_frames

# Common missing transforms:
# - odom → base_link (from odometry node)
# - base_link → laser_link (from robot_state_publisher)
```

### 2. Time Synchronization
```bash
# Ensure all nodes use same time source
# Set use_sim_time: false for all real robot nodes
```

### 3. Coordinate Frames
```bash
# Standard ROS2 conventions:
# - X axis: forward
# - Y axis: left  
# - Z axis: up
# - Rotation: counter-clockwise positive
```

## 📋 Pre-Deployment Checklist

- [ ] LiDAR driver installed and tested
- [ ] Motor controllers respond to /cmd_vel
- [ ] Odometry publishes accurate /odom
- [ ] All transforms properly configured
- [ ] Robot URDF matches physical robot
- [ ] Safety parameters configured
- [ ] Emergency stop procedure tested
- [ ] Navigation parameters tuned
- [ ] Workspace mapped (if using pre-built maps)

## 🎯 Summary

**The system is designed to work with real robots, but requires:**

1. **Hardware integration** (LiDAR, motors, encoders)
2. **ROS2 drivers** for all sensors/actuators  
3. **Proper calibration** of all parameters
4. **Safety configuration** and testing

**Without these components, the system will NOT work on real hardware!**

The navigation algorithm itself is production-ready and will work perfectly once the hardware integration is complete.
