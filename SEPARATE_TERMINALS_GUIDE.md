# Separate Terminals Usage Guide

## 🎯 Overview

You now have the option to run system components in separate terminals for better control and debugging.

## 🚀 Two Ways to Run the System

### Method 1: All-in-One (Original)
```bash
./start_system.sh          # Everything in one terminal
```

### Method 2: Separate Terminals (New)
```bash
# Terminal 1: ROS2 System
./start_ros2.sh

# Terminal 2: Backend Server  
./start_backend.sh

# Terminal 3: Frontend Server
./start_frontend.sh
```

## 📋 Startup Order for Separate Terminals

### 1. Start ROS2 System First
```bash
# Terminal 1
./start_ros2.sh
# Wait for "System startup complete" message
```

### 2. Start Backend Server
```bash
# Terminal 2  
./start_backend.sh
# Wait for "Backend server starting..." message
```

### 3. Start Frontend Server
```bash
# Terminal 3
./start_frontend.sh
# Wait for "compiled successfully" message
```

## 🔧 Individual Script Features

### start_ros2.sh
- **Purpose**: ROS2 navigation system only
- **Includes**: Gazebo, SLAM, obstacle avoidance, robot
- **Options**: `--simulation` (default), `--real-robot`
- **Port**: None (uses ROS2 communication)

### start_backend.sh  
- **Purpose**: FastAPI server with WebSocket
- **Includes**: ROS2 bridge, API endpoints, real-time data
- **Port**: 8000
- **Features**: Auto-detects ROS2, handles Conda conflicts

### start_frontend.sh
- **Purpose**: React development server
- **Includes**: Web interface, map visualization, controls
- **Port**: 3000  
- **Features**: Auto-installs dependencies, port conflict detection

## ✅ Advantages of Separate Terminals

### 1. Better Debugging
- **Isolated logs**: Each component's output in separate terminal
- **Easier troubleshooting**: Can restart individual components
- **Clear error messages**: No mixed output from different systems

### 2. Flexible Development
- **Frontend only**: Restart just frontend for UI changes
- **Backend only**: Restart just backend for API changes
- **ROS2 only**: Restart just ROS2 for navigation changes

### 3. Resource Management
- **Selective shutdown**: Stop only what you need
- **Memory optimization**: Run only required components
- **Performance monitoring**: Monitor each component separately

## 🛑 Stopping Individual Components

### Stop ROS2 System
```bash
# In Terminal 1 (start_ros2.sh)
Ctrl+C
# Automatically kills Gazebo, SLAM, navigation nodes
```

### Stop Backend Server
```bash
# In Terminal 2 (start_backend.sh)  
Ctrl+C
# Automatically kills FastAPI server
```

### Stop Frontend Server
```bash
# In Terminal 3 (start_frontend.sh)
Ctrl+C
# Automatically kills React dev server
```

## 🔧 Troubleshooting Separate Components

### ROS2 System Issues
```bash
./fix_system.sh --kill-ros2     # Kill only ROS2
./fix_system.sh --kill-gazebo   # Kill only Gazebo
./start_ros2.sh                 # Restart ROS2 system
```

### Backend Issues
```bash
./fix_system.sh --fix-ports     # Fix port 8000 conflicts
./fix_system.sh --fix-conda     # Fix Python conflicts
./start_backend.sh              # Restart backend
```

### Frontend Issues
```bash
./fix_system.sh --fix-npm       # Fix npm issues
./fix_system.sh --fix-ports     # Fix port 3000 conflicts
./start_frontend.sh             # Restart frontend
```

## 📊 Component Dependencies

### Dependency Chain
```
ROS2 System (start_ros2.sh)
    ↓ (provides robot data)
Backend Server (start_backend.sh)  
    ↓ (provides API/WebSocket)
Frontend Server (start_frontend.sh)
```

### What Depends on What
- **Frontend** depends on **Backend** (for robot data)
- **Backend** depends on **ROS2** (for robot communication)
- **ROS2** is independent (can run alone)

## 🎮 Usage Scenarios

### Scenario 1: Full Development
```bash
# All terminals for full system development
Terminal 1: ./start_ros2.sh
Terminal 2: ./start_backend.sh  
Terminal 3: ./start_frontend.sh
```

### Scenario 2: ROS2 Development Only
```bash
# Only ROS2 for navigation/robotics development
Terminal 1: ./start_ros2.sh
# Use ROS2 tools: rviz2, ros2 topic, etc.
```

### Scenario 3: Web Development Only
```bash
# Backend + Frontend for web interface development
Terminal 1: ./start_backend.sh
Terminal 2: ./start_frontend.sh
# Mock robot data for UI development
```

### Scenario 4: Testing Individual Components
```bash
# Test each component separately
./start_ros2.sh     # Test navigation
./start_backend.sh  # Test API
./start_frontend.sh # Test UI
```

## 🔍 Monitoring Multiple Terminals

### Terminal Layout Suggestion
```
┌─────────────────┬─────────────────┐
│   Terminal 1    │   Terminal 2    │
│   ROS2 System   │  Backend Server │
│  (start_ros2)   │ (start_backend) │
├─────────────────┼─────────────────┤
│   Terminal 3    │   Terminal 4    │
│ Frontend Server │  Troubleshooting│
│(start_frontend) │ (fix_system.sh) │
└─────────────────┴─────────────────┘
```

### Useful Commands for Monitoring
```bash
# Terminal 4: Monitoring commands
watch -n 1 'ps aux | grep -E "(gazebo|ros2|npm|python3)"'
watch -n 1 'netstat -tulpn | grep -E "(3000|8000)"'
./fix_system.sh --check-system
```

## 🚨 Emergency Procedures

### Kill Everything Quickly
```bash
# In any terminal
./fix_system.sh --kill-all
```

### Restart Specific Component
```bash
# Kill specific component
./fix_system.sh --kill-ros2     # or --kill-web

# Restart in appropriate terminal
./start_ros2.sh                 # or ./start_backend.sh
```

## 💡 Pro Tips

### 1. Use Terminal Tabs
- **Tab 1**: ROS2 System
- **Tab 2**: Backend Server
- **Tab 3**: Frontend Server
- **Tab 4**: Troubleshooting

### 2. Save Terminal Sessions
```bash
# Use tmux or screen to save sessions
tmux new-session -d -s robot './start_ros2.sh'
tmux new-session -d -s backend './start_backend.sh'
tmux new-session -d -s frontend './start_frontend.sh'
```

### 3. Quick Status Check
```bash
# Check all components at once
./fix_system.sh --check-system
```

---

**Choose the method that works best for your workflow!**
- **All-in-One**: Simple, quick startup
- **Separate Terminals**: Better control, easier debugging
