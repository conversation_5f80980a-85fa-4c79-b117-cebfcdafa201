#!/usr/bin/env python

"""
ROS Noetic to Backend Bridge
Standalone bridge that connects your real ROS Noetic system to the existing backend
Run this on the machine with ROS Noetic to bridge to the web interface backend
"""

import rospy
import json
import threading
import time
import requests
try:
    from websocket import WebSocketApp
    websocket_available = True
except ImportError:
    try:
        import websocket
        WebSocketApp = websocket.WebSocketApp
        websocket_available = True
    except (ImportError, AttributeError):
        websocket_available = False
        WebSocketApp = None
from threading import Thread
import signal
import sys
import os

# ROS message imports
from std_msgs.msg import String
from sensor_msgs.msg import LaserScan
from amr_battery.msg import Battery_msgs
from nav_msgs.msg import OccupancyGrid, Odometry
from geometry_msgs.msg import Twist, PoseStamped
from tf.transformations import euler_from_quaternion

class RosNoeticBackendBridge:
    def __init__(self):
        # Set ROS master URI if provided
        ros_master_uri = rospy.get_param('~ros_master_uri', None)
        if ros_master_uri:
            os.environ['ROS_MASTER_URI'] = ros_master_uri
            rospy.loginfo(f"Connecting to ROS master at: {ros_master_uri}")

        rospy.init_node('ros_noetic_backend_bridge', anonymous=True)
        
        # Configuration - Update these to match your remote backend
        self.backend_host = rospy.get_param('~backend_host', 'localhost')
        self.backend_port = rospy.get_param('~backend_port', 8000)
        self.websocket_port = rospy.get_param('~websocket_port', 8000)  # Same as backend port
        self.update_rate = rospy.get_param('~update_rate', 10)  # Hz
        
        # API endpoints
        self.base_url = f"http://{self.backend_host}:{self.backend_port}"
        self.ws_url = f"ws://{self.backend_host}:{self.websocket_port}/ws"
        
        # Data storage
        self.latest_scan = None
        self.latest_odom = None
        self.latest_map = None
        self.robot_status = {
            'connected': True,
            'mode': 'autonomous',
            'position': {'x': 0, 'y': 0, 'theta': 0},
            'linear_velocity': 0.0,
            'angular_velocity': 0.0
        }
        
        # WebSocket connection
        self.ws = None
        self.ws_connected = False
        
        # ROS Subscribers - Subscribe to your real robot's topics
        self.scan_sub = rospy.Subscriber('/scan_forward', LaserScan, self.scan_callback)
        self.odom_sub = rospy.Subscriber('/odom_from_laser', Odometry, self.odom_callback)
        self.map_sub = rospy.Subscriber('/map', OccupancyGrid, self.map_callback)
        
        # ROS Publishers - Publish commands to your real robot
        self.cmd_vel_pub = rospy.Publisher('/output_vel', Twist, queue_size=1)
        self.goal_pub = rospy.Publisher('/move_base/current_goal', PoseStamped, queue_size=1)
        
        # Start WebSocket client
        self.start_websocket_client()
        
        # Start periodic updates
        self.start_periodic_updates()
        
        # Start HTTP status endpoint
        self.start_http_status_server()
        
        rospy.loginfo("ROS Noetic Backend Bridge initialized")
        rospy.loginfo(f"Backend: {self.base_url}")
        rospy.loginfo(f"WebSocket: {self.ws_url}")
        rospy.loginfo("Bridging real ROS Noetic system to web backend")
        
    def scan_callback(self, msg):
        """Handle LiDAR scan data from real robot"""
        # Convert ROS LaserScan to format expected by backend
        self.latest_scan = {
            'timestamp': time.time(),
            'ranges': [r if not (r < msg.range_min or r > msg.range_max) else None for r in msg.ranges],
            'angle_min': msg.angle_min,
            'angle_max': msg.angle_max,
            'angle_increment': msg.angle_increment,
            'range_min': msg.range_min,
            'range_max': msg.range_max,
            'header': {
                'frame_id': msg.header.frame_id,
                'stamp': msg.header.stamp.to_sec()
            }
        }
        
    def odom_callback(self, msg):
        """Handle odometry data from real robot"""
        # Extract position and orientation
        position = msg.pose.pose.position
        orientation = msg.pose.pose.orientation
        linear_vel = msg.twist.twist.linear
        angular_vel = msg.twist.twist.angular
        
        # Convert quaternion to euler angles
        _, _, yaw = euler_from_quaternion([
            orientation.x, orientation.y, orientation.z, orientation.w
        ])
        
        self.latest_odom = {
            'timestamp': time.time(),
            'position': {
                'x': position.x,
                'y': position.y,
                'z': position.z
            },
            'orientation': {
                'x': orientation.x,
                'y': orientation.y,
                'z': orientation.z,
                'w': orientation.w,
                'yaw': yaw
            },
            'linear_velocity': {
                'x': linear_vel.x,
                'y': linear_vel.y,
                'z': linear_vel.z
            },
            'angular_velocity': {
                'x': angular_vel.x,
                'y': angular_vel.y,
                'z': angular_vel.z
            },
            'header': {
                'frame_id': msg.header.frame_id,
                'stamp': msg.header.stamp.to_sec()
            }
        }
        
        # Update robot status
        self.robot_status.update({
            'position': {'x': position.x, 'y': position.y, 'theta': yaw},
            'linear_velocity': linear_vel.x,
            'angular_velocity': angular_vel.z
        })
        
    def map_callback(self, msg):
        """Handle map data from real robot SLAM"""
        # Convert quaternion to yaw angle for theta
        import math
        qx = msg.info.origin.orientation.x
        qy = msg.info.origin.orientation.y
        qz = msg.info.origin.orientation.z
        qw = msg.info.origin.orientation.w

        # Convert quaternion to yaw (theta)
        siny_cosp = 2 * (qw * qz + qx * qy)
        cosy_cosp = 1 - 2 * (qy * qy + qz * qz)
        theta = math.atan2(siny_cosp, cosy_cosp)

        self.latest_map = {
            'timestamp': time.time(),
            'width': msg.info.width,
            'height': msg.info.height,
            'resolution': msg.info.resolution,
            'origin': {
                'x': msg.info.origin.position.x,
                'y': msg.info.origin.position.y,
                'theta': theta
            },
            'data': list(msg.data)
        }

    def start_websocket_client(self):
        """Start WebSocket client for real-time communication with backend"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                rospy.logdebug(f"Received data: {data}")
                if data.get('data_type', '') not in (['odom', 'scan', 'diagnostics', 'pose', 'network_status', 'node_status']):
                    rospy.logdebug(f"Received data: {data}")
                    self.handle_backend_command(data)
            except Exception as e:
                rospy.logerr(f"WebSocket message error: {e}")
                
        def on_error(ws, error):
            rospy.logwarn(f"WebSocket error: {error}")
            self.ws_connected = False
            
        def on_close(ws, close_status_code, close_msg):
            rospy.loginfo("WebSocket connection closed")
            self.ws_connected = False
            
        def on_open(ws):
            rospy.loginfo("WebSocket connection opened")
            self.ws_connected = True
            # Send initial status
            self.send_robot_status()
            
        def run_websocket():
            while not rospy.is_shutdown():
                try:
                    if not websocket_available:
                        rospy.logwarn("WebSocket client not available, skipping WebSocket connection")
                        return

                    self.ws = WebSocketApp(
                        self.ws_url,
                        on_message=on_message,
                        on_error=on_error,
                        on_close=on_close,
                        on_open=on_open
                    )
                    self.ws.run_forever()
                except Exception as e:
                    rospy.logwarn(f"WebSocket connection failed: {e}")
                    time.sleep(5)  # Wait before reconnecting
                
        # Start WebSocket in separate thread
        ws_thread = Thread(target=run_websocket)
        ws_thread.daemon = True
        ws_thread.start()
        
    def handle_backend_command(self, data):
        """Handle commands received from backend via WebSocket"""
        rospy.loginfo(f"Received command: {data}")
        command_type = data.get('type', '')
        if command_type == 'connection':
            rospy.loginfo(data['message'])
        elif command_type == 'ping':
            # Handle ping from backend
            self.send_pong()
        else:
            rospy.logwarn(f"Unknown command type: {data}")
            
    def handle_move_command(self, data):
        """Send movement command to real robot"""
        try:
            cmd = Twist()
            cmd.linear.x = data.get('linear_x', 0.0)
            cmd.linear.y = data.get('linear_y', 0.0)
            cmd.angular.z = data.get('angular_z', 0.0)
            
            self.cmd_vel_pub.publish(cmd)
            rospy.loginfo(f"Move command sent: linear={cmd.linear.x:.2f}, angular={cmd.angular.z:.2f}")
            
            # Send confirmation back to backend
            self.send_command_response('move_robot', 'success', 'Movement command executed')
            
        except Exception as e:
            rospy.logerr(f"Move command error: {e}")
            self.send_command_response('move_robot', 'error', str(e))
            
    def handle_goal_command(self, data):
        """Send navigation goal to real robot"""
        try:
            goal = PoseStamped()
            goal.header.frame_id = "map"
            goal.header.stamp = rospy.Time.now()
            goal.pose.position.x = data.get('x', 0.0)
            goal.pose.position.y = data.get('y', 0.0)
            goal.pose.orientation.w = 1.0  # Default orientation
            
            self.goal_pub.publish(goal)
            rospy.loginfo(f"Navigation goal sent: x={goal.pose.position.x:.2f}, y={goal.pose.position.y:.2f}")
            
            # Send confirmation back to backend
            self.send_command_response('set_goal', 'success', 'Navigation goal set')
            
        except Exception as e:
            rospy.logerr(f"Goal command error: {e}")
            self.send_command_response('set_goal', 'error', str(e))
            
    def handle_stop_command(self):
        """Stop the real robot"""
        try:
            cmd = Twist()  # All zeros
            self.cmd_vel_pub.publish(cmd)
            rospy.loginfo("Robot stopped")
            
            # Send confirmation back to backend
            self.send_command_response('stop_robot', 'success', 'Robot stopped')
            
        except Exception as e:
            rospy.logerr(f"Stop command error: {e}")
            self.send_command_response('stop_robot', 'error', str(e))
            
    def send_robot_status(self):
        """Send comprehensive robot status to backend"""
        if not self.ws_connected:
            return
            
        try:
            # Extract robot pose from odometry for frontend
            robot_pose = None
            position = self.latest_odom.get('position', {})
            orientation = self.latest_odom.get('orientation', {})

            robot_pose = {
                'x': position.get('x', 0),
                'y': position.get('y', 0),
                'z': position.get('z', 0),
                'orientation': orientation
            }

            status_data = {
                'type': 'robot_status',
                'timestamp': time.time(),
                'robot_status': self.robot_status,
                'scan_data': self.latest_scan,
                'odometry': self.latest_odom,
                'pose': robot_pose,  # Add explicit pose for frontend
                'robot_pose': robot_pose,  # Alternative name frontend might expect
                'map_data': self.latest_map,
                'bridge_info': {
                    'ros_distro': 'noetic',
                    'bridge_version': '1.0.0',
                    'connected': True
                }
            }
            
            self.ws.send(json.dumps(status_data))
            
        except Exception as e:
            rospy.logwarn(f"Failed to send robot status: {e}")
            
    def send_command_response(self, command, status, message):
        """Send command response back to backend"""
        if not self.ws_connected:
            return

        try:
            response_data = {
                'type': 'command_response',
                'command': command,
                'status': status,
                'message': message,
                'timestamp': time.time()
            }

            self.ws.send(json.dumps(response_data))

        except Exception as e:
            rospy.logwarn(f"Failed to send command response: {e}")

    def send_pong(self):
        """Send pong response to backend ping"""
        if not self.ws_connected:
            return

        try:
            pong_data = {
                'type': 'pong',
                'timestamp': time.time()
            }

            self.ws.send(json.dumps(pong_data))

        except Exception as e:
            rospy.logwarn(f"Failed to send pong: {e}")
            
    def start_periodic_updates(self):
        """Start periodic status updates to backend"""
        def update_loop():
            rate = rospy.Rate(self.update_rate)
            while not rospy.is_shutdown():
                if self.ws_connected:
                    self.send_robot_status()
                rate.sleep()
                
        update_thread = Thread(target=update_loop)
        update_thread.daemon = True
        update_thread.start()
        
    def start_http_status_server(self):
        """Start simple HTTP status server for backend health checks"""
        def status_server():
            try:
                from http.server import HTTPServer, BaseHTTPRequestHandler
                import json
                
                class StatusHandler(BaseHTTPRequestHandler):
                    def do_GET(self):
                        if self.path == '/status':
                            self.send_response(200)
                            self.send_header('Content-type', 'application/json')
                            self.end_headers()
                            
                            status = {
                                'bridge_status': 'connected',
                                'ros_connected': True,
                                'websocket_connected': self.ws_connected,
                                'last_scan': self.latest_scan is not None,
                                'last_odom': self.latest_odom is not None,
                                'robot_status': self.robot_status
                            }
                            
                            self.wfile.write(json.dumps(status).encode())
                        else:
                            self.send_response(404)
                            self.end_headers()
                            
                    def log_message(self, format, *args):
                        pass  # Suppress HTTP logs
                
                server = HTTPServer(('0.0.0.0', 9090), StatusHandler)
                rospy.loginfo("HTTP status server started on port 9090")
                server.serve_forever()
                
            except Exception as e:
                rospy.logwarn(f"HTTP status server error: {e}")
                
        status_thread = Thread(target=status_server)
        status_thread.daemon = True
        status_thread.start()
        
    def shutdown(self):
        """Cleanup on shutdown"""
        rospy.loginfo("Shutting down ROS Noetic Backend Bridge")
        if self.ws:
            self.ws.close()

def signal_handler(sig, frame):
    rospy.loginfo("Bridge shutdown requested")
    rospy.signal_shutdown("User interrupt")
    sys.exit(0)

if __name__ == '__main__':
    # Setup signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        bridge = RosNoeticBackendBridge()
        rospy.loginfo("Bridge is running. Press Ctrl+C to stop.")
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr(f"Bridge error: {e}")
    finally:
        if 'bridge' in locals():
            bridge.shutdown()
