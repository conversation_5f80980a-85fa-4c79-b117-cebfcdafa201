# Quick Commands Reference

## 🚀 System Control

### Start Complete System (All-in-One)
```bash
# Simulation mode (default) - starts everything in one terminal
./start_system.sh

# Real robot mode
./start_system.sh --real-robot

# Get help
./start_system.sh --help
```

### Start Individual Components (Separate Terminals)
```bash
# Terminal 1: Start ROS2 system (Gazebo + Navigation)
./start_ros2.sh                    # Simulation
./start_ros2.sh --real-robot       # Real robot

# Terminal 2: Start backend server
./start_backend.sh

# Terminal 3: Start frontend server
./start_frontend.sh
```

### Stop System
```bash
# Press Ctrl+C in each terminal running the scripts
# Each script will automatically clean up its processes
```

## 🔧 Troubleshooting Commands

### When System Gets Stuck
```bash
# Kill everything and start fresh
./fix_system.sh --kill-all

# Check what's wrong
./fix_system.sh --check-system

# Get all troubleshooting options
./fix_system.sh --help
```

### Specific Issues
```bash
# Multiple Gazebo processes running
./fix_system.sh --kill-gazebo

# ROS2 nodes stuck
./fix_system.sh --kill-ros2

# Web servers not responding
./fix_system.sh --kill-web

# Build issues
./fix_system.sh --clean-build

# Permission problems
./fix_system.sh --fix-permissions

# Gazebo won't start
./fix_system.sh --reset-gazebo

# Missing Gazebo models error
./fix_system.sh --fix-gazebo-models

# Python/Conda conflicts
./fix_system.sh --fix-conda

# npm/Node.js issues
./fix_system.sh --fix-npm

# Port conflicts (3000/8000 busy)
./fix_system.sh --fix-ports

# Reinstall all dependencies
./fix_system.sh --reinstall-deps
```

## 🤖 Robot Control

### Emergency Stop
```bash
# Stop robot immediately
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{}'
```

### Manual Control
```bash
# Move forward
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{linear: {x: 0.1}}'

# Move backward  
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{linear: {x: -0.1}}'

# Turn left
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{angular: {z: 0.3}}'

# Turn right
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{angular: {z: -0.3}}'
```

### Set Navigation Goal
```bash
# Go to position (2, 1)
ros2 topic pub --once /goal_pose geometry_msgs/msg/PoseStamped "{header: {frame_id: 'map'}, pose: {position: {x: 2.0, y: 1.0, z: 0.0}, orientation: {w: 1.0}}}"

# Go home (0, 0)
ros2 topic pub --once /goal_pose geometry_msgs/msg/PoseStamped "{header: {frame_id: 'map'}, pose: {position: {x: 0.0, y: 0.0, z: 0.0}, orientation: {w: 1.0}}}"
```

## 📊 System Monitoring

### Check System Status
```bash
# Overall system health
./fix_system.sh --check-system

# ROS2 topics
ros2 topic list

# Active nodes
ros2 node list

# System processes
ps aux | grep -E "(gazebo|ros2|npm|python3)"
```

### Monitor Robot Data
```bash
# LiDAR data
ros2 topic echo /scan

# Robot position
ros2 topic echo /odom

# Velocity commands
ros2 topic echo /cmd_vel

# Navigation goals
ros2 topic echo /goal_pose

# Map data
ros2 topic echo /map
```

### Network Status
```bash
# Check web servers
curl http://localhost:3000
curl http://localhost:8000/health

# Check ports
netstat -tulpn | grep -E "(3000|8000)"
```

## 🌐 Web Interface

### Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000

### Browser Issues
```bash
# Clear browser cache: Ctrl+Shift+R
# Open developer console: F12
# Check for JavaScript errors in console
```

## 🔍 Debugging

### ROS2 Debugging
```bash
# Check ROS2 environment
echo $ROS_DOMAIN_ID
ros2 doctor

# Monitor topic frequency
ros2 topic hz /scan
ros2 topic hz /odom

# Check transforms
ros2 run tf2_tools view_frames

# Debug specific node
ros2 run <package> <node> --ros-args --log-level DEBUG
```

### Build Issues
```bash
# Clean rebuild
./fix_system.sh --clean-build

# Build specific package
colcon build --packages-select indoor_nav_bringup

# Check dependencies
rosdep check --from-paths src --ignore-src
```

## ⚠️ Common Problems & Solutions

### "Permission denied" errors
```bash
./fix_system.sh --fix-permissions
```

### Multiple Gazebo instances
```bash
./fix_system.sh --kill-gazebo
```

### Missing Gazebo models (car_wheel error)
```bash
./fix_system.sh --fix-gazebo-models
```

### Python import errors
```bash
./fix_system.sh --fix-conda
python3 -m pip install --user <missing_package>
```

### Web interface not loading
```bash
./fix_system.sh --kill-web
cd web_interface/frontend
npm install
```

### ROS2 topics not found
```bash
source /opt/ros/humble/setup.bash
source install/setup.bash
ros2 topic list
```

## 🚨 Emergency Procedures

### System Completely Stuck
```bash
# Nuclear option - kill everything
./fix_system.sh --kill-all
sudo pkill -f gazebo
sudo pkill -f ros2
```

### Robot Moving Erratically
```bash
# Emergency stop
ros2 topic pub --once /cmd_vel geometry_msgs/msg/Twist '{}'

# Or press Ctrl+C in start_system.sh terminal
```

### Can't Connect to Robot
```bash
# Check ROS2 connection
ros2 topic list
ros2 node list

# Check network
ping <robot_ip>  # if using networked robot
```

## 📱 Quick Status Check

Run this one-liner to check everything:
```bash
echo "=== SYSTEM STATUS ===" && \
./fix_system.sh --check-system && \
echo "=== WEB SERVERS ===" && \
curl -s http://localhost:3000 > /dev/null && echo "Frontend: OK" || echo "Frontend: DOWN" && \
curl -s http://localhost:8000/health > /dev/null && echo "Backend: OK" || echo "Backend: DOWN" && \
echo "=== ROS2 TOPICS ===" && \
ros2 topic list | grep -E "(scan|cmd_vel|odom|goal)" 2>/dev/null || echo "ROS2: Not running"
```

---

**💡 Pro Tip**: Bookmark this file! These are the commands you'll use most often.
