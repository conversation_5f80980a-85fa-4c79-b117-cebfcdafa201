# ROS2 và Colcon build artifacts
build/
install/
log/
.colcon_install_layout
.colcon_test_result_summary

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv312/
venv/
ENV/
env.bak/
venv.bak/
autonomous-venv/
*.egg-info/
dist/
build/
.eggs/

# IDE và Editor
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs và temporary files
*.log
*.tmp
*.temp
.cache/
.pytest_cache/

# ROS specific
*.bag
*.db3
*.mcap
devel/
devel_isolated/
.catkin_workspace
# src/CMakeLists.txt - COMMENTED OUT: might be needed for some ROS2 packages

# Hardware specific
*.pcap
*.rosbag

# Configuration files với thông tin nhạy cảm
config/secrets.yaml
config/private_*.yaml
*.pem
*.key
*.crt

# Runtime data
waypoints/
# maps/ - COMMENTED OUT: maps directory contains essential navigation files
logs/
data/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Compiled Object files
*.o
*.obj

# Executables
*.exe
*.out
*.app

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Documentation build
docs/_build/
docs/build/

# Test results
test_results/
# *.xml - COMMENTED OUT: package.xml files are essential for ROS2 packages

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Virtual environments
.env
.venv

# Node.js (nếu có web components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebooks
.ipynb_checkpoints

# PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

#conda
miniconda.sh

.zip