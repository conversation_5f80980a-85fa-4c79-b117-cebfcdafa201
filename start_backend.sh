#!/bin/bash

# =============================================================================
# BACKEND SERVER STARTUP SCRIPT
# =============================================================================
# Starts the FastAPI backend server with WebSocket support
# Run this in a separate terminal from the ROS2 system
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    print_header "SHUTTING DOWN BACKEND SERVER"
    print_status "Stopping backend processes..."
    pkill -f "python3 main.py" 2>/dev/null || true
    print_success "Backend shutdown complete"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

print_header "BACKEND SERVER STARTUP"

# Check if we're in the right directory
if [ ! -f "web_interface/backend/app/main.py" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: web_interface/backend/app/main.py"
    exit 1
fi

# Check if ROS2 is available
print_status "Checking ROS2 environment..."
if ! command -v ros2 &> /dev/null; then
    print_warning "ROS2 not found in PATH - sourcing environment..."
    source /opt/ros/humble/setup.bash 2>/dev/null || {
        print_error "Cannot source ROS2 environment"
        print_error "Please install ROS2 Humble or source it manually"
        exit 1
    }
fi

# Source workspace if available
if [ -f "install/setup.bash" ]; then
    print_status "Sourcing workspace..."
    source install/setup.bash
else
    print_warning "Workspace not built - some features may not work"
    print_warning "Run: colcon build --packages-select indoor_nav_bringup"
fi

# Handle Conda conflicts
print_status "Handling Python environment..."
if command -v conda &> /dev/null; then
    print_status "Conda detected - deactivating to avoid conflicts..."
    conda deactivate 2>/dev/null || true
    export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:$HOME/.local/bin"
fi

# Check Python dependencies
print_status "Checking Python dependencies..."
python3 -c "import fastapi, uvicorn, websockets" 2>/dev/null || {
    print_error "Missing Python dependencies!"
    print_error "Install with: python3 -m pip install --user fastapi uvicorn websockets"
    exit 1
}

# Check if port 8000 is available
if netstat -tulpn 2>/dev/null | grep -q ":8000 "; then
    print_warning "Port 8000 is already in use!"
    print_status "Checking what's using port 8000..."
    netstat -tulpn 2>/dev/null | grep ":8000 "
    echo ""
    read -p "Kill existing process on port 8000? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Killing process on port 8000..."
        pkill -f "python3 main.py" 2>/dev/null || true
        sleep 2
    else
        print_error "Cannot start backend - port 8000 is busy"
        exit 1
    fi
fi

# Start backend server
print_header "STARTING BACKEND SERVER"

print_status "Changing to backend directory..."
cd web_interface/backend/app

print_status "Starting FastAPI server on port 8000..."
print_success "Backend server starting..."
echo ""
print_status "📊 Backend Information:"
echo "  🔧 API Server:    http://localhost:8000"
echo "  📡 WebSocket:     ws://localhost:8000/ws"
echo "  📖 API Docs:      http://localhost:8000/docs"
echo "  ❤️  Health Check: http://localhost:8000/health"
echo ""
print_status "🔗 ROS2 Integration:"
echo "  📡 Subscribes to: /scan, /odom, /map"
echo "  📤 Publishes to:  /goal_pose"
echo "  🌐 WebSocket:     Real-time data streaming"
echo ""
print_warning "Press Ctrl+C to stop the backend server"
echo ""

# Start the server
python3 main.py

# This line should not be reached unless the server exits
print_warning "Backend server has stopped"
