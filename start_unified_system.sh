#!/bin/bash

# =============================================================================
# UNIFIED SYSTEM STARTUP SCRIPT
# =============================================================================
# Starts both the backend and ROS bridge on the same Linux computer
# Connects to a remote ROS Noetic system running on a different computer
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    if [ "$CLEANUP_DONE" = "true" ]; then
        return
    fi
    CLEANUP_DONE=true

    print_header "SHUTTING DOWN UNIFIED SYSTEM"
    print_status "Stopping all processes..."
    
    # Kill backend process
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend server..."
        kill $BACKEND_PID 2>/dev/null || true
        wait $BACKEND_PID 2>/dev/null || true
    fi
    
    # Kill bridge process
    if [ ! -z "$BRIDGE_PID" ]; then
        print_status "Stopping ROS bridge..."
        kill $BRIDGE_PID 2>/dev/null || true
        wait $BRIDGE_PID 2>/dev/null || true
    fi
    
    print_success "All processes stopped"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Parse command line arguments
ROS_HOST="localhost"
BACKEND_PORT="8000"
FRONTEND_PORT="3000"

while [[ $# -gt 0 ]]; do
    case $1 in
        --ros-host)
            ROS_HOST="$2"
            shift 2
            ;;
        --backend-port)
            BACKEND_PORT="$2"
            shift 2
            ;;
        --frontend-port)
            FRONTEND_PORT="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --ros-host HOST       IP address of ROS Noetic system (default: localhost)"
            echo "  --backend-port PORT   Backend server port (default: 8000)"
            echo "  --frontend-port PORT  Frontend server port (default: 3000)"
            echo "  --help                Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Connect to local ROS"
            echo "  $0 --ros-host ************          # Connect to remote ROS"
            echo "  $0 --ros-host ************ --backend-port 3000  # Custom ports"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "UNIFIED SYSTEM STARTUP"
print_status "Starting backend + bridge on same Linux computer"
print_status "Connecting to ROS Noetic system at: $ROS_HOST"

# Check if we're in the right directory
if [ ! -f "web_interface/backend/app/main.py" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: web_interface/backend/app/main.py"
    exit 1
fi

# Environment checks
print_header "ENVIRONMENT CHECKS"

print_status "Checking Python environment..."

# Check Python dependencies for backend
BACKEND_DEPS=("fastapi" "uvicorn" "websockets" "pydantic" "psutil")
MISSING_BACKEND_DEPS=()

for dep in "${BACKEND_DEPS[@]}"; do
    if ! python3 -c "import $dep" 2>/dev/null; then
        MISSING_BACKEND_DEPS+=("$dep")
    fi
done

# Check Python dependencies for bridge
BRIDGE_DEPS=("requests")
MISSING_BRIDGE_DEPS=()

for dep in "${BRIDGE_DEPS[@]}"; do
    if ! python3 -c "import $dep" 2>/dev/null; then
        MISSING_BRIDGE_DEPS+=("$dep")
    fi
done

# Install missing dependencies
ALL_MISSING=("${MISSING_BACKEND_DEPS[@]}" "${MISSING_BRIDGE_DEPS[@]}")
if [ ${#ALL_MISSING[@]} -gt 0 ]; then
    print_warning "Missing Python dependencies:"
    for dep in "${ALL_MISSING[@]}"; do
        echo "  ❌ $dep"
    done
    print_status "Installing missing dependencies..."
    
    pip3 install fastapi uvicorn websockets pydantic psutil requests websocket-client
    print_success "Dependencies installed"
else
    print_success "All Python dependencies found"
fi

# Check if ports are available
print_status "Checking port availability..."

for port in $BACKEND_PORT $FRONTEND_PORT; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Port $port is already in use"
        print_status "Attempting to free port $port..."
        
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
        
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_error "Could not free port $port"
            exit 1
        else
            print_success "Port $port freed"
        fi
    else
        print_success "Port $port is available"
    fi
done

# Test connectivity to ROS system
print_status "Testing connectivity to ROS system..."
if ping -c 1 -W 3 $ROS_HOST >/dev/null 2>&1; then
    print_success "Can reach ROS system at $ROS_HOST"
else
    print_warning "Cannot ping ROS system at $ROS_HOST"
    print_warning "Bridge will start but may not connect until ROS system is available"
fi

print_success "Environment checks completed"

# Start the backend
print_header "STARTING BACKEND SERVER"

print_status "Starting backend server on port $BACKEND_PORT..."

cd web_interface/backend/app

# Set environment variable to indicate unified mode
export UNIFIED_MODE=true
export ROS_HOST=$ROS_HOST

python3 -m uvicorn main:app \
    --host "0.0.0.0" \
    --port "$BACKEND_PORT" \
    --reload \
    --log-level info &

BACKEND_PID=$!
cd ../../..

# Wait for backend to start
sleep 3

# Check if backend is running
if ! ps -p $BACKEND_PID > /dev/null; then
    print_error "Failed to start backend server!"
    exit 1
fi

print_success "Backend server started (PID: $BACKEND_PID)"

# Start the bridge
print_header "STARTING ROS BRIDGE"

print_status "Starting ROS bridge connecting to $ROS_HOST..."

# Create a temporary bridge config for this session
cat > /tmp/bridge_config_unified.yaml << EOF
# Unified system bridge configuration
backend_host: "localhost"
backend_port: $BACKEND_PORT
websocket_port: $BACKEND_PORT

# ROS system configuration
ros_host: "$ROS_HOST"
ros_port: 11311

# Update rate
update_rate: 10

# ROS topics (adjusted for your system)
ros_topics:
  scan: "/scan_forward"
  odom: "/odom_from_laser"
  cmd_vel: "/cmd_vel"
  goal: "/move_base_simple/goal"
  map: "/map"
  amcl_pose: "/amcl_pose"
  battery_state: "/battery_state"

# Network settings
network:
  connection_timeout: 10
  auto_reconnect: true
  reconnect_interval: 5
EOF

# Start the bridge
python3 ros_noetic_bridge.py \
    _backend_host:=localhost \
    _backend_port:=$BACKEND_PORT \
    _websocket_port:=$BACKEND_PORT \
    _ros_master_uri:=http://$ROS_HOST:11311 \
    _update_rate:=10 &

BRIDGE_PID=$!

# Wait for bridge to start
sleep 3

# Check if bridge is running
if ! ps -p $BRIDGE_PID > /dev/null; then
    print_error "Failed to start ROS bridge!"
    print_status "Backend will continue running without ROS connection"
    BRIDGE_PID=""
else
    print_success "ROS bridge started (PID: $BRIDGE_PID)"
fi

# Start frontend (optional)
print_header "STARTING FRONTEND (OPTIONAL)"

if [ -d "web_interface/frontend" ] && [ -f "web_interface/frontend/package.json" ]; then
    print_status "Frontend directory found. Starting frontend server..."
    
    cd web_interface/frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Start frontend server
    npm start &
    FRONTEND_PID=$!
    cd ../..
    
    sleep 3
    
    if ps -p $FRONTEND_PID > /dev/null; then
        print_success "Frontend server started (PID: $FRONTEND_PID)"
    else
        print_warning "Frontend server failed to start"
        FRONTEND_PID=""
    fi
else
    print_warning "Frontend not found or not configured"
    print_status "You can access the web interface through the backend at port $BACKEND_PORT"
    FRONTEND_PID=""
fi

# System status
print_header "UNIFIED SYSTEM STATUS"

print_success "🎉 Unified system is running!"

print_status "System components:"
echo "  ✅ Backend Server: Running (PID: $BACKEND_PID)"
if [ ! -z "$BRIDGE_PID" ]; then
    echo "  ✅ ROS Bridge: Running (PID: $BRIDGE_PID)"
else
    echo "  ❌ ROS Bridge: Failed to start"
fi
if [ ! -z "$FRONTEND_PID" ]; then
    echo "  ✅ Frontend Server: Running (PID: $FRONTEND_PID)"
else
    echo "  ⚠️  Frontend Server: Not running"
fi

print_status "Access points:"
echo "  🌐 Web Interface: http://localhost:$BACKEND_PORT"
if [ ! -z "$FRONTEND_PID" ]; then
    echo "  🎨 Frontend Dev: http://localhost:$FRONTEND_PORT"
fi
echo "  📚 API Docs: http://localhost:$BACKEND_PORT/docs"
echo "  ❤️  Health Check: http://localhost:$BACKEND_PORT/health"

print_status "ROS connection:"
echo "  🤖 ROS System: $ROS_HOST:11311"
if [ ! -z "$BRIDGE_PID" ]; then
    echo "  🔄 Bridge Status: Connected"
else
    echo "  🔄 Bridge Status: Disconnected"
fi

print_status "Network access:"
LOCAL_IP=$(hostname -I | awk '{print $1}')
echo "  📡 Local Access: http://localhost:$BACKEND_PORT"
echo "  🌐 Network Access: http://$LOCAL_IP:$BACKEND_PORT"

print_warning "Important notes:"
echo "  1. Ensure your ROS Noetic system is running at $ROS_HOST"
echo "  2. Required ROS topics: /scan, /odom, /cmd_vel"
echo "  3. Bridge will auto-reconnect if ROS system restarts"
echo "  4. Press Ctrl+C to stop all services"

print_status "System is ready! Press Ctrl+C to stop all services."

# Wait for processes
if [ ! -z "$FRONTEND_PID" ]; then
    wait $FRONTEND_PID $BACKEND_PID $BRIDGE_PID
else
    wait $BACKEND_PID $BRIDGE_PID
fi
