#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped, Twist
from nav_msgs.msg import OccupancyGrid
import json
import threading
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit
import time
import os
from pathlib import Path

class WebServer(Node):
    def __init__(self):
        super().__init__('web_server')
        
        # Declare parameters
        self.declare_parameter('port', 8080)
        self.declare_parameter('host', '0.0.0.0')
        
        # Get parameters
        self.port = self.get_parameter('port').get_parameter_value().integer_value
        self.host = self.get_parameter('host').get_parameter_value().string_value
        
        # ROS Publishers
        self.goal_pub = self.create_publisher(PoseStamped, '/move_base_simple/goal', 10)
        self.mission_pub = self.create_publisher(String, '/navigation/mission', 10)
        self.waypoint_save_pub = self.create_publisher(String, '/waypoint/save', 10)
        self.waypoint_goto_pub = self.create_publisher(String, '/waypoint/goto', 10)
        self.waypoint_delete_pub = self.create_publisher(String, '/waypoint/delete', 10)
        
        # ROS Subscribers
        self.nav_status_sub = self.create_subscription(
            String, '/navigation/status', self.nav_status_callback, 10)
        self.waypoint_list_sub = self.create_subscription(
            String, '/waypoint/list', self.waypoint_list_callback, 10)
        self.waypoint_status_sub = self.create_subscription(
            String, '/waypoint/status', self.waypoint_status_callback, 10)

        # Map and pose subscribers for real-time visualization
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.map_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)

        # Data storage
        self.latest_nav_status = {}
        self.waypoint_list = []
        self.latest_map_data = None
        self.latest_pose = None
        self.connected_clients = set()
        
        # Flask app setup - get path to templates from ROS2 share directory
        from ament_index_python.packages import get_package_share_directory
        try:
            package_share_dir = get_package_share_directory('web_interface')
            template_dir = os.path.join(package_share_dir, 'templates')
            static_dir = os.path.join(package_share_dir, 'static')
        except Exception as e:
            self.get_logger().warn(f'Could not find package share directory: {e}')
            # Fallback to relative path
            current_dir = Path(__file__).parent.parent
            template_dir = str(current_dir / 'templates')
            static_dir = str(current_dir / 'static')

        self.get_logger().info(f'Template directory: {template_dir}')
        self.get_logger().info(f'Template exists: {os.path.exists(template_dir)}')
        if os.path.exists(template_dir):
            self.get_logger().info(f'Template files: {os.listdir(template_dir)}')

        self.app = Flask(__name__,
                        template_folder=template_dir,
                        static_folder=static_dir)
        self.app.config['SECRET_KEY'] = 'indoor_nav_secret_key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        self.setup_routes()
        self.setup_socketio_events()
        
        # Start Flask server in separate thread
        self.server_thread = threading.Thread(
            target=self.run_server, daemon=True)
        self.server_thread.start()
        
        self.get_logger().info(f'Web server starting on {self.host}:{self.port}')
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            return render_template('index.html')
        
        @self.app.route('/api/status')
        def get_status():
            return jsonify(self.latest_nav_status)
        
        @self.app.route('/api/waypoints')
        def get_waypoints():
            return jsonify(self.waypoint_list)

        @self.app.route('/api/map_data')
        def get_map_data():
            return jsonify({
                'map': self.latest_map_data,
                'pose': self.latest_pose
            })

        @self.app.route('/api/robot_pose')
        def get_robot_pose():
            return jsonify(self.latest_pose)
        
        @self.app.route('/api/goto', methods=['POST'])
        def goto_waypoint():
            data = request.get_json()
            waypoint_name = data.get('waypoint')
            
            if waypoint_name:
                msg = String()
                msg.data = waypoint_name
                self.waypoint_goto_pub.publish(msg)
                return jsonify({'success': True, 'message': f'Navigating to {waypoint_name}'})
            else:
                return jsonify({'success': False, 'message': 'No waypoint specified'})
        
        @self.app.route('/api/save_waypoint', methods=['POST'])
        def save_waypoint():
            data = request.get_json()
            waypoint_name = data.get('name')
            description = data.get('description', '')
            
            if waypoint_name:
                waypoint_data = {
                    'name': waypoint_name,
                    'description': description
                }
                msg = String()
                msg.data = json.dumps(waypoint_data)
                self.waypoint_save_pub.publish(msg)
                return jsonify({'success': True, 'message': f'Waypoint {waypoint_name} saved'})
            else:
                return jsonify({'success': False, 'message': 'No waypoint name specified'})
        
        @self.app.route('/api/delete_waypoint', methods=['POST'])
        def delete_waypoint():
            data = request.get_json()
            waypoint_name = data.get('waypoint')
            
            if waypoint_name:
                msg = String()
                msg.data = waypoint_name
                self.waypoint_delete_pub.publish(msg)
                return jsonify({'success': True, 'message': f'Waypoint {waypoint_name} deleted'})
            else:
                return jsonify({'success': False, 'message': 'No waypoint specified'})
        
        @self.app.route('/api/set_goal', methods=['POST'])
        def set_goal():
            data = request.get_json()
            x = data.get('x')
            y = data.get('y')
            
            if x is not None and y is not None:
                goal = PoseStamped()
                goal.header.frame_id = 'map'
                goal.header.stamp = self.get_clock().now().to_msg()
                goal.pose.position.x = float(x)
                goal.pose.position.y = float(y)
                goal.pose.orientation.w = 1.0
                
                self.goal_pub.publish(goal)
                return jsonify({'success': True, 'message': f'Goal set to ({x}, {y})'})
            else:
                return jsonify({'success': False, 'message': 'Invalid coordinates'})
        
        @self.app.route('/api/create_mission', methods=['POST'])
        def create_mission():
            data = request.get_json()
            waypoint_names = data.get('waypoints', [])
            
            if waypoint_names:
                # Convert waypoint names to mission format
                mission_waypoints = []
                for name in waypoint_names:
                    # Find waypoint in current list
                    for wp in self.waypoint_list:
                        if wp.get('name') == name:
                            mission_waypoints.append({
                                'name': name,
                                'x': wp['x'],
                                'y': wp['y'],
                                'description': wp.get('description', '')
                            })
                            break
                
                mission_data = {
                    'mission_id': f'web_mission_{int(time.time())}',
                    'waypoints': mission_waypoints
                }
                
                msg = String()
                msg.data = json.dumps(mission_data)
                self.mission_pub.publish(msg)
                
                return jsonify({'success': True, 'message': f'Mission created with {len(mission_waypoints)} waypoints'})
            else:
                return jsonify({'success': False, 'message': 'No waypoints specified'})
    
    def setup_socketio_events(self):
        """Setup SocketIO events for real-time communication"""
        
        @self.socketio.on('connect')
        def handle_connect():
            self.connected_clients.add(request.sid)
            self.get_logger().info(f'Client connected: {request.sid}')
            
            # Send current status to new client
            emit('nav_status', self.latest_nav_status)
            emit('waypoint_list', self.waypoint_list)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            self.connected_clients.discard(request.sid)
            self.get_logger().info(f'Client disconnected: {request.sid}')
        
        @self.socketio.on('emergency_stop')
        def handle_emergency_stop():
            # Publish zero velocity
            from geometry_msgs.msg import Twist
            cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
            stop_cmd = Twist()
            cmd_vel_pub.publish(stop_cmd)
            
            emit('emergency_response', {'status': 'stopped'})
            self.get_logger().warn('Emergency stop triggered from web interface')
    
    def nav_status_callback(self, msg):
        """Handle navigation status updates"""
        try:
            self.latest_nav_status = json.loads(msg.data)
            
            # Broadcast to all connected clients
            if self.connected_clients:
                self.socketio.emit('nav_status', self.latest_nav_status)
                
        except json.JSONDecodeError as e:
            self.get_logger().error(f'Failed to parse navigation status: {e}')
    
    def waypoint_list_callback(self, msg):
        """Handle waypoint list updates"""
        try:
            data = json.loads(msg.data)
            self.waypoint_list = data.get('waypoints', [])
            
            # Broadcast to all connected clients
            if self.connected_clients:
                self.socketio.emit('waypoint_list', self.waypoint_list)
                
        except json.JSONDecodeError as e:
            self.get_logger().error(f'Failed to parse waypoint list: {e}')
    
    def waypoint_status_callback(self, msg):
        """Handle waypoint operation status"""
        try:
            status = json.loads(msg.data)
            
            # Broadcast to all connected clients
            if self.connected_clients:
                self.socketio.emit('waypoint_status', status)
                
        except json.JSONDecodeError as e:
            self.get_logger().error(f'Failed to parse waypoint status: {e}')
    
    def run_server(self):
        """Run the Flask server"""
        try:
            self.socketio.run(self.app, host=self.host, port=self.port, debug=False, allow_unsafe_werkzeug=True)
        except Exception as e:
            self.get_logger().error(f'Failed to start web server: {e}')

    def map_callback(self, msg):
        """Handle map updates for real-time visualization"""
        try:
            # Convert map data to a format suitable for web display
            map_data = {
                'width': msg.info.width,
                'height': msg.info.height,
                'resolution': msg.info.resolution,
                'origin': {
                    'x': msg.info.origin.position.x,
                    'y': msg.info.origin.position.y
                },
                'data': list(msg.data)  # Convert to list for JSON serialization
            }

            self.latest_map_data = map_data

            # Emit to web clients
            if hasattr(self, 'socketio'):
                self.socketio.emit('map_data', map_data)

        except Exception as e:
            self.get_logger().error(f'Error processing map data: {e}')

    def pose_callback(self, msg):
        """Handle pose updates for robot position"""
        try:
            pose_data = {
                'x': msg.pose.pose.position.x,
                'y': msg.pose.pose.position.y,
                'z': msg.pose.pose.position.z,
                'theta': self.quaternion_to_yaw(msg.pose.pose.orientation),
                'timestamp': time.time()
            }

            self.latest_pose = pose_data

            # Emit to web clients
            if hasattr(self, 'socketio'):
                self.socketio.emit('robot_pose', pose_data)

        except Exception as e:
            self.get_logger().error(f'Error processing pose data: {e}')

    def quaternion_to_yaw(self, quaternion):
        """Convert quaternion to yaw angle"""
        import math

        # Extract quaternion components
        x = quaternion.x
        y = quaternion.y
        z = quaternion.z
        w = quaternion.w

        # Convert to yaw (rotation around z-axis)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = math.atan2(siny_cosp, cosy_cosp)

        return yaw

def main(args=None):
    rclpy.init(args=args)
    
    try:
        web_server = WebServer()
        rclpy.spin(web_server)
    except KeyboardInterrupt:
        pass
    finally:
        if 'web_server' in locals():
            web_server.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
