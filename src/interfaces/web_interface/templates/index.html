<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indoor Navigation Control</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-panel {
            grid-column: 1 / -1;
        }
        h1, h2 {
            color: #333;
            margin-top: 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-idle { background-color: #gray; }
        .status-navigating { background-color: #4CAF50; }
        .status-obstacle_avoidance { background-color: #FF9800; }
        .status-emergency_stop { background-color: #F44336; }
        .status-paused { background-color: #2196F3; }
        
        .waypoint-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .waypoint-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .waypoint-item:hover {
            background-color: #f9f9f9;
        }
        .waypoint-info {
            flex-grow: 1;
        }
        .waypoint-name {
            font-weight: bold;
            color: #333;
        }
        .waypoint-coords {
            font-size: 0.9em;
            color: #666;
        }
        .waypoint-actions {
            display: flex;
            gap: 5px;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.danger {
            background-color: #F44336;
        }
        button.danger:hover {
            background-color: #da190b;
        }
        button.warning {
            background-color: #FF9800;
        }
        button.warning:hover {
            background-color: #e68900;
        }
        
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        .coordinate-input {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .coordinate-input input {
            flex: 1;
            margin-bottom: 0;
        }
        
        .mission-builder {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }
        .mission-waypoint {
            background-color: #e3f2fd;
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .emergency-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #F44336;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            z-index: 1000;
        }
        .emergency-button:hover {
            background-color: #da190b;
        }
        
        .log-panel {
            grid-column: 1 / -1;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
        }
        .log-timestamp {
            color: #666;
        }
        .log-info { color: #2196F3; }
        .log-warning { color: #FF9800; }
        .log-error { color: #F44336; }
    </style>
</head>
<body>
    <button class="emergency-button" onclick="emergencyStop()">🛑 EMERGENCY STOP</button>
    
    <div class="container">
        <!-- Status Panel -->
        <div class="panel status-panel">
            <h1>🤖 Indoor Navigation Control</h1>
            <div id="status-display">
                <h3>Robot Status: <span id="robot-state">Unknown</span></h3>
                <p><strong>Position:</strong> <span id="robot-position">Unknown</span></p>
                <p><strong>Goal:</strong> <span id="robot-goal">None</span></p>
                <p><strong>Mission Progress:</strong> <span id="mission-progress">No active mission</span></p>
            </div>
        </div>
        
        <!-- Waypoint Management -->
        <div class="panel">
            <h2>📍 Waypoint Management</h2>
            
            <div>
                <h3>Save Current Position</h3>
                <input type="text" id="waypoint-name" placeholder="Waypoint name">
                <input type="text" id="waypoint-description" placeholder="Description (optional)">
                <button onclick="saveWaypoint()">💾 Save Current Position</button>
            </div>
            
            <div style="margin-top: 20px;">
                <h3>Set Manual Goal</h3>
                <div class="coordinate-input">
                    <input type="number" id="goal-x" placeholder="X coordinate" step="0.1">
                    <input type="number" id="goal-y" placeholder="Y coordinate" step="0.1">
                </div>
                <button onclick="setManualGoal()">🎯 Set Goal</button>
            </div>
            
            <div style="margin-top: 20px;">
                <h3>Available Waypoints</h3>
                <div id="waypoint-list" class="waypoint-list">
                    <div style="padding: 20px; text-align: center; color: #666;">
                        Loading waypoints...
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Real-time Map Display -->
        <div class="panel">
            <h2>🗺️ Real-time Map</h2>
            <div style="border: 1px solid #ddd; border-radius: 4px; overflow: hidden;">
                <canvas id="map-canvas" width="400" height="400" style="display: block; background-color: #f0f0f0; cursor: crosshair;"></canvas>
            </div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                <p>🤖 Blue dot = Robot position | 🎯 Red dot = Goal | 🗺️ Black = Obstacles</p>
                <p>Click on map to set goal</p>
            </div>
        </div>

        <!-- Mission Control -->
        <div class="panel">
            <h2>🎮 Mission Control</h2>
            
            <div class="mission-builder">
                <h3>Create Mission</h3>
                <p>Select waypoints to create a multi-point mission:</p>
                <div id="mission-waypoints"></div>
                <button onclick="startMission()" style="margin-top: 10px;">🚀 Start Mission</button>
                <button onclick="clearMission()" class="warning" style="margin-top: 10px;">🗑️ Clear Mission</button>
            </div>
            
            <div style="margin-top: 20px;">
                <h3>Quick Actions</h3>
                <button onclick="pauseNavigation()" class="warning">⏸️ Pause</button>
                <button onclick="resumeNavigation()">▶️ Resume</button>
                <button onclick="cancelNavigation()" class="danger">❌ Cancel</button>
            </div>
        </div>
        
        <!-- Activity Log -->
        <div class="panel log-panel">
            <h3>📋 Activity Log</h3>
            <div id="activity-log"></div>
        </div>
    </div>

    <script>
        // Socket.IO connection
        const socket = io();
        
        // Global variables
        let currentWaypoints = [];
        let missionWaypoints = [];
        let mapData = null;
        let robotPose = null;
        let currentGoal = null;
        let mapCanvas = null;
        let mapCtx = null;
        
        // Socket event handlers
        socket.on('connect', function() {
            addLog('Connected to robot', 'info');
        });
        
        socket.on('disconnect', function() {
            addLog('Disconnected from robot', 'error');
        });
        
        socket.on('nav_status', function(status) {
            updateStatus(status);
        });
        
        socket.on('waypoint_list', function(waypoints) {
            currentWaypoints = waypoints;
            updateWaypointList();
        });
        
        socket.on('waypoint_status', function(status) {
            addLog(`Waypoint ${status.action}: ${status.waypoint} - ${status.success ? 'Success' : 'Failed'}`,
                   status.success ? 'info' : 'error');
        });

        // Map data events
        socket.on('map_data', function(data) {
            mapData = data;
            drawMap();
        });

        socket.on('robot_pose', function(pose) {
            robotPose = pose;
            drawMap();
        });

        socket.on('goal_update', function(goal) {
            currentGoal = goal;
            drawMap();
        });
        
        // Update status display
        function updateStatus(status) {
            const stateElement = document.getElementById('robot-state');
            const positionElement = document.getElementById('robot-position');
            const goalElement = document.getElementById('robot-goal');
            const progressElement = document.getElementById('mission-progress');
            
            // Update state with indicator
            stateElement.innerHTML = `<span class="status-indicator status-${status.state}"></span>${status.state}`;
            
            // Update position
            if (status.current_pose) {
                positionElement.textContent = `(${status.current_pose.x.toFixed(2)}, ${status.current_pose.y.toFixed(2)})`;
            } else {
                positionElement.textContent = 'Unknown';
            }
            
            // Update goal
            if (status.current_goal) {
                goalElement.textContent = `(${status.current_goal.x.toFixed(2)}, ${status.current_goal.y.toFixed(2)})`;
            } else {
                goalElement.textContent = 'None';
            }
            
            // Update mission progress
            if (status.mission_progress) {
                progressElement.textContent = `${status.mission_progress.current_waypoint}/${status.mission_progress.total_waypoints}`;
            } else {
                progressElement.textContent = 'No active mission';
            }
        }
        
        // Update waypoint list
        function updateWaypointList() {
            const listElement = document.getElementById('waypoint-list');
            
            if (currentWaypoints.length === 0) {
                listElement.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">No waypoints saved</div>';
                return;
            }
            
            listElement.innerHTML = '';
            
            currentWaypoints.forEach(waypoint => {
                const item = document.createElement('div');
                item.className = 'waypoint-item';
                
                item.innerHTML = `
                    <div class="waypoint-info">
                        <div class="waypoint-name">${waypoint.name}</div>
                        <div class="waypoint-coords">(${waypoint.x.toFixed(2)}, ${waypoint.y.toFixed(2)})</div>
                        ${waypoint.description ? `<div style="font-size: 0.8em; color: #888;">${waypoint.description}</div>` : ''}
                        <div style="font-size: 0.8em; color: #888;">Visited: ${waypoint.visit_count} times</div>
                    </div>
                    <div class="waypoint-actions">
                        <button onclick="goToWaypoint('${waypoint.name}')">Go</button>
                        <button onclick="addToMission('${waypoint.name}')" class="warning">+Mission</button>
                        <button onclick="deleteWaypoint('${waypoint.name}')" class="danger">Delete</button>
                    </div>
                `;
                
                listElement.appendChild(item);
            });
        }
        
        // Waypoint functions
        function saveWaypoint() {
            const name = document.getElementById('waypoint-name').value.trim();
            const description = document.getElementById('waypoint-description').value.trim();
            
            if (!name) {
                alert('Please enter a waypoint name');
                return;
            }
            
            fetch('/api/save_waypoint', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({name: name, description: description})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog(data.message, 'info');
                    document.getElementById('waypoint-name').value = '';
                    document.getElementById('waypoint-description').value = '';
                } else {
                    addLog(data.message, 'error');
                }
            });
        }
        
        function goToWaypoint(name) {
            fetch('/api/goto', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({waypoint: name})
            })
            .then(response => response.json())
            .then(data => {
                addLog(data.message, data.success ? 'info' : 'error');
            });
        }
        
        function deleteWaypoint(name) {
            if (confirm(`Are you sure you want to delete waypoint "${name}"?`)) {
                fetch('/api/delete_waypoint', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({waypoint: name})
                })
                .then(response => response.json())
                .then(data => {
                    addLog(data.message, data.success ? 'info' : 'error');
                });
            }
        }
        
        function setManualGoal() {
            const x = parseFloat(document.getElementById('goal-x').value);
            const y = parseFloat(document.getElementById('goal-y').value);
            
            if (isNaN(x) || isNaN(y)) {
                alert('Please enter valid coordinates');
                return;
            }
            
            fetch('/api/set_goal', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({x: x, y: y})
            })
            .then(response => response.json())
            .then(data => {
                addLog(data.message, data.success ? 'info' : 'error');
                if (data.success) {
                    document.getElementById('goal-x').value = '';
                    document.getElementById('goal-y').value = '';
                }
            });
        }
        
        // Mission functions
        function addToMission(waypointName) {
            if (!missionWaypoints.includes(waypointName)) {
                missionWaypoints.push(waypointName);
                updateMissionDisplay();
                addLog(`Added ${waypointName} to mission`, 'info');
            }
        }
        
        function removeFromMission(waypointName) {
            const index = missionWaypoints.indexOf(waypointName);
            if (index > -1) {
                missionWaypoints.splice(index, 1);
                updateMissionDisplay();
                addLog(`Removed ${waypointName} from mission`, 'info');
            }
        }
        
        function updateMissionDisplay() {
            const container = document.getElementById('mission-waypoints');
            container.innerHTML = '';
            
            if (missionWaypoints.length === 0) {
                container.innerHTML = '<div style="color: #666; font-style: italic;">No waypoints in mission</div>';
                return;
            }
            
            missionWaypoints.forEach((name, index) => {
                const item = document.createElement('div');
                item.className = 'mission-waypoint';
                item.innerHTML = `
                    <span>${index + 1}. ${name}</span>
                    <button onclick="removeFromMission('${name}')" class="danger" style="padding: 2px 8px; font-size: 12px;">Remove</button>
                `;
                container.appendChild(item);
            });
        }
        
        function startMission() {
            if (missionWaypoints.length === 0) {
                alert('Please add waypoints to the mission first');
                return;
            }
            
            fetch('/api/create_mission', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({waypoints: missionWaypoints})
            })
            .then(response => response.json())
            .then(data => {
                addLog(data.message, data.success ? 'info' : 'error');
                if (data.success) {
                    clearMission();
                }
            });
        }
        
        function clearMission() {
            missionWaypoints = [];
            updateMissionDisplay();
            addLog('Mission cleared', 'info');
        }
        
        // Control functions
        function emergencyStop() {
            socket.emit('emergency_stop');
            addLog('EMERGENCY STOP ACTIVATED', 'error');
        }
        
        function pauseNavigation() {
            addLog('Pause navigation requested', 'warning');
            // Implementation depends on navigation system
        }
        
        function resumeNavigation() {
            addLog('Resume navigation requested', 'info');
            // Implementation depends on navigation system
        }
        
        function cancelNavigation() {
            if (confirm('Are you sure you want to cancel current navigation?')) {
                addLog('Navigation cancelled', 'warning');
                // Implementation depends on navigation system
            }
        }

        // Map drawing functions
        function initializeMap() {
            mapCanvas = document.getElementById('map-canvas');
            if (!mapCanvas) return;

            mapCtx = mapCanvas.getContext('2d');

            // Add click handler for setting goals
            mapCanvas.addEventListener('click', function(event) {
                const rect = mapCanvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                // Convert canvas coordinates to map coordinates
                const mapX = (x / mapCanvas.width) * 10 - 5; // Assuming 10x10 meter map
                const mapY = 5 - (y / mapCanvas.height) * 10;

                setGoalFromMap(mapX, mapY);
            });

            drawMap();
        }

        function drawMap() {
            if (!mapCtx) return;

            // Clear canvas with light background
            mapCtx.fillStyle = '#f8f9fa';
            mapCtx.fillRect(0, 0, mapCanvas.width, mapCanvas.height);

            // Draw grid
            mapCtx.strokeStyle = '#e0e0e0';
            mapCtx.lineWidth = 1;
            for (let i = 0; i <= 20; i++) {
                const x = (i / 20) * mapCanvas.width;
                const y = (i / 20) * mapCanvas.height;

                mapCtx.beginPath();
                mapCtx.moveTo(x, 0);
                mapCtx.lineTo(x, mapCanvas.height);
                mapCtx.stroke();

                mapCtx.beginPath();
                mapCtx.moveTo(0, y);
                mapCtx.lineTo(mapCanvas.width, y);
                mapCtx.stroke();
            }

            // Draw robot position (ALWAYS VISIBLE - bright and clear)
            const robotX = mapCanvas.width / 2;  // Center for now
            const robotY = mapCanvas.height / 2;

            // Draw robot with bright outline for visibility
            mapCtx.fillStyle = '#FF4444';  // Bright red for visibility
            mapCtx.strokeStyle = '#FFFFFF';  // White outline
            mapCtx.lineWidth = 3;
            mapCtx.beginPath();
            mapCtx.arc(robotX, robotY, 12, 0, 2 * Math.PI);
            mapCtx.fill();
            mapCtx.stroke();

            // Draw robot orientation arrow (bigger and brighter)
            mapCtx.strokeStyle = '#FF4444';
            mapCtx.lineWidth = 4;
            mapCtx.beginPath();
            mapCtx.moveTo(robotX, robotY);
            mapCtx.lineTo(robotX + 20, robotY);
            mapCtx.stroke();

            // Add robot label
            mapCtx.fillStyle = '#000000';
            mapCtx.font = 'bold 12px Arial';
            mapCtx.fillText('ROBOT', robotX - 20, robotY - 20);

            // Draw goal if set
            if (currentGoal) {
                const goalX = ((currentGoal.x + 5) / 10) * mapCanvas.width;
                const goalY = ((5 - currentGoal.y) / 10) * mapCanvas.height;

                mapCtx.fillStyle = '#F44336';
                mapCtx.beginPath();
                mapCtx.arc(goalX, goalY, 6, 0, 2 * Math.PI);
                mapCtx.fill();

                // Draw line from robot to goal
                mapCtx.strokeStyle = '#F44336';
                mapCtx.lineWidth = 2;
                mapCtx.setLineDash([5, 5]);
                mapCtx.beginPath();
                mapCtx.moveTo(robotX, robotY);
                mapCtx.lineTo(goalX, goalY);
                mapCtx.stroke();
                mapCtx.setLineDash([]);
            }

            // Draw waypoints
            currentWaypoints.forEach((waypoint, index) => {
                const x = ((waypoint.x + 5) / 10) * mapCanvas.width;
                const y = ((5 - waypoint.y) / 10) * mapCanvas.height;

                mapCtx.fillStyle = '#4CAF50';
                mapCtx.beginPath();
                mapCtx.arc(x, y, 4, 0, 2 * Math.PI);
                mapCtx.fill();

                // Draw waypoint number
                mapCtx.fillStyle = '#333';
                mapCtx.font = '12px Arial';
                mapCtx.fillText((index + 1).toString(), x + 8, y - 8);
            });
        }

        function setGoalFromMap(x, y) {
            currentGoal = {x: x, y: y};
            drawMap();

            fetch('/api/set_goal', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({x: x, y: y})
            })
            .then(response => response.json())
            .then(data => {
                addLog(`Goal set: (${x.toFixed(2)}, ${y.toFixed(2)})`, data.success ? 'info' : 'error');
            })
            .catch(error => {
                addLog(`Failed to set goal: ${error}`, 'error');
            });
        }

        // Logging function
        function addLog(message, type = 'info') {
            const logElement = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-${type}">${message}</span>`;
            
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            
            // Keep only last 100 entries
            while (logElement.children.length > 100) {
                logElement.removeChild(logElement.firstChild);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateMissionDisplay();
            initializeMap();
            addLog('Web interface loaded with real-time map', 'info');

            // Update map every 2 seconds
            setInterval(drawMap, 2000);
        });
    </script>
</body>
</html>
