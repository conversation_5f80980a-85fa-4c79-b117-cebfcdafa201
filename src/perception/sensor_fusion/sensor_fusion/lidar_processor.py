#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan, PointCloud2
from geometry_msgs.msg import Point32
from std_msgs.msg import Float32MultiArray, <PERSON>er
import numpy as np
import math

class LiDARProcessor(Node):
    def __init__(self):
        super().__init__('lidar_processor')
        
        # Parameters
        self.declare_parameter('min_range', 0.15)
        self.declare_parameter('max_range', 12.0)
        self.declare_parameter('angle_filter_min', -math.pi)
        self.declare_parameter('angle_filter_max', math.pi)
        self.declare_parameter('cluster_tolerance', 0.1)
        self.declare_parameter('min_cluster_size', 3)
        self.declare_parameter('max_cluster_size', 1000)
        
        self.min_range = self.get_parameter('min_range').value
        self.max_range = self.get_parameter('max_range').value
        self.angle_filter_min = self.get_parameter('angle_filter_min').value
        self.angle_filter_max = self.get_parameter('angle_filter_max').value
        self.cluster_tolerance = self.get_parameter('cluster_tolerance').value
        self.min_cluster_size = self.get_parameter('min_cluster_size').value
        self.max_cluster_size = self.get_parameter('max_cluster_size').value
        
        # Subscribers
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        
        # Publishers
        self.filtered_scan_pub = self.create_publisher(
            LaserScan, '/scan_filtered', 10)
        self.obstacles_pub = self.create_publisher(
            Float32MultiArray, '/lidar/obstacles', 10)
        self.clusters_pub = self.create_publisher(
            Float32MultiArray, '/lidar/clusters', 10)
        
        self.get_logger().info('LiDAR Processor initialized')
        
    def scan_callback(self, msg):
        """Process incoming laser scan"""
        # Filter scan data
        filtered_scan = self.filter_scan(msg)
        
        # Extract obstacles
        obstacles = self.extract_obstacles(filtered_scan)
        
        # Cluster obstacles
        clusters = self.cluster_obstacles(obstacles)
        
        # Publish results
        self.filtered_scan_pub.publish(filtered_scan)
        self.publish_obstacles(obstacles)
        self.publish_clusters(clusters)
        
    def filter_scan(self, scan):
        """Filter laser scan data"""
        filtered_scan = LaserScan()
        filtered_scan.header = scan.header
        filtered_scan.angle_min = scan.angle_min
        filtered_scan.angle_max = scan.angle_max
        filtered_scan.angle_increment = scan.angle_increment
        filtered_scan.time_increment = scan.time_increment
        filtered_scan.scan_time = scan.scan_time
        filtered_scan.range_min = max(scan.range_min, self.min_range)
        filtered_scan.range_max = min(scan.range_max, self.max_range)
        
        filtered_ranges = []
        filtered_intensities = []
        
        for i, range_val in enumerate(scan.ranges):
            angle = scan.angle_min + i * scan.angle_increment
            
            # Filter by angle
            if angle < self.angle_filter_min or angle > self.angle_filter_max:
                filtered_ranges.append(float('inf'))
                if scan.intensities:
                    filtered_intensities.append(0.0)
                continue
                
            # Filter by range
            if (math.isnan(range_val) or math.isinf(range_val) or 
                range_val < self.min_range or range_val > self.max_range):
                filtered_ranges.append(float('inf'))
                if scan.intensities:
                    filtered_intensities.append(0.0)
                continue
                
            # Apply median filter for noise reduction
            filtered_range = self.median_filter(scan.ranges, i, window_size=3)
            filtered_ranges.append(filtered_range)
            
            if scan.intensities:
                filtered_intensities.append(scan.intensities[i])
                
        filtered_scan.ranges = filtered_ranges
        filtered_scan.intensities = filtered_intensities
        
        return filtered_scan
        
    def median_filter(self, ranges, index, window_size=3):
        """Apply median filter to reduce noise"""
        half_window = window_size // 2
        start_idx = max(0, index - half_window)
        end_idx = min(len(ranges), index + half_window + 1)
        
        window_values = []
        for i in range(start_idx, end_idx):
            if not (math.isnan(ranges[i]) or math.isinf(ranges[i])):
                window_values.append(ranges[i])
                
        if len(window_values) == 0:
            return float('inf')
            
        return np.median(window_values)
        
    def extract_obstacles(self, scan):
        """Extract obstacle points from laser scan"""
        obstacles = []
        
        for i, range_val in enumerate(scan.ranges):
            if math.isnan(range_val) or math.isinf(range_val):
                continue
                
            if range_val < scan.range_min or range_val > scan.range_max:
                continue
                
            angle = scan.angle_min + i * scan.angle_increment
            
            # Convert to Cartesian coordinates
            x = range_val * math.cos(angle)
            y = range_val * math.sin(angle)
            
            obstacles.append([x, y, range_val, angle])
            
        return obstacles
        
    def cluster_obstacles(self, obstacles):
        """Cluster nearby obstacle points"""
        if len(obstacles) == 0:
            return []
            
        clusters = []
        used = [False] * len(obstacles)
        
        for i, obstacle in enumerate(obstacles):
            if used[i]:
                continue
                
            # Start new cluster
            cluster = [obstacle]
            used[i] = True
            
            # Find nearby points
            for j, other_obstacle in enumerate(obstacles):
                if used[j] or i == j:
                    continue
                    
                # Calculate distance
                dx = obstacle[0] - other_obstacle[0]
                dy = obstacle[1] - other_obstacle[1]
                distance = math.sqrt(dx*dx + dy*dy)
                
                if distance <= self.cluster_tolerance:
                    cluster.append(other_obstacle)
                    used[j] = True
                    
            # Check cluster size
            if self.min_cluster_size <= len(cluster) <= self.max_cluster_size:
                clusters.append(cluster)
                
        return clusters
        
    def publish_obstacles(self, obstacles):
        """Publish obstacle points"""
        msg = Float32MultiArray()
        
        data = []
        for obstacle in obstacles:
            data.extend(obstacle)  # [x, y, range, angle]
            
        msg.data = data
        self.obstacles_pub.publish(msg)
        
    def publish_clusters(self, clusters):
        """Publish clustered obstacles"""
        msg = Float32MultiArray()
        
        data = []
        for cluster in clusters:
            # Calculate cluster center
            x_sum = sum(point[0] for point in cluster)
            y_sum = sum(point[1] for point in cluster)
            cluster_size = len(cluster)
            
            center_x = x_sum / cluster_size
            center_y = y_sum / cluster_size
            
            # Calculate cluster radius
            max_dist = 0
            for point in cluster:
                dist = math.sqrt((point[0] - center_x)**2 + (point[1] - center_y)**2)
                max_dist = max(max_dist, dist)
                
            # Add cluster info: [center_x, center_y, radius, size]
            data.extend([center_x, center_y, max_dist, float(cluster_size)])
            
        msg.data = data
        self.clusters_pub.publish(msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = LiDARProcessor()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
