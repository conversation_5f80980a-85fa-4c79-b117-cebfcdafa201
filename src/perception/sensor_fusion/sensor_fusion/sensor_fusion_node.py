#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
from nav_msgs.msg import OccupancyGrid, Odometry
from geometry_msgs.msg import PoseWithCovarianceStamped
from std_msgs.msg import <PERSON><PERSON>, Float32MultiArray
import numpy as np
import tf2_ros
import tf2_geometry_msgs
from tf2_ros import TransformException
import math
import time

class SensorFusionNode(Node):
    def __init__(self):
        super().__init__('sensor_fusion_node')
        
        # Parameters
        self.declare_parameter('map_resolution', 0.05)  # meters per pixel
        self.declare_parameter('map_width', 400)        # pixels
        self.declare_parameter('map_height', 400)       # pixels
        self.declare_parameter('lidar_max_range', 12.0)
        self.declare_parameter('ultrasonic_max_range', 4.0)
        self.declare_parameter('update_rate', 10.0)
        
        self.map_resolution = self.get_parameter('map_resolution').value
        self.map_width = self.get_parameter('map_width').value
        self.map_height = self.get_parameter('map_height').value
        self.lidar_max_range = self.get_parameter('lidar_max_range').value
        self.ultrasonic_max_range = self.get_parameter('ultrasonic_max_range').value
        self.update_rate = self.get_parameter('update_rate').value
        
        # Initialize occupancy grid
        self.occupancy_grid = np.full((self.map_height, self.map_width), -1, dtype=np.int8)
        self.confidence_map = np.zeros((self.map_height, self.map_width), dtype=np.float32)
        
        # Map origin (center of the map)
        self.map_origin_x = -self.map_width * self.map_resolution / 2.0
        self.map_origin_y = -self.map_height * self.map_resolution / 2.0
        
        # Sensor data storage
        self.latest_lidar = None
        self.latest_ultrasonic = None
        self.latest_odom = None
        
        # TF buffer and listener
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        
        # Subscribers
        self.lidar_sub = self.create_subscription(
            LaserScan, '/scan', self.lidar_callback, 10)
        self.ultrasonic_sub = self.create_subscription(
            Float32MultiArray, '/ultrasonic/distances', self.ultrasonic_callback, 10)
        self.odom_sub = self.create_subscription(
            Odometry, '/odom', self.odom_callback, 10)
        
        # Publishers
        self.map_pub = self.create_publisher(OccupancyGrid, '/map', 10)
        self.fused_obstacles_pub = self.create_publisher(
            Float32MultiArray, '/perception/fused_obstacles', 10)
        
        # Timer for map updates
        self.map_timer = self.create_timer(1.0 / self.update_rate, self.update_map)
        
        self.get_logger().info('Sensor Fusion Node initialized')
        self.get_logger().info(f'Map size: {self.map_width}x{self.map_height}, resolution: {self.map_resolution}m')
        
    def lidar_callback(self, msg):
        """Process LiDAR data"""
        self.latest_lidar = msg
        
    def ultrasonic_callback(self, msg):
        """Process ultrasonic sensor data"""
        self.latest_ultrasonic = msg
        
    def odom_callback(self, msg):
        """Process odometry data"""
        self.latest_odom = msg
        
    def world_to_map(self, x, y):
        """Convert world coordinates to map coordinates"""
        map_x = int((x - self.map_origin_x) / self.map_resolution)
        map_y = int((y - self.map_origin_y) / self.map_resolution)
        return map_x, map_y
        
    def map_to_world(self, map_x, map_y):
        """Convert map coordinates to world coordinates"""
        x = map_x * self.map_resolution + self.map_origin_x
        y = map_y * self.map_resolution + self.map_origin_y
        return x, y
        
    def is_valid_map_coord(self, map_x, map_y):
        """Check if map coordinates are valid"""
        return 0 <= map_x < self.map_width and 0 <= map_y < self.map_height
        
    def process_lidar_data(self, robot_x, robot_y, robot_yaw):
        """Process LiDAR data and update occupancy grid"""
        if self.latest_lidar is None:
            return
            
        lidar = self.latest_lidar
        
        for i, range_val in enumerate(lidar.ranges):
            if math.isnan(range_val) or math.isinf(range_val):
                continue
                
            if range_val < lidar.range_min or range_val > min(lidar.range_max, self.lidar_max_range):
                continue
                
            # Calculate angle
            angle = lidar.angle_min + i * lidar.angle_increment + robot_yaw
            
            # Calculate obstacle position
            obs_x = robot_x + range_val * math.cos(angle)
            obs_y = robot_y + range_val * math.sin(angle)
            
            # Convert to map coordinates
            map_x, map_y = self.world_to_map(obs_x, obs_y)
            
            if self.is_valid_map_coord(map_x, map_y):
                # Mark as occupied
                self.occupancy_grid[map_y, map_x] = 100
                self.confidence_map[map_y, map_x] += 0.1
                
            # Mark free space along the ray
            self.mark_free_space(robot_x, robot_y, obs_x, obs_y)
            
    def process_ultrasonic_data(self, robot_x, robot_y, robot_yaw):
        """Process ultrasonic sensor data"""
        if self.latest_ultrasonic is None or len(self.latest_ultrasonic.data) < 4:
            return
            
        # Ultrasonic sensor positions relative to robot (front, left, right, back)
        sensor_angles = [0.0, math.pi/2, -math.pi/2, math.pi]  # radians
        sensor_positions = [(0.15, 0.0), (0.0, 0.15), (0.0, -0.15), (-0.15, 0.0)]
        
        for i, distance in enumerate(self.latest_ultrasonic.data):
            if distance <= 0 or distance > self.ultrasonic_max_range:
                continue
                
            # Calculate sensor position in world frame
            sensor_local_x, sensor_local_y = sensor_positions[i]
            sensor_world_x = robot_x + sensor_local_x * math.cos(robot_yaw) - sensor_local_y * math.sin(robot_yaw)
            sensor_world_y = robot_y + sensor_local_x * math.sin(robot_yaw) + sensor_local_y * math.cos(robot_yaw)
            
            # Calculate obstacle position
            angle = robot_yaw + sensor_angles[i]
            obs_x = sensor_world_x + distance * math.cos(angle)
            obs_y = sensor_world_y + distance * math.sin(angle)
            
            # Convert to map coordinates
            map_x, map_y = self.world_to_map(obs_x, obs_y)
            
            if self.is_valid_map_coord(map_x, map_y):
                # Mark as occupied (with lower confidence than LiDAR)
                current_val = self.occupancy_grid[map_y, map_x]
                if current_val == -1:  # Unknown
                    self.occupancy_grid[map_y, map_x] = 80
                elif current_val < 100:  # Increase confidence
                    self.occupancy_grid[map_y, map_x] = min(100, current_val + 20)
                    
                self.confidence_map[map_y, map_x] += 0.05
                
    def mark_free_space(self, start_x, start_y, end_x, end_y):
        """Mark free space along a ray using Bresenham's line algorithm"""
        start_map_x, start_map_y = self.world_to_map(start_x, start_y)
        end_map_x, end_map_y = self.world_to_map(end_x, end_y)
        
        # Bresenham's line algorithm
        dx = abs(end_map_x - start_map_x)
        dy = abs(end_map_y - start_map_y)
        
        x_step = 1 if start_map_x < end_map_x else -1
        y_step = 1 if start_map_y < end_map_y else -1
        
        error = dx - dy
        x, y = start_map_x, start_map_y
        
        while True:
            if self.is_valid_map_coord(x, y):
                if self.occupancy_grid[y, x] == -1:  # Unknown
                    self.occupancy_grid[y, x] = 0  # Free
                elif self.occupancy_grid[y, x] > 0 and self.occupancy_grid[y, x] < 50:
                    self.occupancy_grid[y, x] = max(0, self.occupancy_grid[y, x] - 5)
                    
            if x == end_map_x and y == end_map_y:
                break
                
            error2 = 2 * error
            if error2 > -dy:
                error -= dy
                x += x_step
            if error2 < dx:
                error += dx
                y += y_step
                
    def get_robot_pose(self):
        """Get robot pose from TF or odometry"""
        try:
            # Try to get transform from map to base_link
            transform = self.tf_buffer.lookup_transform(
                'map', 'base_link', rclpy.time.Time())
            
            x = transform.transform.translation.x
            y = transform.transform.translation.y
            
            # Convert quaternion to yaw
            quat = transform.transform.rotation
            yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                           1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
            
            return x, y, yaw
            
        except TransformException:
            # Fallback to odometry
            if self.latest_odom is not None:
                pose = self.latest_odom.pose.pose
                x = pose.position.x
                y = pose.position.y
                
                quat = pose.orientation
                yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                               1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
                
                return x, y, yaw
                
        return None, None, None
        
    def update_map(self):
        """Update and publish the occupancy grid map"""
        robot_x, robot_y, robot_yaw = self.get_robot_pose()
        
        if robot_x is None:
            return
            
        # Process sensor data
        self.process_lidar_data(robot_x, robot_y, robot_yaw)
        self.process_ultrasonic_data(robot_x, robot_y, robot_yaw)
        
        # Publish occupancy grid
        self.publish_occupancy_grid()
        
        # Publish fused obstacle data
        self.publish_fused_obstacles()
        
    def publish_occupancy_grid(self):
        """Publish the occupancy grid map"""
        grid_msg = OccupancyGrid()
        
        # Header
        grid_msg.header.stamp = self.get_clock().now().to_msg()
        grid_msg.header.frame_id = 'map'
        
        # Map metadata
        grid_msg.info.resolution = self.map_resolution
        grid_msg.info.width = self.map_width
        grid_msg.info.height = self.map_height
        grid_msg.info.origin.position.x = self.map_origin_x
        grid_msg.info.origin.position.y = self.map_origin_y
        grid_msg.info.origin.position.z = 0.0
        grid_msg.info.origin.orientation.w = 1.0
        
        # Flatten the occupancy grid (row-major order)
        grid_msg.data = self.occupancy_grid.flatten().tolist()
        
        self.map_pub.publish(grid_msg)
        
    def publish_fused_obstacles(self):
        """Publish fused obstacle information"""
        obstacles = Float32MultiArray()
        
        # Find obstacles in the map
        obstacle_list = []
        for y in range(self.map_height):
            for x in range(self.map_width):
                if self.occupancy_grid[y, x] > 50:  # Occupied
                    world_x, world_y = self.map_to_world(x, y)
                    obstacle_list.extend([world_x, world_y, self.occupancy_grid[y, x] / 100.0])
                    
        obstacles.data = obstacle_list
        self.fused_obstacles_pub.publish(obstacles)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = SensorFusionNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
