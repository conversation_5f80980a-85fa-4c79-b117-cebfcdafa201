#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import Float32MultiArray
from geometry_msgs.msg import PointStamped
import numpy as np
import math

class UltrasonicProcessor(Node):
    def __init__(self):
        super().__init__('ultrasonic_processor')
        
        # Parameters
        self.declare_parameter('min_range', 0.02)
        self.declare_parameter('max_range', 4.0)
        self.declare_parameter('confidence_threshold', 0.8)
        self.declare_parameter('filter_window_size', 5)
        
        self.min_range = self.get_parameter('min_range').value
        self.max_range = self.get_parameter('max_range').value
        self.confidence_threshold = self.get_parameter('confidence_threshold').value
        self.filter_window_size = self.get_parameter('filter_window_size').value
        
        # Sensor configuration (relative to robot base)
        self.sensor_config = {
            'front': {'position': (0.15, 0.0), 'angle': 0.0},
            'left': {'position': (0.0, 0.15), 'angle': math.pi/2},
            'right': {'position': (0.0, -0.15), 'angle': -math.pi/2},
            'back': {'position': (-0.15, 0.0), 'angle': math.pi}
        }
        
        # Data storage for filtering
        self.sensor_history = {
            'front': [],
            'left': [],
            'right': [],
            'back': []
        }
        
        # Subscribers
        self.ultrasonic_sub = self.create_subscription(
            Float32MultiArray, '/ultrasonic/distances', self.ultrasonic_callback, 10)
        
        # Publishers
        self.filtered_distances_pub = self.create_publisher(
            Float32MultiArray, '/ultrasonic/filtered_distances', 10)
        self.obstacles_pub = self.create_publisher(
            Float32MultiArray, '/ultrasonic/obstacles', 10)
        self.confidence_pub = self.create_publisher(
            Float32MultiArray, '/ultrasonic/confidence', 10)
        
        self.get_logger().info('Ultrasonic Processor initialized')
        
    def ultrasonic_callback(self, msg):
        """Process ultrasonic sensor data"""
        if len(msg.data) < 4:
            self.get_logger().warn('Insufficient ultrasonic data')
            return
            
        # Extract raw distances
        raw_distances = {
            'front': msg.data[0],
            'left': msg.data[1],
            'right': msg.data[2],
            'back': msg.data[3]
        }
        
        # Filter distances
        filtered_distances = self.filter_distances(raw_distances)
        
        # Calculate confidence
        confidence_scores = self.calculate_confidence(filtered_distances)
        
        # Extract obstacles
        obstacles = self.extract_obstacles(filtered_distances, confidence_scores)
        
        # Publish results
        self.publish_filtered_distances(filtered_distances)
        self.publish_confidence(confidence_scores)
        self.publish_obstacles(obstacles)
        
    def filter_distances(self, raw_distances):
        """Apply filtering to ultrasonic distances"""
        filtered_distances = {}
        
        for sensor_name, distance in raw_distances.items():
            # Add to history
            self.sensor_history[sensor_name].append(distance)
            
            # Keep only recent measurements
            if len(self.sensor_history[sensor_name]) > self.filter_window_size:
                self.sensor_history[sensor_name].pop(0)
                
            # Apply range filter
            if distance < self.min_range or distance > self.max_range:
                filtered_distances[sensor_name] = float('inf')
                continue
                
            # Apply median filter
            valid_measurements = [d for d in self.sensor_history[sensor_name] 
                                if self.min_range <= d <= self.max_range]
            
            if len(valid_measurements) == 0:
                filtered_distances[sensor_name] = float('inf')
            else:
                filtered_distances[sensor_name] = np.median(valid_measurements)
                
        return filtered_distances
        
    def calculate_confidence(self, filtered_distances):
        """Calculate confidence scores for each sensor"""
        confidence_scores = {}
        
        for sensor_name, distance in filtered_distances.items():
            if math.isinf(distance):
                confidence_scores[sensor_name] = 0.0
                continue
                
            # Base confidence on measurement consistency
            history = self.sensor_history[sensor_name]
            if len(history) < 2:
                confidence_scores[sensor_name] = 0.5
                continue
                
            # Calculate variance
            valid_measurements = [d for d in history 
                                if self.min_range <= d <= self.max_range]
            
            if len(valid_measurements) < 2:
                confidence_scores[sensor_name] = 0.3
                continue
                
            variance = np.var(valid_measurements)
            
            # Convert variance to confidence (lower variance = higher confidence)
            max_variance = 0.1  # meters^2
            confidence = max(0.0, 1.0 - (variance / max_variance))
            
            # Apply distance-based confidence (closer objects = higher confidence)
            distance_factor = max(0.3, 1.0 - (distance / self.max_range))
            confidence *= distance_factor
            
            confidence_scores[sensor_name] = min(1.0, confidence)
            
        return confidence_scores
        
    def extract_obstacles(self, filtered_distances, confidence_scores):
        """Extract obstacle information from ultrasonic data"""
        obstacles = []
        
        for sensor_name, distance in filtered_distances.items():
            if math.isinf(distance):
                continue
                
            confidence = confidence_scores[sensor_name]
            if confidence < self.confidence_threshold:
                continue
                
            # Get sensor configuration
            sensor_pos = self.sensor_config[sensor_name]['position']
            sensor_angle = self.sensor_config[sensor_name]['angle']
            
            # Calculate obstacle position relative to robot
            obstacle_x = sensor_pos[0] + distance * math.cos(sensor_angle)
            obstacle_y = sensor_pos[1] + distance * math.sin(sensor_angle)
            
            # Add obstacle info: [x, y, distance, confidence, sensor_id]
            sensor_id = list(self.sensor_config.keys()).index(sensor_name)
            obstacles.append([obstacle_x, obstacle_y, distance, confidence, float(sensor_id)])
            
        return obstacles
        
    def publish_filtered_distances(self, filtered_distances):
        """Publish filtered distance measurements"""
        msg = Float32MultiArray()
        
        # Order: front, left, right, back
        data = [
            filtered_distances.get('front', float('inf')),
            filtered_distances.get('left', float('inf')),
            filtered_distances.get('right', float('inf')),
            filtered_distances.get('back', float('inf'))
        ]
        
        msg.data = data
        self.filtered_distances_pub.publish(msg)
        
    def publish_confidence(self, confidence_scores):
        """Publish confidence scores"""
        msg = Float32MultiArray()
        
        # Order: front, left, right, back
        data = [
            confidence_scores.get('front', 0.0),
            confidence_scores.get('left', 0.0),
            confidence_scores.get('right', 0.0),
            confidence_scores.get('back', 0.0)
        ]
        
        msg.data = data
        self.confidence_pub.publish(msg)
        
    def publish_obstacles(self, obstacles):
        """Publish detected obstacles"""
        msg = Float32MultiArray()
        
        data = []
        for obstacle in obstacles:
            data.extend(obstacle)  # [x, y, distance, confidence, sensor_id]
            
        msg.data = data
        self.obstacles_pub.publish(msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = UltrasonicProcessor()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
