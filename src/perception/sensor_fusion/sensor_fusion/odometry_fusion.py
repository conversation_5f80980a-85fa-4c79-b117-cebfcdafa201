#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PoseWithCovarianceStamped, TwistWithCovarianceStamped
from sensor_msgs.msg import Imu
import numpy as np
import math
from scipy.spatial.transform import Rotation

class OdometryFusion(Node):
    def __init__(self):
        super().__init__('odometry_fusion')
        
        # Parameters
        self.declare_parameter('use_imu', False)
        self.declare_parameter('wheel_odom_weight', 0.7)
        self.declare_parameter('imu_weight', 0.3)
        self.declare_parameter('position_covariance', 0.01)
        self.declare_parameter('orientation_covariance', 0.05)
        self.declare_parameter('linear_velocity_covariance', 0.1)
        self.declare_parameter('angular_velocity_covariance', 0.1)
        
        self.use_imu = self.get_parameter('use_imu').value
        self.wheel_odom_weight = self.get_parameter('wheel_odom_weight').value
        self.imu_weight = self.get_parameter('imu_weight').value
        self.pos_cov = self.get_parameter('position_covariance').value
        self.orient_cov = self.get_parameter('orientation_covariance').value
        self.lin_vel_cov = self.get_parameter('linear_velocity_covariance').value
        self.ang_vel_cov = self.get_parameter('angular_velocity_covariance').value
        
        # State variables
        self.last_wheel_odom = None
        self.last_imu = None
        self.fused_pose = None
        self.fused_twist = None
        
        # Kalman filter state
        self.state = np.zeros(6)  # [x, y, theta, vx, vy, vtheta]
        self.covariance = np.eye(6) * 0.1
        
        # Subscribers
        self.wheel_odom_sub = self.create_subscription(
            Odometry, '/odom', self.wheel_odom_callback, 10)
        
        if self.use_imu:
            self.imu_sub = self.create_subscription(
                Imu, '/imu', self.imu_callback, 10)
        
        # Publishers
        self.fused_odom_pub = self.create_publisher(
            Odometry, '/odom_fused', 10)
        self.pose_pub = self.create_publisher(
            PoseWithCovarianceStamped, '/pose_fused', 10)
        
        # Timer for fusion updates
        self.fusion_timer = self.create_timer(0.05, self.update_fusion)  # 20 Hz
        
        self.get_logger().info(f'Odometry Fusion initialized (IMU: {self.use_imu})')
        
    def wheel_odom_callback(self, msg):
        """Process wheel odometry data"""
        self.last_wheel_odom = msg
        
    def imu_callback(self, msg):
        """Process IMU data"""
        self.last_imu = msg
        
    def update_fusion(self):
        """Update fused odometry using Kalman filter"""
        if self.last_wheel_odom is None:
            return
            
        # Prediction step (using wheel odometry)
        self.predict_state()
        
        # Update step (using available sensors)
        self.update_state()
        
        # Publish fused odometry
        self.publish_fused_odometry()
        
    def predict_state(self):
        """Predict state using motion model"""
        if self.last_wheel_odom is None:
            return
            
        # Extract wheel odometry data
        pose = self.last_wheel_odom.pose.pose
        twist = self.last_wheel_odom.twist.twist
        
        # Convert quaternion to yaw
        quat = pose.orientation
        yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                        1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
        
        # Update state with wheel odometry
        self.state[0] = pose.position.x
        self.state[1] = pose.position.y
        self.state[2] = yaw
        self.state[3] = twist.linear.x
        self.state[4] = twist.linear.y
        self.state[5] = twist.angular.z
        
        # Process noise (motion uncertainty)
        dt = 0.05  # 20 Hz
        Q = np.diag([
            self.pos_cov * dt,      # x position
            self.pos_cov * dt,      # y position
            self.orient_cov * dt,   # orientation
            self.lin_vel_cov * dt,  # linear velocity x
            self.lin_vel_cov * dt,  # linear velocity y
            self.ang_vel_cov * dt   # angular velocity
        ])
        
        # Predict covariance
        self.covariance += Q
        
    def update_state(self):
        """Update state using sensor measurements"""
        if self.use_imu and self.last_imu is not None:
            self.update_with_imu()
            
        # Apply constraints to keep covariance reasonable
        self.covariance = np.clip(self.covariance, 0.001, 10.0)
        
    def update_with_imu(self):
        """Update state using IMU data"""
        if self.last_imu is None:
            return
            
        # Extract IMU orientation
        imu_quat = self.last_imu.orientation
        imu_yaw = math.atan2(2.0 * (imu_quat.w * imu_quat.z + imu_quat.x * imu_quat.y),
                            1.0 - 2.0 * (imu_quat.y * imu_quat.y + imu_quat.z * imu_quat.z))
        
        # Measurement update for orientation
        H = np.zeros((1, 6))  # Measurement matrix
        H[0, 2] = 1.0  # Observe orientation
        
        z = np.array([imu_yaw])  # Measurement
        h = np.array([self.state[2]])  # Predicted measurement
        
        # Innovation
        y = z - h
        
        # Handle angle wrapping
        while y[0] > math.pi:
            y[0] -= 2 * math.pi
        while y[0] < -math.pi:
            y[0] += 2 * math.pi
            
        # Innovation covariance
        R = np.array([[self.orient_cov]])  # Measurement noise
        S = H @ self.covariance @ H.T + R
        
        # Kalman gain
        K = self.covariance @ H.T @ np.linalg.inv(S)
        
        # State update
        self.state += K @ y
        
        # Covariance update
        I = np.eye(6)
        self.covariance = (I - K @ H) @ self.covariance
        
        # Update angular velocity from IMU
        imu_angular_vel = self.last_imu.angular_velocity.z
        self.state[5] = (self.wheel_odom_weight * self.state[5] + 
                        self.imu_weight * imu_angular_vel)
        
    def publish_fused_odometry(self):
        """Publish fused odometry"""
        # Create odometry message
        odom_msg = Odometry()
        odom_msg.header.stamp = self.get_clock().now().to_msg()
        odom_msg.header.frame_id = 'odom'
        odom_msg.child_frame_id = 'base_link'
        
        # Position
        odom_msg.pose.pose.position.x = self.state[0]
        odom_msg.pose.pose.position.y = self.state[1]
        odom_msg.pose.pose.position.z = 0.0
        
        # Orientation (convert yaw to quaternion)
        yaw = self.state[2]
        odom_msg.pose.pose.orientation.x = 0.0
        odom_msg.pose.pose.orientation.y = 0.0
        odom_msg.pose.pose.orientation.z = math.sin(yaw / 2.0)
        odom_msg.pose.pose.orientation.w = math.cos(yaw / 2.0)
        
        # Velocity
        odom_msg.twist.twist.linear.x = self.state[3]
        odom_msg.twist.twist.linear.y = self.state[4]
        odom_msg.twist.twist.angular.z = self.state[5]
        
        # Covariance matrices
        pose_cov = np.zeros((6, 6))
        pose_cov[:3, :3] = self.covariance[:3, :3]  # Position and orientation
        odom_msg.pose.covariance = pose_cov.flatten().tolist()
        
        twist_cov = np.zeros((6, 6))
        twist_cov[3:, 3:] = self.covariance[3:, 3:]  # Velocities
        odom_msg.twist.covariance = twist_cov.flatten().tolist()
        
        self.fused_odom_pub.publish(odom_msg)
        
        # Also publish pose with covariance
        self.publish_pose_with_covariance(odom_msg)
        
    def publish_pose_with_covariance(self, odom_msg):
        """Publish pose with covariance"""
        pose_msg = PoseWithCovarianceStamped()
        pose_msg.header = odom_msg.header
        pose_msg.pose.pose = odom_msg.pose.pose
        pose_msg.pose.covariance = odom_msg.pose.covariance
        
        self.pose_pub.publish(pose_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = OdometryFusion()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
