#!/usr/bin/env python3

"""
Dual LiDAR Merger Node
Combines front and rear LiDAR scans into a single 360-degree scan for navigation
Designed for robots with two LiDAR sensors in opposite directions
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
import numpy as np
import math
from threading import Lock


class DualLidarMerger(Node):
    def __init__(self):
        super().__init__('dual_lidar_merger')
        
        # Parameters
        self.declare_parameter('front_topic', '/scan_front')
        self.declare_parameter('rear_topic', '/scan_rear')
        self.declare_parameter('output_topic', '/scan')
        self.declare_parameter('frame_id', 'laser_frame')
        self.declare_parameter('merge_method', 'closest')  # 'closest', 'average', 'front_priority'
        self.declare_parameter('max_range', 8.0)
        self.declare_parameter('min_range', 0.3)
        self.declare_parameter('angle_resolution', 360)  # Number of output rays
        
        # Get parameters
        self.front_topic = self.get_parameter('front_topic').value
        self.rear_topic = self.get_parameter('rear_topic').value
        self.output_topic = self.get_parameter('output_topic').value
        self.frame_id = self.get_parameter('frame_id').value
        self.merge_method = self.get_parameter('merge_method').value
        self.max_range = self.get_parameter('max_range').value
        self.min_range = self.get_parameter('min_range').value
        self.angle_resolution = self.get_parameter('angle_resolution').value
        
        # Subscribers
        self.front_sub = self.create_subscription(
            LaserScan, self.front_topic, self.front_callback, 10)
        self.rear_sub = self.create_subscription(
            LaserScan, self.rear_topic, self.rear_callback, 10)
        
        # Publisher
        self.merged_pub = self.create_publisher(LaserScan, self.output_topic, 10)
        
        # Data storage
        self.front_scan = None
        self.rear_scan = None
        self.data_lock = Lock()
        
        # Timer for publishing merged scan
        self.timer = self.create_timer(0.1, self.merge_and_publish)  # 10 Hz
        
        self.get_logger().info(f'Dual LiDAR Merger initialized')
        self.get_logger().info(f'  Front topic: {self.front_topic}')
        self.get_logger().info(f'  Rear topic: {self.rear_topic}')
        self.get_logger().info(f'  Output topic: {self.output_topic}')
        self.get_logger().info(f'  Merge method: {self.merge_method}')
        
    def front_callback(self, msg):
        """Store front LiDAR scan"""
        with self.data_lock:
            self.front_scan = msg
            
    def rear_callback(self, msg):
        """Store rear LiDAR scan"""
        with self.data_lock:
            self.rear_scan = msg
            
    def merge_and_publish(self):
        """Merge front and rear scans and publish result"""
        with self.data_lock:
            if self.front_scan is None or self.rear_scan is None:
                return
                
            # Create merged scan
            merged_scan = self.merge_scans(self.front_scan, self.rear_scan)
            
            if merged_scan is not None:
                self.merged_pub.publish(merged_scan)
                
    def merge_scans(self, front_scan, rear_scan):
        """Merge two laser scans into a single 360-degree scan"""
        try:
            # Create output scan message
            merged_scan = LaserScan()
            merged_scan.header.stamp = self.get_clock().now().to_msg()
            merged_scan.header.frame_id = self.frame_id
            
            # Set scan parameters
            merged_scan.angle_min = -math.pi
            merged_scan.angle_max = math.pi
            merged_scan.angle_increment = 2 * math.pi / self.angle_resolution
            merged_scan.time_increment = 0.0
            merged_scan.scan_time = 0.1
            merged_scan.range_min = self.min_range
            merged_scan.range_max = self.max_range
            
            # Initialize ranges array
            ranges = [float('inf')] * self.angle_resolution
            
            # Process front scan
            self.add_scan_to_merged(front_scan, ranges, 0.0)  # Front LiDAR at 0° offset
            
            # Process rear scan (180° rotated)
            self.add_scan_to_merged(rear_scan, ranges, math.pi)  # Rear LiDAR at 180° offset
            
            merged_scan.ranges = ranges
            merged_scan.intensities = []  # Not using intensities for now
            
            return merged_scan
            
        except Exception as e:
            self.get_logger().error(f'Error merging scans: {str(e)}')
            return None
            
    def add_scan_to_merged(self, scan, ranges, angle_offset):
        """Add a single scan to the merged ranges array"""
        for i, range_val in enumerate(scan.ranges):
            # Skip invalid ranges
            if math.isnan(range_val) or math.isinf(range_val):
                continue
            if range_val < scan.range_min or range_val > scan.range_max:
                continue
                
            # Calculate angle in the merged scan coordinate system
            scan_angle = scan.angle_min + i * scan.angle_increment
            merged_angle = scan_angle + angle_offset
            
            # Normalize angle to [-pi, pi]
            while merged_angle > math.pi:
                merged_angle -= 2 * math.pi
            while merged_angle < -math.pi:
                merged_angle += 2 * math.pi
                
            # Find corresponding index in merged scan
            merged_index = int((merged_angle - (-math.pi)) / (2 * math.pi / self.angle_resolution))
            merged_index = max(0, min(merged_index, self.angle_resolution - 1))
            
            # Merge range values based on method
            if self.merge_method == 'closest':
                if math.isinf(ranges[merged_index]) or range_val < ranges[merged_index]:
                    ranges[merged_index] = range_val
            elif self.merge_method == 'average':
                if not math.isinf(ranges[merged_index]):
                    ranges[merged_index] = (ranges[merged_index] + range_val) / 2.0
                else:
                    ranges[merged_index] = range_val
            elif self.merge_method == 'front_priority':
                # Front scan (angle_offset = 0) has priority
                if angle_offset == 0.0:
                    ranges[merged_index] = range_val
                elif math.isinf(ranges[merged_index]):
                    ranges[merged_index] = range_val


def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = DualLidarMerger()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'Error in dual_lidar_merger: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
