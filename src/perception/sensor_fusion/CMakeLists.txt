cmake_minimum_required(VERSION 3.8)
project(sensor_fusion)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_python REQUIRED)
find_package(rclpy REQUIRED)
find_package(std_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(tf2_ros REQUIRED)

# Install Python modules
ament_python_install_package(${PROJECT_NAME})

# Install Python executables with correct names (without .py extension)
install(PROGRAMS sensor_fusion/sensor_fusion_node.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME sensor_fusion_node)

install(PROGRAMS sensor_fusion/lidar_processor.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME lidar_processor)

install(PROGRAMS sensor_fusion/ultrasonic_processor.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME ultrasonic_processor)

install(PROGRAMS sensor_fusion/odometry_fusion.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME odometry_fusion)

install(PROGRAMS sensor_fusion/dual_lidar_merger.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME dual_lidar_merger)

# Install launch files
install(DIRECTORY launch/
  DESTINATION share/${PROJECT_NAME}/launch
)

# Install config files
install(DIRECTORY config/
  DESTINATION share/${PROJECT_NAME}/config
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
