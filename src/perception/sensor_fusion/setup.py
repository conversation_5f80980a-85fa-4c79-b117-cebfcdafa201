from setuptools import setup

package_name = 'sensor_fusion'

setup(
    name=package_name,
    version='1.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', [
            'launch/sensor_fusion.launch.py',
            'launch/dual_lidar.launch.py'
        ]),
        ('share/' + package_name + '/config', ['config/fusion_params.yaml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='Sensor data fusion for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'sensor_fusion_node = sensor_fusion.sensor_fusion_node:main',
            'lidar_processor = sensor_fusion.lidar_processor:main',
            'ultrasonic_processor = sensor_fusion.ultrasonic_processor:main',
            'odometry_fusion = sensor_fusion.odometry_fusion:main',
            'dual_lidar_merger = sensor_fusion.dual_lidar_merger:main',
        ],
    },
)
