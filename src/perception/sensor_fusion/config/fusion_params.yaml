sensor_fusion_node:
  ros__parameters:
    # Map parameters
    map_resolution: 0.05      # meters per pixel
    map_width: 400           # pixels (20m x 20m map)
    map_height: 400          # pixels
    
    # Sensor parameters
    lidar_max_range: 12.0    # meters
    ultrasonic_max_range: 4.0 # meters
    
    # Update rate
    update_rate: 10.0        # Hz

lidar_processor:
  ros__parameters:
    # Range filtering
    min_range: 0.15          # meters
    max_range: 12.0          # meters
    
    # Angle filtering
    angle_filter_min: -3.14159  # radians
    angle_filter_max: 3.14159   # radians
    
    # Clustering parameters
    cluster_tolerance: 0.1    # meters
    min_cluster_size: 3      # points
    max_cluster_size: 1000   # points

ultrasonic_processor:
  ros__parameters:
    # Range filtering
    min_range: 0.02          # meters
    max_range: 4.0           # meters
    
    # Confidence parameters
    confidence_threshold: 0.8
    filter_window_size: 5

odometry_fusion:
  ros__parameters:
    # Sensor fusion weights
    use_imu: false
    wheel_odom_weight: 0.7
    imu_weight: 0.3
    
    # Covariance parameters
    position_covariance: 0.01
    orientation_covariance: 0.05
    linear_velocity_covariance: 0.1
    angular_velocity_covariance: 0.1
