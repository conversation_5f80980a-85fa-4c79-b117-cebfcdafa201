#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directory
    pkg_sensor_fusion = get_package_share_directory('sensor_fusion')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(pkg_sensor_fusion, 'config', 'fusion_params.yaml'),
        description='Full path to parameters file'
    )
    
    use_lidar_processor_arg = DeclareLaunchArgument(
        'use_lidar_processor',
        default_value='true',
        description='Enable LiDAR processor'
    )
    
    use_ultrasonic_processor_arg = DeclareLaunchArgument(
        'use_ultrasonic_processor',
        default_value='true',
        description='Enable ultrasonic processor'
    )
    
    use_odometry_fusion_arg = DeclareLaunchArgument(
        'use_odometry_fusion',
        default_value='true',
        description='Enable odometry fusion'
    )
    
    # Sensor Fusion Node
    sensor_fusion_node = Node(
        package='sensor_fusion',
        executable='sensor_fusion_node',
        name='sensor_fusion_node',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ]
    )
    
    # LiDAR Processor Node
    lidar_processor_node = Node(
        package='sensor_fusion',
        executable='lidar_processor',
        name='lidar_processor',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=LaunchConfiguration('use_lidar_processor')
    )
    
    # Ultrasonic Processor Node
    ultrasonic_processor_node = Node(
        package='sensor_fusion',
        executable='ultrasonic_processor',
        name='ultrasonic_processor',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=LaunchConfiguration('use_ultrasonic_processor')
    )
    
    # Odometry Fusion Node
    odometry_fusion_node = Node(
        package='sensor_fusion',
        executable='odometry_fusion',
        name='odometry_fusion',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=LaunchConfiguration('use_odometry_fusion')
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        params_file_arg,
        use_lidar_processor_arg,
        use_ultrasonic_processor_arg,
        use_odometry_fusion_arg,
        
        # Nodes
        sensor_fusion_node,
        lidar_processor_node,
        ultrasonic_processor_node,
        odometry_fusion_node,
    ])
