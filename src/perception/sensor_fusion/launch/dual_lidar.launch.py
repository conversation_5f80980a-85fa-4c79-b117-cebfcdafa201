#!/usr/bin/env python3

"""
Dual LiDAR Launch File
Launches dual LiDAR merger node for robots with front and rear LiDAR sensors
"""

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration, PythonExpression
from launch_ros.actions import Node


def generate_launch_description():
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time if true'
    )
    
    front_topic_arg = DeclareLaunchArgument(
        'front_topic',
        default_value='/scan_front',
        description='Front LiDAR topic'
    )
    
    rear_topic_arg = DeclareLaunchArgument(
        'rear_topic',
        default_value='/scan_rear',
        description='Rear LiDAR topic'
    )
    
    output_topic_arg = DeclareLaunchArgument(
        'output_topic',
        default_value='/scan',
        description='Merged scan output topic'
    )
    
    merge_method_arg = DeclareLaunchArgument(
        'merge_method',
        default_value='closest',
        description='Merge method: closest, average, front_priority'
    )
    
    frame_id_arg = DeclareLaunchArgument(
        'frame_id',
        default_value='laser_frame',
        description='Frame ID for merged scan'
    )
    
    # Dual LiDAR merger node
    dual_lidar_merger_node = Node(
        package='sensor_fusion',
        executable='dual_lidar_merger',
        name='dual_lidar_merger',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'front_topic': LaunchConfiguration('front_topic'),
            'rear_topic': LaunchConfiguration('rear_topic'),
            'output_topic': LaunchConfiguration('output_topic'),
            'merge_method': LaunchConfiguration('merge_method'),
            'frame_id': LaunchConfiguration('frame_id'),
            'max_range': 8.0,
            'min_range': 0.3,
            'angle_resolution': 360
        }],
        remappings=[
            ('/scan_front', LaunchConfiguration('front_topic')),
            ('/scan_rear', LaunchConfiguration('rear_topic')),
            ('/scan', LaunchConfiguration('output_topic'))
        ]
    )
    
    # Note: Real robot LiDAR nodes are launched separately in real_robot_dual_lidar.launch.py
    # This launch file only handles the dual LiDAR merger for both simulation and real robot
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        front_topic_arg,
        rear_topic_arg,
        output_topic_arg,
        merge_method_arg,
        frame_id_arg,
        
        # Nodes
        dual_lidar_merger_node,
    ])
