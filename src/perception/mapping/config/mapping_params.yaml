map_manager:
  ros__parameters:
    # Static map configuration
    static_map_file: ""  # Path to static map file (leave empty for dynamic-only)
    save_map_directory: "saved_maps"
    auto_save_interval: 60.0  # seconds
    
    # Dynamic mapping
    dynamic_update_enabled: true
    map_frame: "map"

dynamic_map_updater:
  ros__parameters:
    # Map parameters
    map_resolution: 0.05      # meters per pixel
    map_width: 400           # pixels (20m x 20m)
    map_height: 400          # pixels
    
    # Obstacle tracking
    obstacle_decay_rate: 0.95    # Per second (0.95 = 5% decay per second)
    obstacle_threshold: 0.7      # Confidence threshold for marking as occupied
    max_obstacle_age: 10.0       # seconds
    
    # Update rate
    update_rate: 5.0            # Hz

map_saver:
  ros__parameters:
    # Save configuration
    save_directory: "saved_maps"
    map_topic: "/map_combined"
    map_name_prefix: "indoor_map"
    
    # Auto-save
    auto_save_enabled: true
    auto_save_interval: 300.0   # 5 minutes
