# Enhanced SLAM Toolbox Configuration for Indoor Autonomous Vehicle
# Phase 2: Advanced Features - Real-time Mapping

slam_toolbox:
  ros__parameters:
    # Plugin params - Optimized for real-time performance
    solver_plugin: solver_plugins::CeresSolver
    ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
    ceres_preconditioner: SCHUR_JACOBI
    ceres_trust_strategy: LEVENBERG_MARQUARDT
    ceres_dogleg_type: TRADITIONAL_DOGLEG
    ceres_loss_function: None

    # ROS Parameters
    odom_frame: odom
    map_frame: map
    base_frame: base_footprint
    scan_topic: /scan
    mode: mapping  # localization

    # FORCE NO MAP LOADING - start completely fresh
    map_file_name: ""
    map_start_pose: [0.0, 0.0, 0.0]
    map_start_at_dock: false

    # Disable auto-save to prevent map persistence
    map_save_period: 0.0  # Disable auto-save
    serialize_data: false  # Don't serialize map data

    debug_logging: true  # Enable debug logging to catch issues
    throttle_scans: 1
    transform_publish_period: 0.02
    map_update_interval: 5.0
    resolution: 0.1  # Lower resolution for cleaner maps
    max_laser_range: 8.0  # Shorter range for indoor stability
    minimum_time_interval: 0.5
    transform_timeout: 0.5  # Increased timeout for stability
    tf_buffer_duration: 30.0
    stack_size_to_use: 40000000
    enable_interactive_mode: true

    # Additional stability parameters
    max_scan_range: 8.0
    minimum_scan_range: 0.3
    scan_matching_min_response: 0.55  # Higher threshold for better matching

    # FIXED: Proper map clearing and updates
    map_start_at_dock: false
    map_file_name: ""  # Don't load old map
    map_start_pose: [0.0, 0.0, 0.0]

    # Clear old data settings
    minimum_travel_distance: 0.5
    minimum_travel_heading: 0.5
    scan_buffer_size: 5  # Smaller buffer = less accumulation
    scan_buffer_maximum_scan_distance: 8.0

    # General Parameters - Optimized for stability and teleport prevention
    use_scan_matching: true
    use_scan_barycenter: true
    minimum_travel_distance: 0.2  # More frequent updates for stability
    minimum_travel_heading: 0.2   # More frequent rotation updates
    scan_buffer_size: 5           # Smaller buffer to prevent accumulation errors
    scan_buffer_maximum_scan_distance: 8.0  # Match LiDAR range
    link_match_minimum_response_fine: 0.6    # Higher threshold for better matching
    link_scan_maximum_distance: 1.0         # Shorter distance for stability
    loop_search_maximum_distance: 2.0       # Shorter loop search to prevent errors
    do_loop_closing: false        # Disable loop closing to prevent teleports
    loop_match_minimum_chain_size: 15       # Higher chain size for confidence
    loop_match_maximum_variance_coarse: 1.5 # Lower variance for stability
    loop_match_minimum_response_coarse: 0.7 # Higher confidence threshold
    loop_match_minimum_response_fine: 0.8   # Much higher fine matching threshold

    # Correlation Parameters - Optimized for stability
    correlation_search_space_dimension: 0.3  # Smaller search space for stability
    correlation_search_space_resolution: 0.005  # Higher resolution for accuracy
    correlation_search_space_smear_deviation: 0.05  # Less smearing for precision

    # Correlation Parameters - Loop Closure Parameters
    loop_search_space_dimension: 8.0
    loop_search_space_resolution: 0.05
    loop_search_space_smear_deviation: 0.03

    # Scan Matcher Parameters
    distance_variance_penalty: 0.5      
    angle_variance_penalty: 1.0    

    fine_search_angle_offset: 0.00349     
    coarse_search_angle_offset: 0.349   
    coarse_angle_resolution: 0.0349        
    minimum_angle_penalty: 0.9
    minimum_distance_penalty: 0.5
    use_response_expansion: true
