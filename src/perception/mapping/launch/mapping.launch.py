#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_mapping = get_package_share_directory('mapping')
    pkg_sensor_fusion = get_package_share_directory('sensor_fusion')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    mapping_params_file_arg = DeclareLaunchArgument(
        'mapping_params_file',
        default_value=os.path.join(pkg_mapping, 'config', 'mapping_params.yaml'),
        description='Full path to mapping parameters file'
    )
    
    fusion_params_file_arg = DeclareLaunchArgument(
        'fusion_params_file',
        default_value=os.path.join(pkg_sensor_fusion, 'config', 'fusion_params.yaml'),
        description='Full path to sensor fusion parameters file'
    )
    
    use_sensor_fusion_arg = DeclareLaunchArgument(
        'use_sensor_fusion',
        default_value='true',
        description='Enable sensor fusion'
    )
    
    use_dynamic_mapping_arg = DeclareLaunchArgument(
        'use_dynamic_mapping',
        default_value='true',
        description='Enable dynamic mapping'
    )
    
    use_map_saver_arg = DeclareLaunchArgument(
        'use_map_saver',
        default_value='true',
        description='Enable map saver'
    )
    
    static_map_file_arg = DeclareLaunchArgument(
        'static_map_file',
        default_value='',
        description='Path to static map file (optional)'
    )
    
    # Include sensor fusion launch
    sensor_fusion_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('sensor_fusion'),
                'launch',
                'sensor_fusion.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'params_file': LaunchConfiguration('fusion_params_file'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_sensor_fusion'))
    )
    
    # Map Manager Node
    map_manager_node = Node(
        package='mapping',
        executable='map_manager',
        name='map_manager',
        output='screen',
        parameters=[
            LaunchConfiguration('mapping_params_file'),
            {
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'static_map_file': LaunchConfiguration('static_map_file')
            }
        ]
    )
    
    # Dynamic Map Updater Node
    dynamic_map_updater_node = Node(
        package='mapping',
        executable='dynamic_map_updater',
        name='dynamic_map_updater',
        output='screen',
        parameters=[
            LaunchConfiguration('mapping_params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_dynamic_mapping'))
    )
    
    # Map Saver Node
    map_saver_node = Node(
        package='mapping',
        executable='map_saver',
        name='map_saver',
        output='screen',
        parameters=[
            LaunchConfiguration('mapping_params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_map_saver'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        mapping_params_file_arg,
        fusion_params_file_arg,
        use_sensor_fusion_arg,
        use_dynamic_mapping_arg,
        use_map_saver_arg,
        static_map_file_arg,
        
        # Launch sensor fusion
        sensor_fusion_launch,
        
        # Mapping nodes
        map_manager_node,
        dynamic_map_updater_node,
        map_saver_node,
    ])
