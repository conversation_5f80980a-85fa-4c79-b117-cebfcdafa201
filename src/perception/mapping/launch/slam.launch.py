#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_mapping = get_package_share_directory('mapping')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    slam_params_file_arg = DeclareLaunchArgument(
        'slam_params_file',
        default_value=os.path.join(pkg_mapping, 'config', 'slam_params.yaml'),
        description='Full path to SLAM parameters file'
    )
    
    use_lifecycle_manager_arg = DeclareLaunchArgument(
        'use_lifecycle_manager',
        default_value='false',
        description='Enable lifecycle manager'
    )
    
    slam_mode_arg = DeclareLaunchArgument(
        'slam_mode',
        default_value='mapping',
        description='SLAM mode: mapping or localization'
    )
    
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        default_value='',
        description='Map file for localization mode'
    )
    
    # Include mapping launch (for sensor fusion and dynamic mapping)
    mapping_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('mapping'),
                'launch',
                'mapping.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'use_dynamic_mapping': 'true',
            'use_map_saver': 'true',
        }.items()
    )
    
    # SLAM Toolbox Node
    slam_toolbox_node = Node(
        package='slam_toolbox',
        executable='async_slam_toolbox_node',
        name='slam_toolbox',
        output='screen',
        parameters=[
            LaunchConfiguration('slam_params_file'),
            {
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'mode': LaunchConfiguration('slam_mode')
            }
        ],
        remappings=[
            ('/scan', '/scan'),
            ('/map', '/map_slam'),
            ('/map_metadata', '/map_metadata_slam')
        ]
    )
    
    # Lifecycle Manager (optional)
    lifecycle_manager_node = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_slam',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'autostart': True,
            'node_names': ['slam_toolbox']
        }],
        condition=IfCondition(LaunchConfiguration('use_lifecycle_manager'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        slam_params_file_arg,
        use_lifecycle_manager_arg,
        slam_mode_arg,
        map_file_arg,
        
        # Include mapping
        mapping_launch,
        
        # SLAM nodes
        slam_toolbox_node,
        lifecycle_manager_node,
    ])
