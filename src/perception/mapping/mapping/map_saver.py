#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import OccupancyGrid
from std_srvs.srv import Empty
from std_msgs.msg import String
import numpy as np
import yaml
import os
import time
from datetime import datetime

class MapSaver(Node):
    def __init__(self):
        super().__init__('map_saver')
        
        # Parameters
        self.declare_parameter('save_directory', 'saved_maps')
        self.declare_parameter('map_topic', '/map')
        self.declare_parameter('auto_save_enabled', True)
        self.declare_parameter('auto_save_interval', 300.0)  # 5 minutes
        self.declare_parameter('map_name_prefix', 'indoor_map')
        
        self.save_directory = self.get_parameter('save_directory').value
        self.map_topic = self.get_parameter('map_topic').value
        self.auto_save_enabled = self.get_parameter('auto_save_enabled').value
        self.auto_save_interval = self.get_parameter('auto_save_interval').value
        self.map_name_prefix = self.get_parameter('map_name_prefix').value
        
        # Map storage
        self.current_map = None
        self.last_save_time = time.time()
        self.map_changed = False
        
        # Create save directory
        os.makedirs(self.save_directory, exist_ok=True)
        
        # Subscribers
        self.map_sub = self.create_subscription(
            OccupancyGrid, self.map_topic, self.map_callback, 10)
        
        # Publishers
        self.status_pub = self.create_publisher(
            String, '/map_saver/status', 10)
        
        # Services
        self.save_map_srv = self.create_service(
            Empty, 'save_current_map', self.save_map_callback)
        
        # Timer for auto-save
        if self.auto_save_enabled:
            self.auto_save_timer = self.create_timer(
                self.auto_save_interval, self.auto_save_map)
        
        self.get_logger().info('Map Saver initialized')
        self.get_logger().info(f'Save directory: {self.save_directory}')
        self.get_logger().info(f'Auto-save: {self.auto_save_enabled}')
        
    def map_callback(self, msg):
        """Handle incoming map data"""
        # Check if map has changed significantly
        if self.has_map_changed(msg):
            self.current_map = msg
            self.map_changed = True
            
    def has_map_changed(self, new_map):
        """Check if the map has changed significantly"""
        if self.current_map is None:
            return True
            
        # Quick check: compare map dimensions and metadata
        if (new_map.info.width != self.current_map.info.width or
            new_map.info.height != self.current_map.info.height or
            abs(new_map.info.resolution - self.current_map.info.resolution) > 1e-6):
            return True
            
        # Compare map data (sample-based for efficiency)
        new_data = np.array(new_map.data)
        old_data = np.array(self.current_map.data)
        
        # Sample every 10th cell for comparison
        sample_indices = np.arange(0, len(new_data), 10)
        new_sample = new_data[sample_indices]
        old_sample = old_data[sample_indices]
        
        # Calculate difference ratio
        different_cells = np.sum(new_sample != old_sample)
        total_sampled = len(sample_indices)
        
        change_ratio = different_cells / total_sampled if total_sampled > 0 else 0
        
        # Consider map changed if more than 1% of sampled cells differ
        return change_ratio > 0.01
        
    def auto_save_map(self):
        """Auto-save map if it has changed"""
        if self.map_changed and self.current_map is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.map_name_prefix}_auto_{timestamp}"
            
            success = self.save_map_to_files(filename)
            if success:
                self.map_changed = False
                self.last_save_time = time.time()
                self.get_logger().info(f'Auto-saved map: {filename}')
                
                # Publish status
                status_msg = String()
                status_msg.data = f"Auto-saved: {filename}"
                self.status_pub.publish(status_msg)
            else:
                self.get_logger().error('Auto-save failed')
                
    def save_map_callback(self, request, response):
        """Service callback to save current map"""
        if self.current_map is None:
            self.get_logger().warn('No map available to save')
            return response
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.map_name_prefix}_manual_{timestamp}"
        
        success = self.save_map_to_files(filename)
        if success:
            self.get_logger().info(f'Map saved manually: {filename}')
            
            # Publish status
            status_msg = String()
            status_msg.data = f"Manual save: {filename}"
            self.status_pub.publish(status_msg)
        else:
            self.get_logger().error('Manual save failed')
            
        return response
        
    def save_map_to_files(self, filename):
        """Save map to PGM and YAML files"""
        if self.current_map is None:
            return False
            
        try:
            # Create file paths
            yaml_path = os.path.join(self.save_directory, f"{filename}.yaml")
            pgm_path = os.path.join(self.save_directory, f"{filename}.pgm")
            
            # Convert occupancy grid to image data
            map_data = np.array(self.current_map.data).reshape(
                (self.current_map.info.height, self.current_map.info.width))
            
            # Convert to image format (0-255)
            image_data = np.full_like(map_data, 205, dtype=np.uint8)  # Unknown = gray (205)
            
            # Free space = white (254)
            free_mask = (map_data >= 0) & (map_data < 25)
            image_data[free_mask] = 254
            
            # Occupied space = black (0)
            occupied_mask = map_data >= 65
            image_data[occupied_mask] = 0
            
            # Save PGM file
            self.save_pgm_file(pgm_path, image_data)
            
            # Create YAML metadata
            yaml_data = {
                'image': f"{filename}.pgm",
                'resolution': float(self.current_map.info.resolution),
                'origin': [
                    float(self.current_map.info.origin.position.x),
                    float(self.current_map.info.origin.position.y),
                    0.0
                ],
                'negate': 0,
                'occupied_thresh': 0.65,
                'free_thresh': 0.196,
                'mode': 'trinary',
                'saved_at': datetime.now().isoformat(),
                'map_size': {
                    'width': int(self.current_map.info.width),
                    'height': int(self.current_map.info.height)
                }
            }
            
            # Save YAML file
            with open(yaml_path, 'w') as f:
                yaml.dump(yaml_data, f, default_flow_style=False)
                
            # Save additional metadata
            self.save_metadata(filename)
            
            return True
            
        except Exception as e:
            self.get_logger().error(f'Failed to save map: {e}')
            return False
            
    def save_pgm_file(self, filepath, image_data):
        """Save image data as PGM file"""
        height, width = image_data.shape
        
        with open(filepath, 'wb') as f:
            # Write PGM header
            header = f"P5\n{width} {height}\n255\n"
            f.write(header.encode('ascii'))
            
            # Write image data
            f.write(image_data.tobytes())
            
    def save_metadata(self, filename):
        """Save additional metadata"""
        metadata_path = os.path.join(self.save_directory, f"{filename}_metadata.yaml")
        
        metadata = {
            'creation_time': datetime.now().isoformat(),
            'map_topic': self.map_topic,
            'node_name': self.get_name(),
            'map_info': {
                'width': int(self.current_map.info.width),
                'height': int(self.current_map.info.height),
                'resolution': float(self.current_map.info.resolution),
                'origin': {
                    'x': float(self.current_map.info.origin.position.x),
                    'y': float(self.current_map.info.origin.position.y),
                    'z': float(self.current_map.info.origin.position.z)
                }
            },
            'statistics': self.calculate_map_statistics()
        }
        
        try:
            with open(metadata_path, 'w') as f:
                yaml.dump(metadata, f, default_flow_style=False)
        except Exception as e:
            self.get_logger().warn(f'Failed to save metadata: {e}')
            
    def calculate_map_statistics(self):
        """Calculate map statistics"""
        if self.current_map is None:
            return {}
            
        map_data = np.array(self.current_map.data)
        
        total_cells = len(map_data)
        free_cells = np.sum((map_data >= 0) & (map_data < 25))
        occupied_cells = np.sum(map_data >= 65)
        unknown_cells = np.sum(map_data == -1)
        
        return {
            'total_cells': int(total_cells),
            'free_cells': int(free_cells),
            'occupied_cells': int(occupied_cells),
            'unknown_cells': int(unknown_cells),
            'free_percentage': float(free_cells / total_cells * 100) if total_cells > 0 else 0,
            'occupied_percentage': float(occupied_cells / total_cells * 100) if total_cells > 0 else 0,
            'unknown_percentage': float(unknown_cells / total_cells * 100) if total_cells > 0 else 0
        }

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = MapSaver()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
