#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import OccupancyGrid
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import Float32MultiArray
import numpy as np
import math
import time

class DynamicMapUpdater(Node):
    def __init__(self):
        super().__init__('dynamic_map_updater')
        
        # Parameters
        self.declare_parameter('map_resolution', 0.05)
        self.declare_parameter('map_width', 400)
        self.declare_parameter('map_height', 400)
        self.declare_parameter('obstacle_decay_rate', 0.95)  # Per second
        self.declare_parameter('obstacle_threshold', 0.7)
        self.declare_parameter('update_rate', 5.0)  # Hz
        self.declare_parameter('max_obstacle_age', 10.0)  # seconds
        
        self.map_resolution = self.get_parameter('map_resolution').value
        self.map_width = self.get_parameter('map_width').value
        self.map_height = self.get_parameter('map_height').value
        self.decay_rate = self.get_parameter('obstacle_decay_rate').value
        self.obstacle_threshold = self.get_parameter('obstacle_threshold').value
        self.update_rate = self.get_parameter('update_rate').value
        self.max_obstacle_age = self.get_parameter('max_obstacle_age').value
        
        # Map origin (center of the map)
        self.map_origin_x = -self.map_width * self.map_resolution / 2.0
        self.map_origin_y = -self.map_height * self.map_resolution / 2.0
        
        # Dynamic obstacle tracking
        self.obstacle_confidence = np.zeros((self.map_height, self.map_width), dtype=np.float32)
        self.obstacle_timestamps = np.zeros((self.map_height, self.map_width), dtype=np.float64)
        self.dynamic_map = np.full((self.map_height, self.map_width), -1, dtype=np.int8)
        
        # Robot pose
        self.robot_pose = None
        
        # Subscribers
        self.fused_obstacles_sub = self.create_subscription(
            Float32MultiArray, '/perception/fused_obstacles', 
            self.fused_obstacles_callback, 10)
        self.lidar_obstacles_sub = self.create_subscription(
            Float32MultiArray, '/lidar/obstacles',
            self.lidar_obstacles_callback, 10)
        self.ultrasonic_obstacles_sub = self.create_subscription(
            Float32MultiArray, '/ultrasonic/obstacles',
            self.ultrasonic_obstacles_callback, 10)
        self.robot_pose_sub = self.create_subscription(
            PoseStamped, '/robot_pose', self.robot_pose_callback, 10)
        
        # Publishers
        self.dynamic_map_pub = self.create_publisher(
            OccupancyGrid, '/map_dynamic', 10)
        
        # Timer for map updates
        self.update_timer = self.create_timer(1.0 / self.update_rate, self.update_dynamic_map)
        
        self.get_logger().info('Dynamic Map Updater initialized')
        self.get_logger().info(f'Map size: {self.map_width}x{self.map_height}')
        self.get_logger().info(f'Update rate: {self.update_rate} Hz')
        
    def world_to_map(self, x, y):
        """Convert world coordinates to map coordinates"""
        map_x = int((x - self.map_origin_x) / self.map_resolution)
        map_y = int((y - self.map_origin_y) / self.map_resolution)
        return map_x, map_y
        
    def is_valid_map_coord(self, map_x, map_y):
        """Check if map coordinates are valid"""
        return 0 <= map_x < self.map_width and 0 <= map_y < self.map_height
        
    def fused_obstacles_callback(self, msg):
        """Process fused obstacle data"""
        self.process_obstacles(msg.data, confidence_weight=1.0)
        
    def lidar_obstacles_callback(self, msg):
        """Process LiDAR obstacle data"""
        # Extract obstacle positions from LiDAR data
        # Format: [x, y, range, angle, x, y, range, angle, ...]
        obstacles = []
        for i in range(0, len(msg.data), 4):
            if i + 3 < len(msg.data):
                x, y, range_val, angle = msg.data[i:i+4]
                obstacles.extend([x, y, 0.8])  # High confidence for LiDAR
                
        self.process_obstacles(obstacles, confidence_weight=0.8)
        
    def ultrasonic_obstacles_callback(self, msg):
        """Process ultrasonic obstacle data"""
        # Extract obstacle positions from ultrasonic data
        # Format: [x, y, distance, confidence, sensor_id, ...]
        obstacles = []
        for i in range(0, len(msg.data), 5):
            if i + 4 < len(msg.data):
                x, y, distance, confidence, sensor_id = msg.data[i:i+5]
                obstacles.extend([x, y, confidence * 0.6])  # Lower confidence for ultrasonic
                
        self.process_obstacles(obstacles, confidence_weight=0.6)
        
    def robot_pose_callback(self, msg):
        """Update robot pose"""
        self.robot_pose = msg
        
    def process_obstacles(self, obstacle_data, confidence_weight=1.0):
        """Process obstacle data and update dynamic map"""
        current_time = time.time()
        
        # Process obstacles in groups of 3 (x, y, confidence)
        for i in range(0, len(obstacle_data), 3):
            if i + 2 >= len(obstacle_data):
                break
                
            x, y, confidence = obstacle_data[i:i+3]
            
            # Convert to map coordinates
            map_x, map_y = self.world_to_map(x, y)
            
            if not self.is_valid_map_coord(map_x, map_y):
                continue
                
            # Update obstacle confidence
            weighted_confidence = confidence * confidence_weight
            
            # Increase confidence for detected obstacles
            self.obstacle_confidence[map_y, map_x] = min(1.0, 
                self.obstacle_confidence[map_y, map_x] + weighted_confidence * 0.1)
            
            # Update timestamp
            self.obstacle_timestamps[map_y, map_x] = current_time
            
            # Mark surrounding area with lower confidence (uncertainty)
            self.mark_obstacle_area(map_x, map_y, weighted_confidence * 0.5, current_time)
            
    def mark_obstacle_area(self, center_x, center_y, confidence, timestamp):
        """Mark area around obstacle with lower confidence"""
        radius = 2  # pixels
        
        for dy in range(-radius, radius + 1):
            for dx in range(-radius, radius + 1):
                if dx == 0 and dy == 0:
                    continue  # Skip center (already processed)
                    
                map_x = center_x + dx
                map_y = center_y + dy
                
                if not self.is_valid_map_coord(map_x, map_y):
                    continue
                    
                # Distance-based confidence reduction
                distance = math.sqrt(dx*dx + dy*dy)
                area_confidence = confidence * (1.0 / (1.0 + distance))
                
                # Only update if this increases confidence
                if area_confidence > self.obstacle_confidence[map_y, map_x] * 0.1:
                    self.obstacle_confidence[map_y, map_x] = min(1.0,
                        self.obstacle_confidence[map_y, map_x] + area_confidence * 0.05)
                    self.obstacle_timestamps[map_y, map_x] = timestamp
                    
    def update_dynamic_map(self):
        """Update dynamic map with decay and publish"""
        current_time = time.time()
        dt = 1.0 / self.update_rate
        
        # Apply decay to obstacle confidence
        decay_factor = self.decay_rate ** dt
        self.obstacle_confidence *= decay_factor
        
        # Remove old obstacles
        age_mask = (current_time - self.obstacle_timestamps) > self.max_obstacle_age
        self.obstacle_confidence[age_mask] = 0.0
        
        # Update dynamic map based on confidence
        self.dynamic_map.fill(-1)  # Unknown
        
        # Mark high-confidence areas as occupied
        occupied_mask = self.obstacle_confidence > self.obstacle_threshold
        self.dynamic_map[occupied_mask] = 100
        
        # Mark medium-confidence areas as possibly occupied
        medium_mask = (self.obstacle_confidence > self.obstacle_threshold * 0.5) & \
                     (self.obstacle_confidence <= self.obstacle_threshold)
        self.dynamic_map[medium_mask] = 70
        
        # Mark low-confidence areas as free (if recently observed)
        recent_mask = (current_time - self.obstacle_timestamps) < 2.0
        low_confidence_mask = (self.obstacle_confidence <= self.obstacle_threshold * 0.3) & recent_mask
        self.dynamic_map[low_confidence_mask] = 0
        
        # Publish dynamic map
        self.publish_dynamic_map()
        
    def publish_dynamic_map(self):
        """Publish the dynamic occupancy grid"""
        map_msg = OccupancyGrid()
        
        # Header
        map_msg.header.stamp = self.get_clock().now().to_msg()
        map_msg.header.frame_id = 'map'
        
        # Map metadata
        map_msg.info.resolution = self.map_resolution
        map_msg.info.width = self.map_width
        map_msg.info.height = self.map_height
        map_msg.info.origin.position.x = self.map_origin_x
        map_msg.info.origin.position.y = self.map_origin_y
        map_msg.info.origin.position.z = 0.0
        map_msg.info.origin.orientation.w = 1.0
        
        # Map data (flatten in row-major order)
        map_msg.data = self.dynamic_map.flatten().tolist()
        
        self.dynamic_map_pub.publish(map_msg)
        
        # Log statistics
        occupied_cells = np.sum(self.dynamic_map == 100)
        if occupied_cells > 0:
            self.get_logger().debug(f'Dynamic obstacles: {occupied_cells} cells')

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = DynamicMapUpdater()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
