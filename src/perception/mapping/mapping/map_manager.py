#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import OccupancyGrid, MapMetaData
from geometry_msgs.msg import PoseWithCovarianceStamped
from std_msgs.msg import Float32MultiArray, String
from std_srvs.srv import Empty, SetBool
import numpy as np
import yaml
import os
import time

class MapManager(Node):
    def __init__(self):
        super().__init__('map_manager')
        
        # Parameters
        self.declare_parameter('static_map_file', '')
        self.declare_parameter('save_map_directory', 'maps')
        self.declare_parameter('auto_save_interval', 60.0)  # seconds
        self.declare_parameter('dynamic_update_enabled', True)
        self.declare_parameter('map_frame', 'map')
        
        self.static_map_file = self.get_parameter('static_map_file').value
        self.save_directory = self.get_parameter('save_map_directory').value
        self.auto_save_interval = self.get_parameter('auto_save_interval').value
        self.dynamic_update_enabled = self.get_parameter('dynamic_update_enabled').value
        self.map_frame = self.get_parameter('map_frame').value
        
        # Map storage
        self.static_map = None
        self.dynamic_map = None
        self.combined_map = None
        self.map_metadata = None
        
        # Map update tracking
        self.last_update_time = time.time()
        self.map_changed = False
        
        # Subscribers
        self.static_map_sub = self.create_subscription(
            OccupancyGrid, '/map_static', self.static_map_callback, 10)
        self.dynamic_map_sub = self.create_subscription(
            OccupancyGrid, '/map_dynamic', self.dynamic_map_callback, 10)
        self.fused_map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.fused_map_callback, 10)
        
        # Publishers
        self.combined_map_pub = self.create_publisher(
            OccupancyGrid, '/map_combined', 10)
        self.map_status_pub = self.create_publisher(
            String, '/map_status', 10)
        
        # Services
        self.save_map_srv = self.create_service(
            Empty, 'save_map', self.save_map_callback)
        self.load_map_srv = self.create_service(
            Empty, 'load_map', self.load_map_callback)
        self.reset_dynamic_map_srv = self.create_service(
            Empty, 'reset_dynamic_map', self.reset_dynamic_map_callback)
        self.toggle_dynamic_update_srv = self.create_service(
            SetBool, 'toggle_dynamic_update', self.toggle_dynamic_update_callback)
        
        # Timer for auto-save and map publishing
        self.auto_save_timer = self.create_timer(
            self.auto_save_interval, self.auto_save_map)
        self.publish_timer = self.create_timer(1.0, self.publish_combined_map)
        
        # Load static map if specified
        if self.static_map_file:
            self.load_static_map()
            
        # Create save directory
        os.makedirs(self.save_directory, exist_ok=True)
        
        self.get_logger().info('Map Manager initialized')
        self.get_logger().info(f'Save directory: {self.save_directory}')
        self.get_logger().info(f'Dynamic updates: {self.dynamic_update_enabled}')
        
    def static_map_callback(self, msg):
        """Handle static map updates"""
        self.static_map = msg
        self.map_metadata = msg.info
        self.update_combined_map()
        
    def dynamic_map_callback(self, msg):
        """Handle dynamic map updates"""
        if self.dynamic_update_enabled:
            self.dynamic_map = msg
            self.update_combined_map()
            self.map_changed = True
            
    def fused_map_callback(self, msg):
        """Handle fused sensor map updates"""
        if self.dynamic_update_enabled:
            # Use fused map as dynamic layer
            self.dynamic_map = msg
            self.update_combined_map()
            self.map_changed = True
            
    def update_combined_map(self):
        """Combine static and dynamic maps"""
        if self.static_map is None and self.dynamic_map is None:
            return
            
        # Use the available map as base
        if self.static_map is not None:
            base_map = self.static_map
        else:
            base_map = self.dynamic_map
            
        # Create combined map
        self.combined_map = OccupancyGrid()
        self.combined_map.header.stamp = self.get_clock().now().to_msg()
        self.combined_map.header.frame_id = self.map_frame
        self.combined_map.info = base_map.info
        
        # Convert to numpy arrays
        if self.static_map is not None:
            static_data = np.array(self.static_map.data).reshape(
                (self.static_map.info.height, self.static_map.info.width))
        else:
            static_data = np.full((base_map.info.height, base_map.info.width), -1, dtype=np.int8)
            
        if self.dynamic_map is not None and self.dynamic_update_enabled:
            dynamic_data = np.array(self.dynamic_map.data).reshape(
                (self.dynamic_map.info.height, self.dynamic_map.info.width))
        else:
            dynamic_data = np.full((base_map.info.height, base_map.info.width), -1, dtype=np.int8)
            
        # Combine maps (dynamic obstacles override static)
        combined_data = static_data.copy()
        
        # Apply dynamic obstacles
        dynamic_obstacles = dynamic_data > 50  # Occupied cells
        combined_data[dynamic_obstacles] = dynamic_data[dynamic_obstacles]
        
        # Apply dynamic free space (with lower priority)
        dynamic_free = (dynamic_data >= 0) & (dynamic_data <= 25)
        static_unknown = static_data == -1
        combined_data[dynamic_free & static_unknown] = dynamic_data[dynamic_free & static_unknown]
        
        # Convert back to list
        self.combined_map.data = combined_data.flatten().tolist()
        
    def publish_combined_map(self):
        """Publish the combined map"""
        if self.combined_map is not None:
            self.combined_map.header.stamp = self.get_clock().now().to_msg()
            self.combined_map_pub.publish(self.combined_map)
            
        # Publish status
        status_msg = String()
        status_msg.data = f"Static: {'Yes' if self.static_map else 'No'}, " \
                         f"Dynamic: {'Yes' if self.dynamic_map else 'No'}, " \
                         f"Updates: {'Enabled' if self.dynamic_update_enabled else 'Disabled'}"
        self.map_status_pub.publish(status_msg)
        
    def load_static_map(self):
        """Load static map from file"""
        try:
            if not os.path.exists(self.static_map_file):
                self.get_logger().warn(f'Static map file not found: {self.static_map_file}')
                return
                
            # Load YAML metadata
            yaml_file = self.static_map_file
            if yaml_file.endswith('.pgm'):
                yaml_file = yaml_file.replace('.pgm', '.yaml')
                
            with open(yaml_file, 'r') as f:
                map_yaml = yaml.safe_load(f)
                
            # Load PGM image
            pgm_file = map_yaml['image']
            if not os.path.isabs(pgm_file):
                pgm_file = os.path.join(os.path.dirname(yaml_file), pgm_file)
                
            map_data = self.load_pgm_image(pgm_file)
            
            # Create OccupancyGrid message
            self.static_map = OccupancyGrid()
            self.static_map.header.stamp = self.get_clock().now().to_msg()
            self.static_map.header.frame_id = self.map_frame
            
            # Set metadata
            self.static_map.info.resolution = map_yaml['resolution']
            self.static_map.info.width = map_data.shape[1]
            self.static_map.info.height = map_data.shape[0]
            self.static_map.info.origin.position.x = map_yaml['origin'][0]
            self.static_map.info.origin.position.y = map_yaml['origin'][1]
            self.static_map.info.origin.orientation.w = 1.0
            
            # Convert image data to occupancy grid
            occupied_thresh = map_yaml.get('occupied_thresh', 0.65)
            free_thresh = map_yaml.get('free_thresh', 0.196)
            
            occupancy_data = np.full_like(map_data, -1, dtype=np.int8)  # Unknown
            occupancy_data[map_data < free_thresh * 255] = 0  # Free
            occupancy_data[map_data > occupied_thresh * 255] = 100  # Occupied
            
            self.static_map.data = occupancy_data.flatten().tolist()
            
            self.get_logger().info(f'Static map loaded: {self.static_map_file}')
            self.update_combined_map()
            
        except Exception as e:
            self.get_logger().error(f'Failed to load static map: {e}')
            
    def load_pgm_image(self, filename):
        """Load PGM image file"""
        with open(filename, 'rb') as f:
            # Read header
            magic = f.readline().decode().strip()
            if magic != 'P5':
                raise ValueError('Only P5 PGM format supported')
                
            # Skip comments
            line = f.readline().decode().strip()
            while line.startswith('#'):
                line = f.readline().decode().strip()
                
            # Read dimensions
            width, height = map(int, line.split())
            
            # Read max value
            max_val = int(f.readline().decode().strip())
            
            # Read image data
            image_data = np.frombuffer(f.read(), dtype=np.uint8)
            image_data = image_data.reshape((height, width))
            
        return image_data
        
    def auto_save_map(self):
        """Auto-save map if changed"""
        if self.map_changed and self.combined_map is not None:
            timestamp = int(time.time())
            filename = f'auto_save_{timestamp}'
            self.save_map_to_file(filename)
            self.map_changed = False
            
    def save_map_to_file(self, filename):
        """Save current map to file"""
        if self.combined_map is None:
            self.get_logger().warn('No map to save')
            return False
            
        try:
            # Create filenames
            yaml_file = os.path.join(self.save_directory, f'{filename}.yaml')
            pgm_file = os.path.join(self.save_directory, f'{filename}.pgm')
            
            # Save PGM image
            map_data = np.array(self.combined_map.data).reshape(
                (self.combined_map.info.height, self.combined_map.info.width))
            
            # Convert occupancy grid to image
            image_data = np.full_like(map_data, 127, dtype=np.uint8)  # Unknown = gray
            image_data[map_data == 0] = 254    # Free = white
            image_data[map_data == 100] = 0    # Occupied = black
            
            with open(pgm_file, 'wb') as f:
                f.write(f'P5\n{self.combined_map.info.width} {self.combined_map.info.height}\n255\n'.encode())
                f.write(image_data.tobytes())
                
            # Save YAML metadata
            yaml_data = {
                'image': f'{filename}.pgm',
                'resolution': self.combined_map.info.resolution,
                'origin': [
                    self.combined_map.info.origin.position.x,
                    self.combined_map.info.origin.position.y,
                    0.0
                ],
                'negate': 0,
                'occupied_thresh': 0.65,
                'free_thresh': 0.196
            }
            
            with open(yaml_file, 'w') as f:
                yaml.dump(yaml_data, f)
                
            self.get_logger().info(f'Map saved: {filename}')
            return True
            
        except Exception as e:
            self.get_logger().error(f'Failed to save map: {e}')
            return False
            
    def save_map_callback(self, request, response):
        """Service callback to save map"""
        timestamp = int(time.time())
        filename = f'manual_save_{timestamp}'
        success = self.save_map_to_file(filename)
        
        if success:
            self.get_logger().info('Map saved successfully')
        else:
            self.get_logger().error('Failed to save map')
            
        return response
        
    def load_map_callback(self, request, response):
        """Service callback to reload static map"""
        if self.static_map_file:
            self.load_static_map()
            self.get_logger().info('Static map reloaded')
        else:
            self.get_logger().warn('No static map file specified')
            
        return response
        
    def reset_dynamic_map_callback(self, request, response):
        """Service callback to reset dynamic map"""
        self.dynamic_map = None
        self.update_combined_map()
        self.get_logger().info('Dynamic map reset')
        return response
        
    def toggle_dynamic_update_callback(self, request, response):
        """Service callback to toggle dynamic updates"""
        self.dynamic_update_enabled = request.data
        self.get_logger().info(f'Dynamic updates: {self.dynamic_update_enabled}')
        response.success = True
        response.message = f'Dynamic updates {"enabled" if self.dynamic_update_enabled else "disabled"}'
        return response

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = MapManager()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
