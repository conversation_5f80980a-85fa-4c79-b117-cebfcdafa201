#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import OccupancyGrid, MapMetaData
from geometry_msgs.msg import PoseWithCovarianceStamped, PoseStamped
from sensor_msgs.msg import LaserScan
from std_msgs.msg import String, Bool
from std_srvs.srv import Empty, SetBool
import numpy as np
import time
import threading
from typing import Optional, Dict, Any

class RealtimeSLAMManager(Node):
    """
    Enhanced SLAM Manager for real-time mapping and localization
    Phase 2: Advanced Features
    """
    
    def __init__(self):
        super().__init__('realtime_slam_manager')
        
        # Parameters
        self.declare_parameter('map_update_rate', 5.0)  # Hz
        self.declare_parameter('pose_update_rate', 20.0)  # Hz
        self.declare_parameter('quality_threshold', 0.7)  # Map quality threshold
        self.declare_parameter('auto_save_interval', 60.0)  # seconds
        self.declare_parameter('loop_closure_enabled', True)
        self.declare_parameter('dynamic_obstacles_enabled', True)
        
        self.map_update_rate = self.get_parameter('map_update_rate').value
        self.pose_update_rate = self.get_parameter('pose_update_rate').value
        self.quality_threshold = self.get_parameter('quality_threshold').value
        self.auto_save_interval = self.get_parameter('auto_save_interval').value
        self.loop_closure_enabled = self.get_parameter('loop_closure_enabled').value
        self.dynamic_obstacles_enabled = self.get_parameter('dynamic_obstacles_enabled').value
        
        # State variables
        self.current_map: Optional[OccupancyGrid] = None
        self.current_pose: Optional[PoseWithCovarianceStamped] = None
        self.current_scan: Optional[LaserScan] = None
        self.slam_active = False
        self.map_quality = 0.0
        self.loop_closures_detected = 0
        self.mapping_start_time = time.time()
        
        # Statistics
        self.stats = {
            'total_scans_processed': 0,
            'map_updates': 0,
            'loop_closures': 0,
            'average_processing_time': 0.0,
            'map_coverage': 0.0
        }
        
        # Subscribers
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map_slam', self.map_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)

        # Publishers
        self.enhanced_map_pub = self.create_publisher(
            OccupancyGrid, '/map_enhanced', 10)
        self.map_pub = self.create_publisher(
            OccupancyGrid, '/map', 10)
        self.slam_status_pub = self.create_publisher(
            String, '/slam/status', 10)
        self.map_quality_pub = self.create_publisher(
            String, '/slam/quality', 10)
        self.slam_stats_pub = self.create_publisher(
            String, '/slam/statistics', 10)
        
        # Services
        self.start_slam_srv = self.create_service(
            SetBool, '/slam/start_stop', self.start_stop_slam_callback)
        self.save_map_srv = self.create_service(
            Empty, '/slam/save_map', self.save_map_callback)
        self.reset_slam_srv = self.create_service(
            Empty, '/slam/reset', self.reset_slam_callback)
        self.clear_map_srv = self.create_service(
            Empty, '/slam/clear_map', self.clear_map_callback)
        
        # Timers
        self.status_timer = self.create_timer(1.0, self.publish_status)
        self.quality_timer = self.create_timer(2.0, self.analyze_map_quality)
        self.stats_timer = self.create_timer(5.0, self.publish_statistics)
        self.auto_save_timer = self.create_timer(
            self.auto_save_interval, self.auto_save_map)
        
        # Threading for real-time processing
        self.processing_lock = threading.Lock()
        
        self.get_logger().info('🗺️ Real-time SLAM Manager initialized')
        self.get_logger().info(f'📊 Map update rate: {self.map_update_rate} Hz')
        self.get_logger().info(f'📍 Pose update rate: {self.pose_update_rate} Hz')
        
    def map_callback(self, msg: OccupancyGrid):
        """Process incoming map updates with detailed logging"""
        with self.processing_lock:
            start_time = time.time()

            # Log map update details
            if self.stats['map_updates'] % 10 == 0:  # Every 10th update
                self.get_logger().info(f'🗺️ Map update #{self.stats["map_updates"]}:')
                self.get_logger().info(f'   Size: {msg.info.width}x{msg.info.height}')
                self.get_logger().info(f'   Resolution: {msg.info.resolution:.3f}m/px')
                self.get_logger().info(f'   Origin: ({msg.info.origin.position.x:.3f}, {msg.info.origin.position.y:.3f})')
                self.get_logger().info(f'   Data length: {len(msg.data)}')

            self.current_map = msg
            self.stats['map_updates'] += 1

            # Enhance map with additional processing
            enhanced_map = self.enhance_map(msg)
            self.enhanced_map_pub.publish(enhanced_map)

            # Also publish to /map for backend consumption
            self.map_pub.publish(enhanced_map)

            # Update processing time statistics
            processing_time = time.time() - start_time
            self.update_processing_stats(processing_time)

            # Log processing time if it's unusually high
            if processing_time > 0.1:  # More than 100ms
                self.get_logger().warn(f'⚠️ Slow map processing: {processing_time:.3f}s')
            
    def pose_callback(self, msg: PoseWithCovarianceStamped):
        """Process pose updates with teleport detection"""
        # Check for teleportation
        if self.current_pose is not None:
            old_x = self.current_pose.pose.pose.position.x
            old_y = self.current_pose.pose.pose.position.y
            new_x = msg.pose.pose.position.x
            new_y = msg.pose.pose.position.y

            distance_moved = ((new_x - old_x)**2 + (new_y - old_y)**2)**0.5

            # Log significant pose changes
            if distance_moved > 0.5:  # More than 50cm movement
                self.get_logger().info(f'📍 Large pose change detected:')
                self.get_logger().info(f'   From: ({old_x:.3f}, {old_y:.3f})')
                self.get_logger().info(f'   To: ({new_x:.3f}, {new_y:.3f})')
                self.get_logger().info(f'   Distance: {distance_moved:.3f}m')

                # Check for potential teleportation
                if distance_moved > 2.0:  # More than 2m is suspicious
                    self.get_logger().error(f'🚨 POTENTIAL TELEPORT IN SLAM POSE!')
                    self.get_logger().error(f'   Distance: {distance_moved:.3f}m')
                    self.get_logger().error(f'   Frame: {msg.header.frame_id}')
                    self.get_logger().error(f'   Timestamp: {msg.header.stamp.sec}.{msg.header.stamp.nanosec}')

        self.current_pose = msg
        
    def scan_callback(self, msg: LaserScan):
        """Process laser scan data"""
        self.current_scan = msg
        self.stats['total_scans_processed'] += 1
        
        # Detect potential loop closures
        if self.loop_closure_enabled:
            self.detect_loop_closure(msg)
            
    def enhance_map(self, original_map: OccupancyGrid) -> OccupancyGrid:
        """Enhance map with proper data handling - no accumulation"""
        enhanced_map = OccupancyGrid()
        enhanced_map.header = original_map.header
        enhanced_map.info = original_map.info

        # Convert to numpy array for processing
        width = original_map.info.width
        height = original_map.info.height

        if len(original_map.data) != width * height:
            self.get_logger().warn(f'Map data size mismatch: expected {width*height}, got {len(original_map.data)}')
            enhanced_map.data = original_map.data
            return enhanced_map

        data = np.array(original_map.data, dtype=np.int8).reshape((height, width))

        # Apply enhancements WITHOUT accumulating old data
        if self.dynamic_obstacles_enabled:
            data = self.filter_dynamic_obstacles(data)

        # Apply light smoothing filter (preserve map accuracy)
        data = self.apply_light_smoothing(data)

        # Convert back to message format - ensure proper data types
        enhanced_map.data = data.flatten().astype(int).tolist()

        return enhanced_map
        
    def filter_dynamic_obstacles(self, map_data: np.ndarray) -> np.ndarray:
        """Filter out dynamic obstacles from static map"""
        # Keep only static obstacles (walls, furniture)
        # Remove temporary obstacles that might be people or moving objects

        # Create a copy to avoid modifying original
        filtered_data = map_data.copy()

        # For now, just return the data as-is
        # In a real implementation, this would use temporal analysis
        return filtered_data

    def apply_light_smoothing(self, map_data: np.ndarray) -> np.ndarray:
        """Apply advanced noise filtering to reduce noise while preserving map detail"""
        try:
            # Create a copy to work with
            filtered_data = map_data.copy().astype(np.float32)

            # Step 1: Remove isolated noise pixels (salt and pepper noise)
            filtered_data = self.remove_isolated_pixels(filtered_data)

            # Step 2: Apply median filter to reduce noise
            filtered_data = self.apply_median_filter(filtered_data)

            # Step 3: Apply light Gaussian smoothing for final cleanup
            filtered_data = self.apply_gaussian_smoothing(filtered_data)

            return filtered_data.astype(np.int8)

        except Exception as e:
            self.get_logger().warn(f'Advanced filtering failed: {e}')
            return map_data

    def remove_isolated_pixels(self, map_data: np.ndarray) -> np.ndarray:
        """Remove isolated noise pixels (salt and pepper noise)"""
        filtered = map_data.copy()
        height, width = map_data.shape

        for i in range(1, height-1):
            for j in range(1, width-1):
                # Get 3x3 neighborhood
                neighborhood = map_data[i-1:i+2, j-1:j+2]
                center_val = map_data[i, j]

                # Count similar values in neighborhood
                if center_val == 100:  # Occupied pixel
                    occupied_count = np.sum(neighborhood == 100)
                    if occupied_count <= 2:  # Isolated occupied pixel
                        # Replace with most common neighbor value
                        free_count = np.sum(neighborhood == 0)
                        unknown_count = np.sum(neighborhood == -1)
                        if free_count > unknown_count:
                            filtered[i, j] = 0
                        else:
                            filtered[i, j] = -1

                elif center_val == 0:  # Free pixel
                    free_count = np.sum(neighborhood == 0)
                    if free_count <= 2:  # Isolated free pixel
                        # Replace with most common neighbor value
                        occupied_count = np.sum(neighborhood == 100)
                        unknown_count = np.sum(neighborhood == -1)
                        if occupied_count > unknown_count:
                            filtered[i, j] = 100
                        else:
                            filtered[i, j] = -1

        return filtered

    def apply_median_filter(self, map_data: np.ndarray) -> np.ndarray:
        """Apply median filter to reduce noise"""
        filtered = map_data.copy()
        height, width = map_data.shape

        for i in range(1, height-1):
            for j in range(1, width-1):
                # Get 3x3 neighborhood
                neighborhood = map_data[i-1:i+2, j-1:j+2].flatten()

                # Only apply median filter to non-obstacle areas to preserve walls
                if map_data[i, j] != 100:
                    # Calculate median of neighborhood
                    median_val = np.median(neighborhood)
                    filtered[i, j] = median_val

        return filtered

    def apply_gaussian_smoothing(self, map_data: np.ndarray) -> np.ndarray:
        """Apply light Gaussian smoothing for final cleanup"""
        # Light Gaussian kernel for final smoothing
        kernel = np.array([[0.05, 0.1, 0.05],
                          [0.1,  0.4, 0.1],
                          [0.05, 0.1, 0.05]])

        smoothed = map_data.copy()
        height, width = map_data.shape

        for i in range(1, height-1):
            for j in range(1, width-1):
                # Only smooth non-obstacle areas to preserve wall sharpness
                if map_data[i, j] != 100:
                    neighborhood = map_data[i-1:i+2, j-1:j+2]
                    # Only smooth if no obstacles in immediate neighborhood
                    if not np.any(neighborhood == 100):
                        smoothed[i, j] = np.sum(neighborhood * kernel)

        return smoothed
            
    def detect_loop_closure(self, scan: LaserScan):
        """Detect potential loop closures"""
        # Simplified loop closure detection
        # In a real implementation, this would use more sophisticated algorithms
        if self.current_pose is not None:
            # Check if we've been to this location before
            # This is a placeholder for actual loop closure detection
            pass
            
    def analyze_map_quality(self):
        """Analyze and publish map quality metrics"""
        if self.current_map is None:
            return
            
        # Calculate map quality metrics
        width = self.current_map.info.width
        height = self.current_map.info.height
        data = np.array(self.current_map.data)
        
        # Calculate coverage (non-unknown cells)
        known_cells = np.sum(data >= 0)
        total_cells = len(data)
        coverage = known_cells / total_cells if total_cells > 0 else 0.0
        
        # Calculate quality based on various factors
        self.map_quality = min(coverage * 1.2, 1.0)  # Simple quality metric
        self.stats['map_coverage'] = coverage
        
        # Publish quality
        quality_msg = String()
        quality_msg.data = f"quality:{self.map_quality:.3f},coverage:{coverage:.3f}"
        self.map_quality_pub.publish(quality_msg)
        
    def update_processing_stats(self, processing_time: float):
        """Update processing time statistics"""
        if self.stats['map_updates'] == 1:
            self.stats['average_processing_time'] = processing_time
        else:
            # Running average
            alpha = 0.1  # Smoothing factor
            self.stats['average_processing_time'] = (
                alpha * processing_time + 
                (1 - alpha) * self.stats['average_processing_time']
            )
            
    def publish_status(self):
        """Publish SLAM status"""
        status_msg = String()
        
        if self.slam_active:
            uptime = time.time() - self.mapping_start_time
            status_data = {
                'status': 'ACTIVE',
                'uptime': f'{uptime:.1f}s',
                'quality': f'{self.map_quality:.3f}',
                'scans': self.stats['total_scans_processed'],
                'updates': self.stats['map_updates']
            }
        else:
            status_data = {'status': 'INACTIVE'}
            
        status_msg.data = ','.join([f'{k}:{v}' for k, v in status_data.items()])
        self.slam_status_pub.publish(status_msg)
        
    def publish_statistics(self):
        """Publish detailed SLAM statistics"""
        stats_msg = String()
        stats_data = {
            'scans_processed': self.stats['total_scans_processed'],
            'map_updates': self.stats['map_updates'],
            'loop_closures': self.stats['loop_closures'],
            'avg_processing_time': f"{self.stats['average_processing_time']:.4f}s",
            'map_coverage': f"{self.stats['map_coverage']:.3f}",
            'quality': f'{self.map_quality:.3f}'
        }
        
        stats_msg.data = ','.join([f'{k}:{v}' for k, v in stats_data.items()])
        self.slam_stats_pub.publish(stats_msg)
        
    def start_stop_slam_callback(self, request, response):
        """Service to start/stop SLAM"""
        self.slam_active = request.data
        
        if self.slam_active:
            self.mapping_start_time = time.time()
            self.get_logger().info('🚀 SLAM started')
            response.message = 'SLAM started successfully'
        else:
            self.get_logger().info('🛑 SLAM stopped')
            response.message = 'SLAM stopped successfully'
            
        response.success = True
        return response
        
    def save_map_callback(self, request, response):
        """Service to save current map"""
        if self.current_map is not None:
            # In a real implementation, this would save the map to file
            self.get_logger().info('💾 Map saved successfully')
        else:
            self.get_logger().warn('⚠️ No map available to save')
            
        return response
        
    def reset_slam_callback(self, request, response):
        """Service to reset SLAM"""
        self.stats = {
            'total_scans_processed': 0,
            'map_updates': 0,
            'loop_closures': 0,
            'average_processing_time': 0.0,
            'map_coverage': 0.0
        }
        self.map_quality = 0.0
        self.mapping_start_time = time.time()
        
        self.get_logger().info('🔄 SLAM reset successfully')
        return response
        
    def clear_map_callback(self, request, response):
        """Service to clear accumulated map data"""
        self.current_map = None
        self.stats = {
            'total_scans_processed': 0,
            'map_updates': 0,
            'loop_closures': 0,
            'average_processing_time': 0.0,
            'map_coverage': 0.0
        }
        self.map_quality = 0.0

        self.get_logger().info('🧹 Map data cleared - fresh start')
        return response

    def auto_save_map(self):
        """Automatically save map at intervals"""
        if self.slam_active and self.current_map is not None:
            if self.map_quality > self.quality_threshold:
                self.get_logger().info(f'💾 Auto-saving map (quality: {self.map_quality:.3f})')
                # Auto-save implementation would go here

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = RealtimeSLAMManager()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
