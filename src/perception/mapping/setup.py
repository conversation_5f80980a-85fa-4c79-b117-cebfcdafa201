from setuptools import setup

package_name = 'mapping'

setup(
    name=package_name,
    version='1.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/mapping.launch.py']),
        ('share/' + package_name + '/launch', ['launch/slam.launch.py']),
        ('share/' + package_name + '/config', ['config/slam_params.yaml']),
        ('share/' + package_name + '/config', ['config/mapping_params.yaml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='Mapping and SLAM for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'map_manager = mapping.map_manager:main',
            'dynamic_map_updater = mapping.dynamic_map_updater:main',
            'map_saver = mapping.map_saver:main',
            'realtime_slam_manager = mapping.realtime_slam_manager:main',
        ],
    },
)
