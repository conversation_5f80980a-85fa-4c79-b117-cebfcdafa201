#!/usr/bin/env python3

import os
from launch import LaunchDes<PERSON>
from launch.actions import DeclareLaunchArgument
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_decision_making = get_package_share_directory('decision_making')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(pkg_decision_making, 'config', 'behavior_params.yaml'),
        description='Full path to behavior parameters file'
    )
    
    use_behavior_coordinator_arg = DeclareLaunchArgument(
        'use_behavior_coordinator',
        default_value='true',
        description='Enable behavior coordinator'
    )
    
    use_mission_planner_arg = DeclareLaunchArgument(
        'use_mission_planner',
        default_value='true',
        description='Enable mission planner'
    )
    
    use_safety_monitor_arg = DeclareLaunchArgument(
        'use_safety_monitor',
        default_value='true',
        description='Enable safety monitor'
    )
    
    # Behavior Coordinator Node
    behavior_coordinator_node = Node(
        package='decision_making',
        executable='behavior_coordinator',
        name='behavior_coordinator',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_behavior_coordinator'))
    )
    
    # Mission Planner Node
    mission_planner_node = Node(
        package='decision_making',
        executable='mission_planner',
        name='mission_planner',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_mission_planner'))
    )
    
    # Safety Monitor Node
    safety_monitor_node = Node(
        package='decision_making',
        executable='safety_monitor',
        name='safety_monitor',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_safety_monitor'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        params_file_arg,
        use_behavior_coordinator_arg,
        use_mission_planner_arg,
        use_safety_monitor_arg,
        
        # Nodes
        behavior_coordinator_node,
        mission_planner_node,
        safety_monitor_node,
    ])
