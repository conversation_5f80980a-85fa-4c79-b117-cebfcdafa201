#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Bool
from geometry_msgs.msg import PoseStamped, Twist
from sensor_msgs.msg import LaserScan
import time
import json

class BehaviorCoordinator(Node):
    def __init__(self):
        super().__init__('behavior_coordinator')
        
        # Parameters
        self.declare_parameter('default_behavior', 'idle')
        self.declare_parameter('emergency_stop_enabled', True)
        self.declare_parameter('behavior_timeout', 60.0)
        
        self.default_behavior = self.get_parameter('default_behavior').value
        self.emergency_stop_enabled = self.get_parameter('emergency_stop_enabled').value
        self.behavior_timeout = self.get_parameter('behavior_timeout').value
        
        # State machine
        self.current_behavior = self.default_behavior
        self.behavior_start_time = time.time()
        self.emergency_stop = False
        self.system_status = {}
        
        # Behavior states
        self.behaviors = {
            'idle': self.idle_behavior,
            'navigation': self.navigation_behavior,
            'exploration': self.exploration_behavior,
            'emergency_stop': self.emergency_stop_behavior,
            'recovery': self.recovery_behavior,
            'manual_control': self.manual_control_behavior
        }
        
        # Subscribers for system status
        self.localization_status_sub = self.create_subscription(
            String, '/localization/status', self.localization_status_callback, 10)
        self.path_executor_status_sub = self.create_subscription(
            String, '/path_executor/status', self.path_executor_status_callback, 10)
        self.safety_status_sub = self.create_subscription(
            String, '/safety_monitor/status', self.safety_status_callback, 10)
        self.mission_status_sub = self.create_subscription(
            String, '/mission_planner/status', self.mission_status_callback, 10)
        
        # Emergency stop subscriber
        self.emergency_stop_sub = self.create_subscription(
            Bool, '/emergency_stop', self.emergency_stop_callback, 10)
        
        # Publishers
        self.behavior_status_pub = self.create_publisher(
            String, '/behavior_coordinator/status', 10)
        self.system_command_pub = self.create_publisher(
            String, '/system_command', 10)
        self.cmd_vel_override_pub = self.create_publisher(
            Twist, '/cmd_vel_override', 10)
        
        # Timer for behavior execution
        self.behavior_timer = self.create_timer(0.1, self.execute_behavior)  # 10 Hz
        
        # Timer for status monitoring
        self.status_timer = self.create_timer(1.0, self.monitor_system_status)
        
        self.get_logger().info(f'Behavior Coordinator initialized with default behavior: {self.default_behavior}')
        
    def localization_status_callback(self, msg):
        """Update localization status"""
        self.system_status['localization'] = msg.data
        
    def path_executor_status_callback(self, msg):
        """Update path executor status"""
        self.system_status['path_executor'] = msg.data
        
    def safety_status_callback(self, msg):
        """Update safety monitor status"""
        self.system_status['safety'] = msg.data
        
        # Check for safety issues
        if 'EMERGENCY' in msg.data or 'CRITICAL' in msg.data:
            self.trigger_emergency_stop('Safety system triggered')
            
    def mission_status_callback(self, msg):
        """Update mission planner status"""
        self.system_status['mission'] = msg.data
        
    def emergency_stop_callback(self, msg):
        """Handle emergency stop command"""
        if msg.data:
            self.trigger_emergency_stop('Manual emergency stop')
        else:
            self.clear_emergency_stop()
            
    def trigger_emergency_stop(self, reason):
        """Trigger emergency stop"""
        if not self.emergency_stop:
            self.emergency_stop = True
            self.change_behavior('emergency_stop')
            self.get_logger().error(f'EMERGENCY STOP TRIGGERED: {reason}')
            
    def clear_emergency_stop(self):
        """Clear emergency stop"""
        if self.emergency_stop:
            self.emergency_stop = False
            self.change_behavior('recovery')
            self.get_logger().info('Emergency stop cleared, entering recovery mode')
            
    def change_behavior(self, new_behavior):
        """Change current behavior"""
        if new_behavior in self.behaviors:
            old_behavior = self.current_behavior
            self.current_behavior = new_behavior
            self.behavior_start_time = time.time()
            
            self.get_logger().info(f'Behavior changed: {old_behavior} -> {new_behavior}')
            
            # Publish behavior change
            command_msg = String()
            command_msg.data = f'BEHAVIOR_CHANGE:{old_behavior}:{new_behavior}'
            self.system_command_pub.publish(command_msg)
        else:
            self.get_logger().error(f'Unknown behavior: {new_behavior}')
            
    def execute_behavior(self):
        """Execute current behavior"""
        if self.current_behavior in self.behaviors:
            self.behaviors[self.current_behavior]()
        else:
            self.get_logger().error(f'Behavior not implemented: {self.current_behavior}')
            self.change_behavior('idle')
            
    def idle_behavior(self):
        """Idle behavior - wait for commands"""
        # Check if there's a mission to execute
        mission_status = self.system_status.get('mission', '')
        if 'MISSION_READY' in mission_status:
            self.change_behavior('navigation')
            
    def navigation_behavior(self):
        """Navigation behavior - follow planned paths"""
        path_status = self.system_status.get('path_executor', '')
        
        if 'FAILED' in path_status:
            self.get_logger().warn('Navigation failed, entering recovery mode')
            self.change_behavior('recovery')
        elif 'COMPLETED' in path_status:
            self.get_logger().info('Navigation completed, returning to idle')
            self.change_behavior('idle')
        elif 'EXECUTING' not in path_status and 'PLANNING' not in path_status:
            # Check timeout
            elapsed_time = time.time() - self.behavior_start_time
            if elapsed_time > self.behavior_timeout:
                self.get_logger().warn('Navigation timeout, entering recovery')
                self.change_behavior('recovery')
                
    def exploration_behavior(self):
        """Exploration behavior - autonomous exploration"""
        # Simple exploration: send random goals
        elapsed_time = time.time() - self.behavior_start_time
        
        if elapsed_time > 30.0:  # Explore for 30 seconds then return to idle
            self.change_behavior('idle')
        else:
            # Send exploration commands
            command_msg = String()
            command_msg.data = 'EXPLORE:RANDOM_GOAL'
            self.system_command_pub.publish(command_msg)
            
    def emergency_stop_behavior(self):
        """Emergency stop behavior - stop all motion"""
        # Publish stop command
        stop_cmd = Twist()
        self.cmd_vel_override_pub.publish(stop_cmd)
        
        # Send stop command to all systems
        command_msg = String()
        command_msg.data = 'EMERGENCY_STOP:ALL_SYSTEMS'
        self.system_command_pub.publish(command_msg)
        
    def recovery_behavior(self):
        """Recovery behavior - attempt to recover from errors"""
        elapsed_time = time.time() - self.behavior_start_time
        
        if elapsed_time < 5.0:
            # First 5 seconds: stop and assess
            stop_cmd = Twist()
            self.cmd_vel_override_pub.publish(stop_cmd)
        elif elapsed_time < 10.0:
            # Next 5 seconds: try to clear path
            command_msg = String()
            command_msg.data = 'RECOVERY:CLEAR_PATH'
            self.system_command_pub.publish(command_msg)
        else:
            # After 10 seconds: return to idle
            self.get_logger().info('Recovery completed, returning to idle')
            self.change_behavior('idle')
            
    def manual_control_behavior(self):
        """Manual control behavior - human operator control"""
        # In manual mode, just monitor for return to autonomous
        command_msg = String()
        command_msg.data = 'MANUAL_CONTROL:ACTIVE'
        self.system_command_pub.publish(command_msg)
        
    def monitor_system_status(self):
        """Monitor overall system status"""
        # Check localization
        localization_ok = self.check_localization_health()
        
        # Check safety systems
        safety_ok = self.check_safety_health()
        
        # Determine overall system health
        system_health = 'HEALTHY' if (localization_ok and safety_ok) else 'DEGRADED'
        
        # Publish behavior status
        status_data = {
            'behavior': self.current_behavior,
            'health': system_health,
            'emergency_stop': self.emergency_stop,
            'uptime': time.time() - self.behavior_start_time,
            'system_status': self.system_status
        }
        
        status_msg = String()
        status_msg.data = json.dumps(status_data)
        self.behavior_status_pub.publish(status_msg)
        
    def check_localization_health(self):
        """Check if localization is healthy"""
        localization_status = self.system_status.get('localization', '')
        
        if 'LOCALIZED' in localization_status:
            return True
        elif 'TIMEOUT' in localization_status or 'POOR' in localization_status:
            return False
        else:
            return True  # Assume OK if no status
            
    def check_safety_health(self):
        """Check if safety systems are healthy"""
        safety_status = self.system_status.get('safety', '')
        
        if 'SAFE' in safety_status or safety_status == '':
            return True
        elif 'WARNING' in safety_status:
            return True  # Warning is OK, just degraded
        else:
            return False
            
    def handle_system_command(self, command):
        """Handle external system commands"""
        if command == 'START_NAVIGATION':
            if self.current_behavior == 'idle':
                self.change_behavior('navigation')
        elif command == 'START_EXPLORATION':
            if self.current_behavior == 'idle':
                self.change_behavior('exploration')
        elif command == 'STOP':
            self.change_behavior('idle')
        elif command == 'EMERGENCY_STOP':
            self.trigger_emergency_stop('External command')
        elif command == 'MANUAL_CONTROL':
            self.change_behavior('manual_control')
        elif command == 'AUTO_CONTROL':
            if self.current_behavior == 'manual_control':
                self.change_behavior('idle')

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = BehaviorCoordinator()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
