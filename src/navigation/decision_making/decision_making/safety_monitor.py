#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Bool, Float32
from sensor_msgs.msg import LaserScan
from geometry_msgs.msg import Twist, PoseWithCovarianceStamped
import math
import time

class SafetyMonitor(Node):
    def __init__(self):
        super().__init__('safety_monitor')
        
        # Parameters
        self.declare_parameter('emergency_stop_distance', 0.3)  # meters
        self.declare_parameter('warning_distance', 0.8)  # meters
        self.declare_parameter('max_linear_velocity', 0.5)  # m/s
        self.declare_parameter('max_angular_velocity', 1.0)  # rad/s
        self.declare_parameter('localization_timeout', 5.0)  # seconds
        self.declare_parameter('heartbeat_timeout', 2.0)  # seconds
        
        self.emergency_distance = self.get_parameter('emergency_stop_distance').value
        self.warning_distance = self.get_parameter('warning_distance').value
        self.max_linear_vel = self.get_parameter('max_linear_velocity').value
        self.max_angular_vel = self.get_parameter('max_angular_velocity').value
        self.localization_timeout = self.get_parameter('localization_timeout').value
        self.heartbeat_timeout = self.get_parameter('heartbeat_timeout').value
        
        # Safety state
        self.safety_level = 'SAFE'  # SAFE, WARNING, CRITICAL, EMERGENCY
        self.emergency_stop_active = False
        self.last_scan_time = None
        self.last_pose_time = None
        self.last_heartbeat_time = time.time()
        self.min_obstacle_distance = float('inf')
        
        # System health tracking
        self.system_health = {
            'localization': True,
            'sensors': True,
            'communication': True,
            'motion': True
        }
        
        # Subscribers
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10)
        self.localization_quality_sub = self.create_subscription(
            Float32, '/localization/quality', self.localization_quality_callback, 10)
        self.heartbeat_sub = self.create_subscription(
            String, '/system_heartbeat', self.heartbeat_callback, 10)
        
        # Publishers
        self.safety_status_pub = self.create_publisher(String, '/safety_monitor/status', 10)
        self.emergency_stop_pub = self.create_publisher(Bool, '/emergency_stop', 10)
        self.cmd_vel_safe_pub = self.create_publisher(Twist, '/cmd_vel_safe', 10)
        
        # Timer for safety monitoring
        self.safety_timer = self.create_timer(0.1, self.monitor_safety)  # 10 Hz
        
        self.get_logger().info('Safety Monitor initialized')
        
    def scan_callback(self, msg):
        """Process laser scan for obstacle detection"""
        self.last_scan_time = time.time()
        
        # Find minimum distance to obstacles
        valid_ranges = []
        for i, range_val in enumerate(msg.ranges):
            if not math.isnan(range_val) and not math.isinf(range_val):
                if msg.range_min <= range_val <= msg.range_max:
                    # Check if obstacle is in front (within 60 degrees)
                    angle = msg.angle_min + i * msg.angle_increment
                    if abs(angle) < math.pi / 3:  # 60 degrees
                        valid_ranges.append(range_val)
                        
        if valid_ranges:
            self.min_obstacle_distance = min(valid_ranges)
        else:
            self.min_obstacle_distance = float('inf')
            
        # Update sensor health
        self.system_health['sensors'] = True
        
    def pose_callback(self, msg):
        """Monitor localization health"""
        self.last_pose_time = time.time()
        self.system_health['localization'] = True
        
    def cmd_vel_callback(self, msg):
        """Monitor and filter velocity commands"""
        # Apply safety limits and publish safe velocity
        safe_cmd = self.apply_safety_limits(msg)
        self.cmd_vel_safe_pub.publish(safe_cmd)
        
        # Update motion health
        self.system_health['motion'] = True
        
    def localization_quality_callback(self, msg):
        """Monitor localization quality"""
        quality = msg.data
        
        # Update localization health based on quality
        if quality < 0.3:
            self.system_health['localization'] = False
        else:
            self.system_health['localization'] = True
            
    def heartbeat_callback(self, msg):
        """Monitor system heartbeat"""
        self.last_heartbeat_time = time.time()
        self.system_health['communication'] = True
        
    def apply_safety_limits(self, cmd_vel):
        """Apply safety limits to velocity commands"""
        safe_cmd = Twist()
        
        # Check emergency conditions
        if self.safety_level == 'EMERGENCY' or self.emergency_stop_active:
            # Complete stop
            return safe_cmd
            
        # Apply velocity limits
        safe_cmd.linear.x = max(-self.max_linear_vel, min(self.max_linear_vel, cmd_vel.linear.x))
        safe_cmd.linear.y = max(-self.max_linear_vel, min(self.max_linear_vel, cmd_vel.linear.y))
        safe_cmd.angular.z = max(-self.max_angular_vel, min(self.max_angular_vel, cmd_vel.angular.z))
        
        # Apply obstacle-based speed reduction
        if self.min_obstacle_distance < self.warning_distance:
            # Reduce speed based on distance to obstacle
            speed_factor = max(0.1, (self.min_obstacle_distance - self.emergency_distance) / 
                             (self.warning_distance - self.emergency_distance))
            
            safe_cmd.linear.x *= speed_factor
            safe_cmd.linear.y *= speed_factor
            safe_cmd.angular.z *= speed_factor
            
        # Emergency stop for very close obstacles
        if self.min_obstacle_distance < self.emergency_distance:
            safe_cmd = Twist()  # Complete stop
            
        return safe_cmd
        
    def check_sensor_health(self):
        """Check sensor system health"""
        current_time = time.time()
        
        # Check laser scan timeout
        if self.last_scan_time is None:
            return False
        elif current_time - self.last_scan_time > 1.0:  # 1 second timeout
            return False
            
        return True
        
    def check_localization_health(self):
        """Check localization system health"""
        current_time = time.time()
        
        # Check pose timeout
        if self.last_pose_time is None:
            return False
        elif current_time - self.last_pose_time > self.localization_timeout:
            return False
            
        return self.system_health['localization']
        
    def check_communication_health(self):
        """Check communication system health"""
        current_time = time.time()
        
        # Check heartbeat timeout
        if current_time - self.last_heartbeat_time > self.heartbeat_timeout:
            return False
            
        return True
        
    def determine_safety_level(self):
        """Determine overall safety level"""
        # Check obstacle distances
        if self.min_obstacle_distance < self.emergency_distance:
            return 'EMERGENCY'
        elif self.min_obstacle_distance < self.warning_distance:
            safety_level = 'WARNING'
        else:
            safety_level = 'SAFE'
            
        # Check system health
        if not self.check_sensor_health():
            safety_level = 'CRITICAL'
        elif not self.check_localization_health():
            safety_level = 'WARNING'
        elif not self.check_communication_health():
            safety_level = 'WARNING'
            
        return safety_level
        
    def monitor_safety(self):
        """Main safety monitoring loop"""
        # Update system health
        self.system_health['sensors'] = self.check_sensor_health()
        self.system_health['localization'] = self.check_localization_health()
        self.system_health['communication'] = self.check_communication_health()
        
        # Determine safety level
        new_safety_level = self.determine_safety_level()
        
        # Handle safety level changes
        if new_safety_level != self.safety_level:
            self.handle_safety_level_change(self.safety_level, new_safety_level)
            self.safety_level = new_safety_level
            
        # Handle emergency stop
        if self.safety_level == 'EMERGENCY' and not self.emergency_stop_active:
            self.trigger_emergency_stop()
        elif self.safety_level != 'EMERGENCY' and self.emergency_stop_active:
            self.clear_emergency_stop()
            
        # Publish safety status
        self.publish_safety_status()
        
    def handle_safety_level_change(self, old_level, new_level):
        """Handle safety level changes"""
        self.get_logger().info(f'Safety level changed: {old_level} -> {new_level}')
        
        if new_level == 'EMERGENCY':
            self.get_logger().error('EMERGENCY: Immediate stop required!')
        elif new_level == 'CRITICAL':
            self.get_logger().error('CRITICAL: System malfunction detected!')
        elif new_level == 'WARNING':
            self.get_logger().warn('WARNING: Reduced safety margin')
        elif new_level == 'SAFE':
            self.get_logger().info('SAFE: Normal operation')
            
    def trigger_emergency_stop(self):
        """Trigger emergency stop"""
        self.emergency_stop_active = True
        
        # Publish emergency stop
        emergency_msg = Bool()
        emergency_msg.data = True
        self.emergency_stop_pub.publish(emergency_msg)
        
        self.get_logger().error('EMERGENCY STOP ACTIVATED')
        
    def clear_emergency_stop(self):
        """Clear emergency stop"""
        self.emergency_stop_active = False
        
        # Publish emergency stop clear
        emergency_msg = Bool()
        emergency_msg.data = False
        self.emergency_stop_pub.publish(emergency_msg)
        
        self.get_logger().info('Emergency stop cleared')
        
    def publish_safety_status(self):
        """Publish safety status"""
        status_data = {
            'safety_level': self.safety_level,
            'emergency_stop': self.emergency_stop_active,
            'min_obstacle_distance': self.min_obstacle_distance if self.min_obstacle_distance != float('inf') else -1,
            'system_health': self.system_health,
            'warnings': []
        }
        
        # Add specific warnings
        if self.min_obstacle_distance < self.warning_distance:
            status_data['warnings'].append(f'Obstacle at {self.min_obstacle_distance:.2f}m')
            
        if not self.system_health['sensors']:
            status_data['warnings'].append('Sensor timeout')
            
        if not self.system_health['localization']:
            status_data['warnings'].append('Localization degraded')
            
        if not self.system_health['communication']:
            status_data['warnings'].append('Communication timeout')
            
        # Publish detailed status
        import json
        status_msg = String()
        status_msg.data = json.dumps(status_data)
        self.safety_status_pub.publish(status_msg)
        
        # Publish simple status for behavior coordinator
        simple_status = f'{self.safety_level}'
        if status_data['warnings']:
            simple_status += f':WARNINGS:{len(status_data["warnings"])}'
            
        simple_status_msg = String()
        simple_status_msg.data = simple_status
        self.safety_status_pub.publish(simple_status_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = SafetyMonitor()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
