#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
from geometry_msgs.msg import PoseStamped
from std_srvs.srv import Empty
import json
import time

class MissionPlanner(Node):
    def __init__(self):
        super().__init__('mission_planner')
        
        # Parameters
        self.declare_parameter('mission_file', 'missions.json')
        self.declare_parameter('auto_start_missions', False)
        self.declare_parameter('mission_timeout', 300.0)  # 5 minutes
        
        self.mission_file = self.get_parameter('mission_file').value
        self.auto_start = self.get_parameter('auto_start_missions').value
        self.mission_timeout = self.get_parameter('mission_timeout').value
        
        # Mission state
        self.missions = []
        self.current_mission = None
        self.current_waypoint_index = 0
        self.mission_state = 'IDLE'  # IDLE, PLANNING, EXECUTING, COMPLETED, FAILED
        self.mission_start_time = None
        
        # Load missions
        self.load_missions()
        
        # Subscribers
        self.path_executor_status_sub = self.create_subscription(
            String, '/path_executor/status', self.path_executor_status_callback, 10)
        self.system_command_sub = self.create_subscription(
            String, '/system_command', self.system_command_callback, 10)
        
        # Publishers
        self.status_pub = self.create_publisher(String, '/mission_planner/status', 10)
        self.goal_pub = self.create_publisher(PoseStamped, '/move_base_simple/goal', 10)
        
        # Services
        self.start_mission_srv = self.create_service(Empty, 'start_mission', self.start_mission_callback)
        self.stop_mission_srv = self.create_service(Empty, 'stop_mission', self.stop_mission_callback)
        self.next_mission_srv = self.create_service(Empty, 'next_mission', self.next_mission_callback)
        
        # Timer for mission monitoring
        self.mission_timer = self.create_timer(1.0, self.monitor_mission)
        
        self.get_logger().info(f'Mission Planner initialized with {len(self.missions)} missions')
        
    def load_missions(self):
        """Load missions from file"""
        try:
            with open(self.mission_file, 'r') as f:
                mission_data = json.load(f)
                
            self.missions = mission_data.get('missions', [])
            self.get_logger().info(f'Loaded {len(self.missions)} missions')
            
            # Log mission names
            for i, mission in enumerate(self.missions):
                self.get_logger().info(f'  Mission {i}: {mission.get("name", "Unnamed")}')
                
        except FileNotFoundError:
            self.get_logger().warn(f'Mission file {self.mission_file} not found, creating default missions')
            self.create_default_missions()
        except Exception as e:
            self.get_logger().error(f'Error loading missions: {e}')
            self.create_default_missions()
            
    def create_default_missions(self):
        """Create default missions"""
        self.missions = [
            {
                'name': 'Room Patrol',
                'description': 'Patrol around the room',
                'waypoints': [
                    {'x': 2.0, 'y': 2.0, 'yaw': 0.0, 'wait_time': 5.0},
                    {'x': -2.0, 'y': 2.0, 'yaw': 1.57, 'wait_time': 5.0},
                    {'x': -2.0, 'y': -2.0, 'yaw': 3.14, 'wait_time': 5.0},
                    {'x': 2.0, 'y': -2.0, 'yaw': -1.57, 'wait_time': 5.0},
                    {'x': 0.0, 'y': 0.0, 'yaw': 0.0, 'wait_time': 10.0}
                ],
                'repeat': False
            },
            {
                'name': 'Inspection Route',
                'description': 'Inspect specific points',
                'waypoints': [
                    {'x': 1.0, 'y': 0.0, 'yaw': 0.0, 'wait_time': 10.0},
                    {'x': 0.0, 'y': 1.0, 'yaw': 1.57, 'wait_time': 10.0},
                    {'x': -1.0, 'y': 0.0, 'yaw': 3.14, 'wait_time': 10.0},
                    {'x': 0.0, 'y': -1.0, 'yaw': -1.57, 'wait_time': 10.0},
                    {'x': 0.0, 'y': 0.0, 'yaw': 0.0, 'wait_time': 5.0}
                ],
                'repeat': True
            }
        ]
        
        # Save default missions
        self.save_missions()
        
    def save_missions(self):
        """Save missions to file"""
        try:
            mission_data = {'missions': self.missions}
            with open(self.mission_file, 'w') as f:
                json.dump(mission_data, f, indent=2)
            self.get_logger().info(f'Missions saved to {self.mission_file}')
        except Exception as e:
            self.get_logger().error(f'Error saving missions: {e}')
            
    def path_executor_status_callback(self, msg):
        """Handle path executor status updates"""
        status = msg.data
        
        if self.mission_state == 'EXECUTING':
            if status.startswith('COMPLETED'):
                self.handle_waypoint_completed()
            elif status.startswith('FAILED'):
                self.handle_waypoint_failed()
                
    def system_command_callback(self, msg):
        """Handle system commands"""
        command = msg.data
        
        if command == 'START_MISSION' and self.mission_state == 'IDLE':
            self.start_current_mission()
        elif command == 'STOP_MISSION':
            self.stop_current_mission()
        elif command.startswith('START_MISSION:'):
            mission_index = int(command.split(':')[1])
            self.start_mission_by_index(mission_index)
            
    def start_mission_callback(self, request, response):
        """Service callback to start mission"""
        self.start_current_mission()
        return response
        
    def stop_mission_callback(self, request, response):
        """Service callback to stop mission"""
        self.stop_current_mission()
        return response
        
    def next_mission_callback(self, request, response):
        """Service callback to go to next mission"""
        if len(self.missions) > 0:
            current_index = self.missions.index(self.current_mission) if self.current_mission else -1
            next_index = (current_index + 1) % len(self.missions)
            self.start_mission_by_index(next_index)
        return response
        
    def start_current_mission(self):
        """Start the current mission"""
        if len(self.missions) == 0:
            self.get_logger().warn('No missions available')
            return
            
        if self.current_mission is None:
            self.current_mission = self.missions[0]
            
        self.start_mission(self.current_mission)
        
    def start_mission_by_index(self, index):
        """Start mission by index"""
        if 0 <= index < len(self.missions):
            self.current_mission = self.missions[index]
            self.start_mission(self.current_mission)
        else:
            self.get_logger().error(f'Invalid mission index: {index}')
            
    def start_mission(self, mission):
        """Start a specific mission"""
        self.current_mission = mission
        self.current_waypoint_index = 0
        self.mission_state = 'EXECUTING'
        self.mission_start_time = time.time()
        
        self.get_logger().info(f'Starting mission: {mission["name"]}')
        
        # Send first waypoint
        self.send_next_waypoint()
        
    def stop_current_mission(self):
        """Stop current mission"""
        if self.mission_state != 'IDLE':
            self.mission_state = 'IDLE'
            self.current_mission = None
            self.current_waypoint_index = 0
            self.get_logger().info('Mission stopped')
            
    def send_next_waypoint(self):
        """Send next waypoint to path executor"""
        if self.current_mission is None or self.current_waypoint_index >= len(self.current_mission['waypoints']):
            self.handle_mission_completed()
            return
            
        waypoint = self.current_mission['waypoints'][self.current_waypoint_index]
        
        # Create goal message
        goal_msg = PoseStamped()
        goal_msg.header.stamp = self.get_clock().now().to_msg()
        goal_msg.header.frame_id = 'map'
        goal_msg.pose.position.x = waypoint['x']
        goal_msg.pose.position.y = waypoint['y']
        goal_msg.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        import math
        yaw = waypoint['yaw']
        goal_msg.pose.orientation.x = 0.0
        goal_msg.pose.orientation.y = 0.0
        goal_msg.pose.orientation.z = math.sin(yaw / 2.0)
        goal_msg.pose.orientation.w = math.cos(yaw / 2.0)
        
        # Publish goal
        self.goal_pub.publish(goal_msg)
        
        self.get_logger().info(f'Sent waypoint {self.current_waypoint_index + 1}/{len(self.current_mission["waypoints"])}: '
                              f'({waypoint["x"]:.2f}, {waypoint["y"]:.2f})')
        
    def handle_waypoint_completed(self):
        """Handle waypoint completion"""
        if self.current_mission is None:
            return
            
        waypoint = self.current_mission['waypoints'][self.current_waypoint_index]
        wait_time = waypoint.get('wait_time', 0.0)
        
        self.get_logger().info(f'Waypoint {self.current_waypoint_index + 1} completed, waiting {wait_time}s')
        
        # Wait at waypoint if specified
        if wait_time > 0:
            time.sleep(wait_time)
            
        # Move to next waypoint
        self.current_waypoint_index += 1
        
        if self.current_waypoint_index >= len(self.current_mission['waypoints']):
            self.handle_mission_completed()
        else:
            self.send_next_waypoint()
            
    def handle_waypoint_failed(self):
        """Handle waypoint failure"""
        self.get_logger().warn(f'Waypoint {self.current_waypoint_index + 1} failed')
        
        # For now, just try the next waypoint
        # Could implement retry logic here
        self.current_waypoint_index += 1
        
        if self.current_waypoint_index >= len(self.current_mission['waypoints']):
            self.handle_mission_completed()
        else:
            self.get_logger().info('Attempting next waypoint')
            self.send_next_waypoint()
            
    def handle_mission_completed(self):
        """Handle mission completion"""
        if self.current_mission is None:
            return
            
        mission_time = time.time() - self.mission_start_time if self.mission_start_time else 0
        self.get_logger().info(f'Mission "{self.current_mission["name"]}" completed in {mission_time:.1f}s')
        
        # Check if mission should repeat
        if self.current_mission.get('repeat', False):
            self.get_logger().info('Repeating mission')
            self.current_waypoint_index = 0
            self.send_next_waypoint()
        else:
            self.mission_state = 'COMPLETED'
            
            # Auto-start next mission if enabled
            if self.auto_start and len(self.missions) > 1:
                current_index = self.missions.index(self.current_mission)
                next_index = (current_index + 1) % len(self.missions)
                self.get_logger().info(f'Auto-starting next mission: {self.missions[next_index]["name"]}')
                self.start_mission_by_index(next_index)
            else:
                self.mission_state = 'IDLE'
                self.current_mission = None
                
    def monitor_mission(self):
        """Monitor mission execution"""
        # Check mission timeout
        if (self.mission_state == 'EXECUTING' and 
            self.mission_start_time is not None and
            time.time() - self.mission_start_time > self.mission_timeout):
            
            self.get_logger().error('Mission timeout reached')
            self.mission_state = 'FAILED'
            self.current_mission = None
            
        # Publish status
        self.publish_status()
        
    def publish_status(self):
        """Publish mission status"""
        status_data = {
            'state': self.mission_state,
            'current_mission': self.current_mission['name'] if self.current_mission else None,
            'waypoint_index': self.current_waypoint_index,
            'total_waypoints': len(self.current_mission['waypoints']) if self.current_mission else 0,
            'mission_time': time.time() - self.mission_start_time if self.mission_start_time else 0,
            'total_missions': len(self.missions)
        }
        
        status_msg = String()
        status_msg.data = json.dumps(status_data)
        self.status_pub.publish(status_msg)
        
        # Also publish simple status for behavior coordinator
        simple_status = f'{self.mission_state}'
        if self.current_mission:
            simple_status += f':{self.current_mission["name"]}'
        if self.mission_state == 'EXECUTING':
            simple_status += f':{self.current_waypoint_index + 1}/{len(self.current_mission["waypoints"])}'
            
        simple_status_msg = String()
        simple_status_msg.data = simple_status
        self.status_pub.publish(simple_status_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = MissionPlanner()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
