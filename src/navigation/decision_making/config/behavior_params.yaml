behavior_coordinator:
  ros__parameters:
    # Default behavior on startup
    default_behavior: 'idle'
    
    # Emergency stop configuration
    emergency_stop_enabled: true
    
    # Behavior timeout (seconds)
    behavior_timeout: 60.0

mission_planner:
  ros__parameters:
    # Mission configuration
    mission_file: 'missions.json'
    auto_start_missions: false
    mission_timeout: 300.0  # 5 minutes

safety_monitor:
  ros__parameters:
    # Safety distances (meters) - Made less sensitive
    emergency_stop_distance: 0.15
    warning_distance: 0.4
    
    # Velocity limits
    max_linear_velocity: 0.5   # m/s
    max_angular_velocity: 1.0  # rad/s
    
    # Timeout settings (seconds)
    localization_timeout: 5.0
    heartbeat_timeout: 2.0
