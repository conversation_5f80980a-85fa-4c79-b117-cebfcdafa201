from setuptools import setup

package_name = 'decision_making'

setup(
    name=package_name,
    version='1.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/decision_making.launch.py']),
        ('share/' + package_name + '/config', ['config/behavior_params.yaml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='Decision making and behavior coordination for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'behavior_coordinator = decision_making.behavior_coordinator:main',
            'mission_planner = decision_making.mission_planner:main',
            'safety_monitor = decision_making.safety_monitor:main',
        ],
    },
)
