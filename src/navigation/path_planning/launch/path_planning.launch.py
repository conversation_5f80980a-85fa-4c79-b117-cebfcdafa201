#!/usr/bin/env python3

import os
from launch import LaunchDes<PERSON>
from launch.actions import DeclareLaunchArgument
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_path_planning = get_package_share_directory('path_planning')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(pkg_path_planning, 'config', 'planning_params.yaml'),
        description='Full path to planning parameters file'
    )
    
    use_global_planner_arg = DeclareLaunchArgument(
        'use_global_planner',
        default_value='true',
        description='Enable global planner'
    )
    
    use_local_planner_arg = DeclareLaunchArgument(
        'use_local_planner',
        default_value='true',
        description='Enable local planner'
    )
    
    use_path_executor_arg = DeclareLaunchArgument(
        'use_path_executor',
        default_value='true',
        description='Enable path executor'
    )
    
    use_goal_manager_arg = DeclareLaunchArgument(
        'use_goal_manager',
        default_value='true',
        description='Enable goal manager'
    )
    
    # Global Planner Node
    global_planner_node = Node(
        package='path_planning',
        executable='global_planner',
        name='global_planner',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_global_planner'))
    )
    
    # Local Planner Node
    local_planner_node = Node(
        package='path_planning',
        executable='local_planner',
        name='local_planner',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_local_planner'))
    )
    
    # Path Executor Node
    path_executor_node = Node(
        package='path_planning',
        executable='path_executor',
        name='path_executor',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_path_executor'))
    )
    
    # Goal Manager Node
    goal_manager_node = Node(
        package='path_planning',
        executable='goal_manager',
        name='goal_manager',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_goal_manager'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        params_file_arg,
        use_global_planner_arg,
        use_local_planner_arg,
        use_path_executor_arg,
        use_goal_manager_arg,
        
        # Nodes
        global_planner_node,
        local_planner_node,
        path_executor_node,
        goal_manager_node,
    ])
