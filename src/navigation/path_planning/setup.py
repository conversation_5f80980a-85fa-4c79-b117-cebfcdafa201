from setuptools import setup

package_name = 'path_planning'

setup(
    name=package_name,
    version='1.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/path_planning.launch.py']),
        ('share/' + package_name + '/config', ['config/planning_params.yaml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='Path planning and navigation for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'global_planner = path_planning.global_planner:main',
            'local_planner = path_planning.local_planner:main',
            'path_executor = path_planning.path_executor:main',
            'goal_manager = path_planning.goal_manager:main',
            'advanced_global_planner = path_planning.advanced_global_planner:main',
        ],
    },
)
