#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import OccupancyGrid, Path
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from std_msgs.msg import String
import numpy as np
import heapq
import math
import time

class GlobalPlanner(Node):
    def __init__(self):
        super().__init__('global_planner')
        
        # Parameters
        self.declare_parameter('planning_algorithm', 'astar')  # 'astar', 'dijkstra'
        self.declare_parameter('heuristic_weight', 1.0)
        self.declare_parameter('diagonal_movement', True)
        self.declare_parameter('smoothing_enabled', True)
        self.declare_parameter('path_resolution', 0.1)  # meters
        self.declare_parameter('obstacle_inflation', 0.2)  # meters
        
        self.planning_algorithm = self.get_parameter('planning_algorithm').value
        self.heuristic_weight = self.get_parameter('heuristic_weight').value
        self.diagonal_movement = self.get_parameter('diagonal_movement').value
        self.smoothing_enabled = self.get_parameter('smoothing_enabled').value
        self.path_resolution = self.get_parameter('path_resolution').value
        self.obstacle_inflation = self.get_parameter('obstacle_inflation').value
        
        # State variables
        self.current_map = None
        self.current_pose = None
        self.current_goal = None
        self.last_path = None
        
        # Subscribers
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.map_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.goal_sub = self.create_subscription(
            PoseStamped, '/move_base_simple/goal', self.goal_callback, 10)
        
        # Publishers
        self.path_pub = self.create_publisher(Path, '/global_path', 10)
        self.status_pub = self.create_publisher(String, '/global_planner/status', 10)
        
        # Timer for replanning
        self.replan_timer = self.create_timer(2.0, self.replan_if_needed)
        
        self.get_logger().info(f'Global Planner initialized with {self.planning_algorithm} algorithm')
        
    def map_callback(self, msg):
        """Store current map"""
        self.current_map = msg
        self.get_logger().debug('Map updated')
        
    def pose_callback(self, msg):
        """Store current robot pose"""
        self.current_pose = msg
        
    def goal_callback(self, msg):
        """Handle new goal and plan path"""
        self.current_goal = msg
        self.get_logger().info(f'New goal received: ({msg.pose.position.x:.2f}, {msg.pose.position.y:.2f})')
        
        # Plan path immediately
        self.plan_path()
        
    def world_to_map(self, x, y):
        """Convert world coordinates to map coordinates"""
        if self.current_map is None:
            return None, None
            
        map_x = int((x - self.current_map.info.origin.position.x) / self.current_map.info.resolution)
        map_y = int((y - self.current_map.info.origin.position.y) / self.current_map.info.resolution)
        
        return map_x, map_y
        
    def map_to_world(self, map_x, map_y):
        """Convert map coordinates to world coordinates"""
        if self.current_map is None:
            return None, None
            
        x = map_x * self.current_map.info.resolution + self.current_map.info.origin.position.x
        y = map_y * self.current_map.info.resolution + self.current_map.info.origin.position.y
        
        return x, y
        
    def is_valid_cell(self, map_x, map_y):
        """Check if map cell is valid and free"""
        if self.current_map is None:
            return False
            
        width = self.current_map.info.width
        height = self.current_map.info.height
        
        if map_x < 0 or map_x >= width or map_y < 0 or map_y >= height:
            return False
            
        index = map_y * width + map_x
        if index >= len(self.current_map.data):
            return False
            
        # Check if cell is free (0) or unknown (-1), avoid occupied (100)
        cell_value = self.current_map.data[index]
        return cell_value >= 0 and cell_value < 50
        
    def inflate_obstacles(self, grid):
        """Inflate obstacles for robot safety"""
        if self.obstacle_inflation <= 0:
            return grid
            
        height, width = grid.shape
        inflation_cells = int(self.obstacle_inflation / self.current_map.info.resolution)
        
        inflated_grid = grid.copy()
        
        # Find all obstacle cells
        obstacle_cells = np.where(grid >= 50)
        
        # Inflate around each obstacle
        for y, x in zip(obstacle_cells[0], obstacle_cells[1]):
            for dy in range(-inflation_cells, inflation_cells + 1):
                for dx in range(-inflation_cells, inflation_cells + 1):
                    new_y, new_x = y + dy, x + dx
                    if 0 <= new_y < height and 0 <= new_x < width:
                        if math.sqrt(dx*dx + dy*dy) <= inflation_cells:
                            inflated_grid[new_y, new_x] = max(inflated_grid[new_y, new_x], 50)
                            
        return inflated_grid
        
    def heuristic(self, a, b):
        """Calculate heuristic distance between two points"""
        if self.diagonal_movement:
            # Diagonal distance (Chebyshev distance)
            dx = abs(a[0] - b[0])
            dy = abs(a[1] - b[1])
            return max(dx, dy) + (math.sqrt(2) - 1) * min(dx, dy)
        else:
            # Manhattan distance
            return abs(a[0] - b[0]) + abs(a[1] - b[1])
            
    def get_neighbors(self, node):
        """Get valid neighbors of a node"""
        x, y = node
        neighbors = []
        
        # 8-connected or 4-connected movement
        if self.diagonal_movement:
            directions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        else:
            directions = [(-1,0), (1,0), (0,-1), (0,1)]
            
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self.is_valid_cell(new_x, new_y):
                neighbors.append((new_x, new_y))
                
        return neighbors
        
    def astar_search(self, start, goal):
        """A* pathfinding algorithm"""
        open_set = []
        heapq.heappush(open_set, (0, start))
        
        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.heuristic(start, goal)}
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # Reconstruct path
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]
                
            for neighbor in self.get_neighbors(current):
                # Calculate movement cost
                if self.diagonal_movement:
                    dx = abs(neighbor[0] - current[0])
                    dy = abs(neighbor[1] - current[1])
                    move_cost = math.sqrt(dx*dx + dy*dy)
                else:
                    move_cost = 1.0
                    
                tentative_g_score = g_score[current] + move_cost
                
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + self.heuristic_weight * self.heuristic(neighbor, goal)
                    
                    if neighbor not in [item[1] for item in open_set]:
                        heapq.heappush(open_set, (f_score[neighbor], neighbor))
                        
        return None  # No path found
        
    def dijkstra_search(self, start, goal):
        """Dijkstra pathfinding algorithm"""
        open_set = []
        heapq.heappush(open_set, (0, start))
        
        came_from = {}
        distance = {start: 0}
        
        while open_set:
            current_dist, current = heapq.heappop(open_set)
            
            if current == goal:
                # Reconstruct path
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]
                
            if current_dist > distance.get(current, float('inf')):
                continue
                
            for neighbor in self.get_neighbors(current):
                # Calculate movement cost
                if self.diagonal_movement:
                    dx = abs(neighbor[0] - current[0])
                    dy = abs(neighbor[1] - current[1])
                    move_cost = math.sqrt(dx*dx + dy*dy)
                else:
                    move_cost = 1.0
                    
                new_distance = distance[current] + move_cost
                
                if new_distance < distance.get(neighbor, float('inf')):
                    distance[neighbor] = new_distance
                    came_from[neighbor] = current
                    heapq.heappush(open_set, (new_distance, neighbor))
                    
        return None  # No path found
        
    def smooth_path(self, path):
        """Smooth the path using line-of-sight optimization"""
        if not self.smoothing_enabled or len(path) < 3:
            return path
            
        smoothed_path = [path[0]]
        current_index = 0
        
        while current_index < len(path) - 1:
            # Find the farthest point we can reach directly
            farthest_index = current_index + 1
            
            for i in range(current_index + 2, len(path)):
                if self.has_line_of_sight(path[current_index], path[i]):
                    farthest_index = i
                else:
                    break
                    
            smoothed_path.append(path[farthest_index])
            current_index = farthest_index
            
        return smoothed_path
        
    def has_line_of_sight(self, start, end):
        """Check if there's a clear line of sight between two points"""
        x0, y0 = start
        x1, y1 = end
        
        # Bresenham's line algorithm
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        
        x_step = 1 if x0 < x1 else -1
        y_step = 1 if y0 < y1 else -1
        
        error = dx - dy
        x, y = x0, y0
        
        while True:
            if not self.is_valid_cell(x, y):
                return False
                
            if x == x1 and y == y1:
                break
                
            error2 = 2 * error
            if error2 > -dy:
                error -= dy
                x += x_step
            if error2 < dx:
                error += dx
                y += y_step
                
        return True
        
    def plan_path(self):
        """Plan path from current pose to goal"""
        if self.current_map is None or self.current_pose is None or self.current_goal is None:
            self.get_logger().warn('Missing required data for path planning')
            return
            
        start_time = time.time()
        
        # Convert poses to map coordinates
        start_x = self.current_pose.pose.pose.position.x
        start_y = self.current_pose.pose.pose.position.y
        goal_x = self.current_goal.pose.position.x
        goal_y = self.current_goal.pose.position.y
        
        start_map = self.world_to_map(start_x, start_y)
        goal_map = self.world_to_map(goal_x, goal_y)
        
        if start_map[0] is None or goal_map[0] is None:
            self.get_logger().error('Invalid start or goal coordinates')
            return
            
        # Check if start and goal are valid
        if not self.is_valid_cell(start_map[0], start_map[1]):
            self.get_logger().error('Start position is in obstacle')
            return
            
        if not self.is_valid_cell(goal_map[0], goal_map[1]):
            self.get_logger().error('Goal position is in obstacle')
            return
            
        # Plan path
        if self.planning_algorithm == 'astar':
            path = self.astar_search(start_map, goal_map)
        else:
            path = self.dijkstra_search(start_map, goal_map)
            
        if path is None:
            self.get_logger().error('No path found to goal')
            status_msg = String()
            status_msg.data = 'NO_PATH_FOUND'
            self.status_pub.publish(status_msg)
            return
            
        # Smooth path
        smoothed_path = self.smooth_path(path)
        
        # Convert back to world coordinates and publish
        self.publish_path(smoothed_path)
        
        planning_time = time.time() - start_time
        self.get_logger().info(f'Path planned in {planning_time:.3f}s: {len(path)} -> {len(smoothed_path)} points')
        
        status_msg = String()
        status_msg.data = f'PATH_FOUND:{len(smoothed_path)}:{planning_time:.3f}'
        self.status_pub.publish(status_msg)
        
    def publish_path(self, path):
        """Publish the planned path"""
        path_msg = Path()
        path_msg.header.stamp = self.get_clock().now().to_msg()
        path_msg.header.frame_id = 'map'
        
        for map_x, map_y in path:
            world_x, world_y = self.map_to_world(map_x, map_y)
            
            pose = PoseStamped()
            pose.header = path_msg.header
            pose.pose.position.x = world_x
            pose.pose.position.y = world_y
            pose.pose.position.z = 0.0
            pose.pose.orientation.w = 1.0
            
            path_msg.poses.append(pose)
            
        self.path_pub.publish(path_msg)
        self.last_path = path_msg
        
    def replan_if_needed(self):
        """Replan if conditions have changed"""
        if self.current_goal is not None and self.current_pose is not None:
            # Check if we're close to goal
            goal_x = self.current_goal.pose.position.x
            goal_y = self.current_goal.pose.position.y
            current_x = self.current_pose.pose.pose.position.x
            current_y = self.current_pose.pose.pose.position.y
            
            distance_to_goal = math.sqrt((goal_x - current_x)**2 + (goal_y - current_y)**2)
            
            if distance_to_goal < 0.5:  # Within 50cm of goal
                self.get_logger().info('Goal reached!')
                self.current_goal = None
                
                status_msg = String()
                status_msg.data = 'GOAL_REACHED'
                self.status_pub.publish(status_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = GlobalPlanner()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
