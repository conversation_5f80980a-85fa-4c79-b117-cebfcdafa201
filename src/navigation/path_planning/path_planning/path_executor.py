#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped, Twist
from std_msgs.msg import String, Bool
from rclpy.action import ActionServer
from nav2_msgs.action import NavigateToPose
import math
import time

class PathExecutor(Node):
    def __init__(self):
        super().__init__('path_executor')
        
        # Parameters
        self.declare_parameter('goal_tolerance', 0.3)
        self.declare_parameter('path_timeout', 30.0)
        self.declare_parameter('replan_threshold', 2.0)
        self.declare_parameter('max_retries', 3)
        
        self.goal_tolerance = self.get_parameter('goal_tolerance').value
        self.path_timeout = self.get_parameter('path_timeout').value
        self.replan_threshold = self.get_parameter('replan_threshold').value
        self.max_retries = self.get_parameter('max_retries').value
        
        # State variables
        self.current_pose = None
        self.current_goal = None
        self.current_path = None
        self.execution_state = 'IDLE'  # IDLE, PLANNING, EXECUTING, COMPLETED, FAILED
        self.start_time = None
        self.retry_count = 0
        self.last_progress_time = None
        self.last_distance_to_goal = float('inf')
        
        # Subscribers
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.path_sub = self.create_subscription(
            Path, '/global_path', self.path_callback, 10)
        self.global_planner_status_sub = self.create_subscription(
            String, '/global_planner/status', self.global_planner_status_callback, 10)
        self.local_planner_status_sub = self.create_subscription(
            String, '/local_planner/status', self.local_planner_status_callback, 10)
        
        # Publishers
        self.goal_pub = self.create_publisher(PoseStamped, '/move_base_simple/goal', 10)
        self.status_pub = self.create_publisher(String, '/path_executor/status', 10)
        self.cancel_pub = self.create_publisher(Bool, '/path_executor/cancel', 10)
        
        # Action Server
        self.action_server = ActionServer(
            self, NavigateToPose, 'navigate_to_pose', self.navigate_callback)
        
        # Timer for monitoring execution
        self.monitor_timer = self.create_timer(1.0, self.monitor_execution)
        
        self.get_logger().info('Path Executor initialized')
        
    def pose_callback(self, msg):
        """Update current robot pose"""
        self.current_pose = msg
        
    def path_callback(self, msg):
        """Receive planned path"""
        self.current_path = msg
        if self.execution_state == 'PLANNING':
            self.execution_state = 'EXECUTING'
            self.start_time = time.time()
            self.last_progress_time = time.time()
            self.get_logger().info(f'Path received, starting execution with {len(msg.poses)} waypoints')
            
    def global_planner_status_callback(self, msg):
        """Handle global planner status updates"""
        status = msg.data
        
        if status == 'NO_PATH_FOUND':
            if self.execution_state == 'PLANNING':
                self.retry_count += 1
                if self.retry_count < self.max_retries:
                    self.get_logger().warn(f'No path found, retrying ({self.retry_count}/{self.max_retries})')
                    # Try replanning with slightly different goal
                    self.replan_with_offset()
                else:
                    self.execution_state = 'FAILED'
                    self.get_logger().error('Max retries reached, navigation failed')
                    
        elif status.startswith('PATH_FOUND'):
            self.retry_count = 0  # Reset retry count on successful planning
            
    def local_planner_status_callback(self, msg):
        """Handle local planner status updates"""
        status = msg.data
        
        if status == 'TURNING_AROUND':
            self.get_logger().warn('Robot is turning around due to obstacles')
            # Consider replanning if turning around too often
            
    def navigate_callback(self, goal_handle):
        """Handle navigation action requests"""
        self.get_logger().info('Navigation goal received')
        
        # Extract goal pose
        goal_pose = goal_handle.request.pose
        self.current_goal = goal_pose
        
        # Start navigation
        self.start_navigation(goal_pose)
        
        # Wait for completion
        result = NavigateToPose.Result()
        
        while self.execution_state in ['PLANNING', 'EXECUTING']:
            if goal_handle.is_cancel_requested:
                self.cancel_navigation()
                goal_handle.canceled()
                result.result = NavigateToPose.Result.RESULT_CANCELED
                return result
                
            # Publish feedback
            feedback = NavigateToPose.Feedback()
            if self.current_pose is not None and self.current_goal is not None:
                distance_to_goal = self.calculate_distance_to_goal()
                feedback.distance_remaining = distance_to_goal
                feedback.navigation_time = self.get_navigation_time()
                
            goal_handle.publish_feedback(feedback)
            time.sleep(0.1)
            
        # Set result based on final state
        if self.execution_state == 'COMPLETED':
            goal_handle.succeed()
            result.result = NavigateToPose.Result.RESULT_SUCCESS
        else:
            goal_handle.abort()
            result.result = NavigateToPose.Result.RESULT_FAILED
            
        return result
        
    def start_navigation(self, goal_pose):
        """Start navigation to goal"""
        self.current_goal = goal_pose
        self.execution_state = 'PLANNING'
        self.start_time = time.time()
        self.retry_count = 0
        self.last_progress_time = time.time()
        
        # Publish goal to global planner
        goal_msg = PoseStamped()
        goal_msg.header.stamp = self.get_clock().now().to_msg()
        goal_msg.header.frame_id = goal_pose.header.frame_id
        goal_msg.pose = goal_pose.pose
        
        self.goal_pub.publish(goal_msg)
        
        self.get_logger().info(f'Navigation started to ({goal_pose.pose.position.x:.2f}, {goal_pose.pose.position.y:.2f})')
        
    def cancel_navigation(self):
        """Cancel current navigation"""
        self.execution_state = 'IDLE'
        self.current_goal = None
        self.current_path = None
        
        # Publish stop command
        cancel_msg = Bool()
        cancel_msg.data = True
        self.cancel_pub.publish(cancel_msg)
        
        self.get_logger().info('Navigation canceled')
        
    def calculate_distance_to_goal(self):
        """Calculate distance to current goal"""
        if self.current_pose is None or self.current_goal is None:
            return float('inf')
            
        current_x = self.current_pose.pose.pose.position.x
        current_y = self.current_pose.pose.pose.position.y
        goal_x = self.current_goal.pose.position.x
        goal_y = self.current_goal.pose.position.y
        
        return math.sqrt((goal_x - current_x)**2 + (goal_y - current_y)**2)
        
    def get_navigation_time(self):
        """Get elapsed navigation time"""
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time
        
    def replan_with_offset(self):
        """Replan with slightly offset goal to avoid local minima"""
        if self.current_goal is None:
            return
            
        # Add small random offset to goal
        import random
        offset_x = random.uniform(-0.5, 0.5)
        offset_y = random.uniform(-0.5, 0.5)
        
        offset_goal = PoseStamped()
        offset_goal.header = self.current_goal.header
        offset_goal.pose.position.x = self.current_goal.pose.position.x + offset_x
        offset_goal.pose.position.y = self.current_goal.pose.position.y + offset_y
        offset_goal.pose.position.z = self.current_goal.pose.position.z
        offset_goal.pose.orientation = self.current_goal.pose.orientation
        
        self.goal_pub.publish(offset_goal)
        self.get_logger().info(f'Replanning with offset ({offset_x:.2f}, {offset_y:.2f})')
        
    def check_progress(self):
        """Check if robot is making progress towards goal"""
        if self.current_pose is None or self.current_goal is None:
            return True
            
        current_distance = self.calculate_distance_to_goal()
        
        # Check if we've made progress
        if current_distance < self.last_distance_to_goal - 0.1:  # 10cm progress
            self.last_progress_time = time.time()
            self.last_distance_to_goal = current_distance
            return True
            
        # Check if we're stuck
        time_since_progress = time.time() - self.last_progress_time
        if time_since_progress > 10.0:  # No progress for 10 seconds
            self.get_logger().warn('No progress detected, considering replanning')
            return False
            
        return True
        
    def monitor_execution(self):
        """Monitor navigation execution"""
        if self.execution_state == 'IDLE':
            return
            
        current_time = time.time()
        
        # Check timeout
        if self.start_time is not None:
            elapsed_time = current_time - self.start_time
            if elapsed_time > self.path_timeout:
                self.get_logger().error('Navigation timeout reached')
                self.execution_state = 'FAILED'
                return
                
        # Check if goal is reached
        if self.execution_state == 'EXECUTING' and self.current_pose is not None and self.current_goal is not None:
            distance_to_goal = self.calculate_distance_to_goal()
            
            if distance_to_goal < self.goal_tolerance:
                self.execution_state = 'COMPLETED'
                self.get_logger().info(f'Goal reached! Distance: {distance_to_goal:.3f}m')
                return
                
            # Check progress
            if not self.check_progress():
                if self.retry_count < self.max_retries:
                    self.get_logger().warn('Robot appears stuck, attempting replan')
                    self.execution_state = 'PLANNING'
                    self.retry_count += 1
                    self.replan_with_offset()
                else:
                    self.execution_state = 'FAILED'
                    self.get_logger().error('Robot stuck, max retries reached')
                    
        # Publish status
        self.publish_status()
        
    def publish_status(self):
        """Publish current execution status"""
        status_msg = String()
        
        if self.execution_state == 'IDLE':
            status_msg.data = 'IDLE'
        elif self.execution_state == 'PLANNING':
            status_msg.data = f'PLANNING:RETRY:{self.retry_count}'
        elif self.execution_state == 'EXECUTING':
            if self.current_goal is not None:
                distance = self.calculate_distance_to_goal()
                elapsed_time = self.get_navigation_time()
                status_msg.data = f'EXECUTING:DIST:{distance:.2f}:TIME:{elapsed_time:.1f}'
            else:
                status_msg.data = 'EXECUTING'
        elif self.execution_state == 'COMPLETED':
            elapsed_time = self.get_navigation_time()
            status_msg.data = f'COMPLETED:TIME:{elapsed_time:.1f}'
        elif self.execution_state == 'FAILED':
            status_msg.data = f'FAILED:RETRIES:{self.retry_count}'
            
        self.status_pub.publish(status_msg)
        
    def navigate_to_pose(self, x, y, yaw=0.0, frame_id='map'):
        """Convenience method to navigate to a pose"""
        goal = PoseStamped()
        goal.header.stamp = self.get_clock().now().to_msg()
        goal.header.frame_id = frame_id
        goal.pose.position.x = x
        goal.pose.position.y = y
        goal.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        goal.pose.orientation.x = 0.0
        goal.pose.orientation.y = 0.0
        goal.pose.orientation.z = math.sin(yaw / 2.0)
        goal.pose.orientation.w = math.cos(yaw / 2.0)
        
        self.start_navigation(goal)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = PathExecutor()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
