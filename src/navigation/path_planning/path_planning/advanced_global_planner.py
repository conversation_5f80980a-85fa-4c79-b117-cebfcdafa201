#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import OccupancyGrid, Path
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped, Twist, TransformStamped
from std_msgs.msg import String, Bool
from std_srvs.srv import Empty
from tf2_ros import TransformListener, Buffer
import tf2_geometry_msgs
import numpy as np
import heapq
import math
import time
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum

class PlanningAlgorithm(Enum):
    ASTAR = "astar"
    DIJKSTRA = "dijkstra"
    RRT = "rrt"
    RRT_STAR = "rrt_star"
    HYBRID_ASTAR = "hybrid_astar"

class AdvancedGlobalPlanner(Node):
    """
    Advanced Global Path Planner with multiple algorithms
    Phase 2: Advanced Features
    """
    
    def __init__(self):
        super().__init__('advanced_global_planner')
        
        # Parameters
        self.declare_parameter('planning_algorithm', 'astar')
        self.declare_parameter('heuristic_weight', 1.2)
        self.declare_parameter('diagonal_movement', True)
        self.declare_parameter('smoothing_enabled', True)
        self.declare_parameter('path_resolution', 0.1)
        self.declare_parameter('obstacle_inflation', 0.25)
        self.declare_parameter('max_planning_time', 5.0)  # seconds
        self.declare_parameter('replanning_threshold', 1.0)  # meters
        self.declare_parameter('dynamic_replanning', True)
        self.declare_parameter('path_optimization', True)
        
        # RRT specific parameters
        self.declare_parameter('rrt_max_iterations', 5000)
        self.declare_parameter('rrt_step_size', 0.5)
        self.declare_parameter('rrt_goal_bias', 0.1)
        self.declare_parameter('rrt_star_radius', 1.0)
        
        # Load parameters
        self.planning_algorithm = PlanningAlgorithm(
            self.get_parameter('planning_algorithm').value)
        self.heuristic_weight = self.get_parameter('heuristic_weight').value
        self.diagonal_movement = self.get_parameter('diagonal_movement').value
        self.smoothing_enabled = self.get_parameter('smoothing_enabled').value
        self.path_resolution = self.get_parameter('path_resolution').value
        self.obstacle_inflation = self.get_parameter('obstacle_inflation').value
        self.max_planning_time = self.get_parameter('max_planning_time').value
        self.replanning_threshold = self.get_parameter('replanning_threshold').value
        self.dynamic_replanning = self.get_parameter('dynamic_replanning').value
        self.path_optimization = self.get_parameter('path_optimization').value
        
        # RRT parameters
        self.rrt_max_iterations = self.get_parameter('rrt_max_iterations').value
        self.rrt_step_size = self.get_parameter('rrt_step_size').value
        self.rrt_goal_bias = self.get_parameter('rrt_goal_bias').value
        self.rrt_star_radius = self.get_parameter('rrt_star_radius').value
        
        # State variables
        self.current_map: Optional[OccupancyGrid] = None
        self.current_pose: Optional[PoseWithCovarianceStamped] = None
        self.current_goal: Optional[PoseStamped] = None
        self.last_path: Optional[Path] = None
        self.planning_active = False
        
        # Statistics
        self.planning_stats = {
            'total_plans': 0,
            'successful_plans': 0,
            'average_planning_time': 0.0,
            'average_path_length': 0.0,
            'replans': 0
        }

        # TF2 for getting robot pose from SLAM
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)
        
        # Subscribers
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.map_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.goal_sub = self.create_subscription(
            PoseStamped, '/move_base_simple/goal', self.goal_callback, 10)
        
        # Publishers
        self.path_pub = self.create_publisher(Path, '/global_path', 10)
        self.status_pub = self.create_publisher(String, '/planner/status', 10)
        self.stats_pub = self.create_publisher(String, '/planner/statistics', 10)
        self.visualization_pub = self.create_publisher(Path, '/planner/search_tree', 10)
        
        # Services
        self.replan_srv = self.create_service(
            Empty, '/planner/replan', self.replan_service_callback)
        self.change_algorithm_srv = self.create_service(
            Empty, '/planner/change_algorithm', self.change_algorithm_callback)
        
        # Timers
        self.status_timer = self.create_timer(1.0, self.publish_status)
        self.stats_timer = self.create_timer(5.0, self.publish_statistics)
        self.replan_timer = self.create_timer(2.0, self.check_replanning_needed)
        
        self.get_logger().info(f'🧠 Advanced Global Planner initialized')
        self.get_logger().info(f'🔧 Algorithm: {self.planning_algorithm.value}')
        self.get_logger().info(f'⚡ Dynamic replanning: {self.dynamic_replanning}')
        
    def map_callback(self, msg: OccupancyGrid):
        """Handle map updates"""
        self.current_map = msg
        
        # Trigger replanning if map changed significantly
        if self.dynamic_replanning and self.current_goal is not None:
            self.plan_path()
            
    def pose_callback(self, msg: PoseWithCovarianceStamped):
        """Handle pose updates"""
        self.current_pose = msg
        
    def goal_callback(self, msg: PoseStamped):
        """Handle new goal"""
        self.current_goal = msg
        self.get_logger().info(
            f'🎯 New goal: ({msg.pose.position.x:.2f}, {msg.pose.position.y:.2f})')
        
        # Plan path immediately
        self.plan_path()
        
    def plan_path(self):
        """Main path planning function"""
        if not self.validate_planning_conditions():
            return
            
        start_time = time.time()
        self.planning_active = True
        
        try:
            # Get robot pose (AMCL or SLAM)
            robot_pose = self.get_robot_pose()
            if robot_pose is None:
                self.get_logger().error('❌ Could not get robot pose for planning')
                return

            # Convert coordinates
            start_world = (
                robot_pose.pose.pose.position.x,
                robot_pose.pose.pose.position.y
            )
            goal_world = (
                self.current_goal.pose.position.x,
                self.current_goal.pose.position.y
            )
            
            start_map = self.world_to_map(*start_world)
            goal_map = self.world_to_map(*goal_world)

            # Debug coordinate conversion and map info
            self.get_logger().info(f'🗺️ Map info: size=({self.current_map.info.width}x{self.current_map.info.height}) resolution={self.current_map.info.resolution:.3f} origin=({self.current_map.info.origin.position.x:.2f}, {self.current_map.info.origin.position.y:.2f})')
            self.get_logger().info(f'🎯 Goal conversion: world=({goal_world[0]:.2f}, {goal_world[1]:.2f}) → map=({goal_map[0]}, {goal_map[1]})')
            self.get_logger().info(f'🤖 Start conversion: world=({start_world[0]:.2f}, {start_world[1]:.2f}) → map=({start_map[0]}, {start_map[1]})')

            if not self.validate_coordinates(start_map, goal_map):
                return
                
            # Plan path using selected algorithm
            path = self.execute_planning_algorithm(start_map, goal_map)
            
            if path is None:
                self.handle_planning_failure()
                return
                
            # Post-process path
            if self.path_optimization:
                path = self.optimize_path(path)
                
            if self.smoothing_enabled:
                path = self.smooth_path(path)
                
            # Publish results
            self.publish_path(path)
            
            # Update statistics
            planning_time = time.time() - start_time
            self.update_planning_stats(planning_time, path, True)
            
            self.get_logger().info(
                f'✅ Path planned in {planning_time:.3f}s: {len(path)} points')
                
        except Exception as e:
            self.get_logger().error(f'❌ Planning failed: {str(e)}')
            self.handle_planning_failure()
            
        finally:
            self.planning_active = False
            
    def execute_planning_algorithm(self, start: Tuple[int, int], 
                                 goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """Execute the selected planning algorithm"""
        
        if self.planning_algorithm == PlanningAlgorithm.ASTAR:
            return self.astar_search(start, goal)
        elif self.planning_algorithm == PlanningAlgorithm.DIJKSTRA:
            return self.dijkstra_search(start, goal)
        elif self.planning_algorithm == PlanningAlgorithm.RRT:
            return self.rrt_search(start, goal)
        elif self.planning_algorithm == PlanningAlgorithm.RRT_STAR:
            return self.rrt_star_search(start, goal)
        elif self.planning_algorithm == PlanningAlgorithm.HYBRID_ASTAR:
            return self.hybrid_astar_search(start, goal)
        else:
            self.get_logger().error(f'Unknown algorithm: {self.planning_algorithm}')
            return None
            
    def rrt_search(self, start: Tuple[int, int], 
                   goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """RRT (Rapidly-exploring Random Tree) algorithm"""
        
        class RRTNode:
            def __init__(self, x: int, y: int, parent=None):
                self.x = x
                self.y = y
                self.parent = parent
                
        # Initialize tree
        tree = [RRTNode(*start)]
        
        for iteration in range(self.rrt_max_iterations):
            # Check timeout
            if time.time() - self.planning_start_time > self.max_planning_time:
                break
                
            # Sample random point or goal (with bias)
            if np.random.random() < self.rrt_goal_bias:
                sample = goal
            else:
                sample = self.sample_random_point()
                
            # Find nearest node
            nearest_node = min(tree, key=lambda n: 
                             math.sqrt((n.x - sample[0])**2 + (n.y - sample[1])**2))
            
            # Extend towards sample
            new_point = self.extend_towards(
                (nearest_node.x, nearest_node.y), sample, self.rrt_step_size)
                
            if self.is_valid_cell(*new_point):
                new_node = RRTNode(*new_point, nearest_node)
                tree.append(new_node)
                
                # Check if goal reached
                if math.sqrt((new_point[0] - goal[0])**2 + 
                           (new_point[1] - goal[1])**2) < self.rrt_step_size:
                    # Reconstruct path
                    path = []
                    current = new_node
                    while current is not None:
                        path.append((current.x, current.y))
                        current = current.parent
                    return path[::-1]
                    
        return None  # No path found
        
    def sample_random_point(self) -> Tuple[int, int]:
        """Sample a random valid point in the map"""
        if self.current_map is None:
            return (0, 0)
            
        width = self.current_map.info.width
        height = self.current_map.info.height
        
        # Sample until we find a valid point
        for _ in range(100):  # Max attempts
            x = np.random.randint(0, width)
            y = np.random.randint(0, height)
            if self.is_valid_cell(x, y):
                return (x, y)
                
        return (0, 0)  # Fallback
        
    def extend_towards(self, from_point: Tuple[int, int], 
                      to_point: Tuple[int, int], step_size: float) -> Tuple[int, int]:
        """Extend from one point towards another by step_size"""
        dx = to_point[0] - from_point[0]
        dy = to_point[1] - from_point[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance <= step_size:
            return to_point
            
        # Normalize and scale
        scale = step_size / distance
        new_x = int(from_point[0] + dx * scale)
        new_y = int(from_point[1] + dy * scale)
        
        return (new_x, new_y)
        
    def optimize_path(self, path: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """Optimize path by removing unnecessary waypoints"""
        if len(path) < 3:
            return path
            
        optimized = [path[0]]
        i = 0
        
        while i < len(path) - 1:
            # Find farthest reachable point
            farthest = i + 1
            for j in range(i + 2, len(path)):
                if self.has_line_of_sight(path[i], path[j]):
                    farthest = j
                else:
                    break
                    
            optimized.append(path[farthest])
            i = farthest
            
        return optimized
        
    def world_to_map(self, x: float, y: float) -> Tuple[int, int]:
        """Convert world coordinates to map coordinates"""
        if self.current_map is None:
            return (0, 0)

        map_x = int((x - self.current_map.info.origin.position.x) /
                   self.current_map.info.resolution)
        map_y = int((y - self.current_map.info.origin.position.y) /
                   self.current_map.info.resolution)

        return (map_x, map_y)

    def map_to_world(self, map_x: int, map_y: int) -> Tuple[float, float]:
        """Convert map coordinates to world coordinates"""
        if self.current_map is None:
            return (0.0, 0.0)

        x = map_x * self.current_map.info.resolution + self.current_map.info.origin.position.x
        y = map_y * self.current_map.info.resolution + self.current_map.info.origin.position.y

        return (x, y)

    def is_valid_cell(self, map_x: int, map_y: int) -> bool:
        """Check if map cell is valid and free"""
        if self.current_map is None:
            return False

        width = self.current_map.info.width
        height = self.current_map.info.height

        if map_x < 0 or map_x >= width or map_y < 0 or map_y >= height:
            return False

        index = map_y * width + map_x
        if index >= len(self.current_map.data):
            return False

        cell_value = self.current_map.data[index]
        # More lenient obstacle detection for testing
        # -1: unknown, 0-49: free, 50-100: occupied
        # Allow unknown cells (-1) and free cells (0-49) for navigation
        return cell_value == -1 or (cell_value >= 0 and cell_value < 80)

    def get_cell_value(self, map_x: int, map_y: int) -> int:
        """Get the value of a map cell for debugging"""
        if self.current_map is None:
            return -999

        width = self.current_map.info.width
        height = self.current_map.info.height

        if map_x < 0 or map_x >= width or map_y < 0 or map_y >= height:
            return -998  # Out of bounds

        index = map_y * width + map_x
        if index >= len(self.current_map.data):
            return -997  # Index out of range

        return self.current_map.data[index]

    def has_line_of_sight(self, start: Tuple[int, int], end: Tuple[int, int]) -> bool:
        """Check if there's a clear line of sight between two points"""
        x0, y0 = start
        x1, y1 = end

        dx = abs(x1 - x0)
        dy = abs(y1 - y0)

        x_step = 1 if x0 < x1 else -1
        y_step = 1 if y0 < y1 else -1

        error = dx - dy
        x, y = x0, y0

        while True:
            if not self.is_valid_cell(x, y):
                return False

            if x == x1 and y == y1:
                break

            error2 = 2 * error
            if error2 > -dy:
                error -= dy
                x += x_step
            if error2 < dx:
                error += dx
                y += y_step

        return True

    def astar_search(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """A* pathfinding algorithm"""
        open_set = []
        heapq.heappush(open_set, (0, start))

        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.heuristic(start, goal)}

        while open_set:
            current = heapq.heappop(open_set)[1]

            if current == goal:
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]

            for neighbor in self.get_neighbors(current):
                move_cost = self.calculate_move_cost(current, neighbor)
                tentative_g_score = g_score[current] + move_cost

                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + self.heuristic_weight * self.heuristic(neighbor, goal)

                    if neighbor not in [item[1] for item in open_set]:
                        heapq.heappush(open_set, (f_score[neighbor], neighbor))

        return None

    def dijkstra_search(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """Dijkstra pathfinding algorithm"""
        open_set = []
        heapq.heappush(open_set, (0, start))

        came_from = {}
        distance = {start: 0}

        while open_set:
            current_dist, current = heapq.heappop(open_set)

            if current == goal:
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]

            if current_dist > distance.get(current, float('inf')):
                continue

            for neighbor in self.get_neighbors(current):
                move_cost = self.calculate_move_cost(current, neighbor)
                new_distance = distance[current] + move_cost

                if new_distance < distance.get(neighbor, float('inf')):
                    distance[neighbor] = new_distance
                    came_from[neighbor] = current
                    heapq.heappush(open_set, (new_distance, neighbor))

        return None

    def get_neighbors(self, node: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Get valid neighbors of a node"""
        x, y = node
        neighbors = []

        if self.diagonal_movement:
            directions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        else:
            directions = [(-1,0), (1,0), (0,-1), (0,1)]

        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self.is_valid_cell(new_x, new_y):
                neighbors.append((new_x, new_y))

        return neighbors

    def calculate_move_cost(self, from_node: Tuple[int, int], to_node: Tuple[int, int]) -> float:
        """Calculate movement cost between two nodes"""
        if self.diagonal_movement:
            dx = abs(to_node[0] - from_node[0])
            dy = abs(to_node[1] - from_node[1])
            return math.sqrt(dx*dx + dy*dy)
        else:
            return 1.0

    def heuristic(self, a: Tuple[int, int], b: Tuple[int, int]) -> float:
        """Calculate heuristic distance between two points"""
        if self.diagonal_movement:
            dx = abs(a[0] - b[0])
            dy = abs(a[1] - b[1])
            return max(dx, dy) + (math.sqrt(2) - 1) * min(dx, dy)
        else:
            return abs(a[0] - b[0]) + abs(a[1] - b[1])

    def smooth_path(self, path: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """Smooth the path using line-of-sight optimization"""
        if len(path) < 3:
            return path

        smoothed_path = [path[0]]
        current_index = 0

        while current_index < len(path) - 1:
            farthest_index = current_index + 1

            for i in range(current_index + 2, len(path)):
                if self.has_line_of_sight(path[current_index], path[i]):
                    farthest_index = i
                else:
                    break

            smoothed_path.append(path[farthest_index])
            current_index = farthest_index

        return smoothed_path
    
    def get_robot_pose(self) -> Optional[PoseWithCovarianceStamped]:
        """Get robot pose from AMCL or TF (SLAM)"""
        # First try AMCL pose
        if self.current_pose is not None:
            return self.current_pose

        # If no AMCL, try to get pose from TF (SLAM)
        try:
            transform = self.tf_buffer.lookup_transform(
                'map', 'base_footprint', rclpy.time.Time())

            # Convert transform to PoseWithCovarianceStamped
            pose_msg = PoseWithCovarianceStamped()
            pose_msg.header.stamp = transform.header.stamp
            pose_msg.header.frame_id = 'map'

            pose_msg.pose.pose.position.x = transform.transform.translation.x
            pose_msg.pose.pose.position.y = transform.transform.translation.y
            pose_msg.pose.pose.position.z = transform.transform.translation.z

            pose_msg.pose.pose.orientation = transform.transform.rotation

            # Set some default covariance
            pose_msg.pose.covariance = [0.1] * 36

            return pose_msg

        except Exception as e:
            self.get_logger().debug(f'Could not get pose from TF: {str(e)}')
            return None

    def validate_planning_conditions(self) -> bool:
        """Validate that all conditions for planning are met"""
        if self.current_map is None:
            self.get_logger().warn('⚠️ No map available - creating fallback map for testing')
            self.create_fallback_map()
            if self.current_map is None:
                return False

        robot_pose = self.get_robot_pose()
        if robot_pose is None:
            self.get_logger().warn('⚠️ No pose available (neither AMCL nor SLAM)')
            return False

        if self.current_goal is None:
            self.get_logger().warn('⚠️ No goal available')
            return False
        return True

    def validate_coordinates(self, start_map: Tuple[int, int], goal_map: Tuple[int, int]) -> bool:
        """Validate start and goal coordinates"""
        # Debug start position
        start_valid = self.is_valid_cell(start_map[0], start_map[1])
        if not start_valid:
            start_world = self.map_to_world(start_map[0], start_map[1])
            start_cell_value = self.get_cell_value(start_map[0], start_map[1])
            self.get_logger().error(f'❌ Start position is in obstacle: map=({start_map[0]}, {start_map[1]}) world=({start_world[0]:.2f}, {start_world[1]:.2f}) cell_value={start_cell_value}')
            return False

        # Debug goal position
        goal_valid = self.is_valid_cell(goal_map[0], goal_map[1])
        if not goal_valid:
            goal_world = self.map_to_world(goal_map[0], goal_map[1])
            goal_cell_value = self.get_cell_value(goal_map[0], goal_map[1])
            self.get_logger().error(f'❌ Goal position is in obstacle: map=({goal_map[0]}, {goal_map[1]}) world=({goal_world[0]:.2f}, {goal_world[1]:.2f}) cell_value={goal_cell_value}')

            # Check surrounding cells for debugging
            self.get_logger().info(f'🔍 Checking surrounding cells around goal:')
            for dy in range(-2, 3):
                for dx in range(-2, 3):
                    check_x, check_y = goal_map[0] + dx, goal_map[1] + dy
                    check_value = self.get_cell_value(check_x, check_y)
                    check_world = self.map_to_world(check_x, check_y)
                    self.get_logger().info(f'  Cell ({check_x}, {check_y}) = {check_value} (world: {check_world[0]:.2f}, {check_world[1]:.2f})')

            return False

        return True

    def create_fallback_map(self):
        """Create a simple fallback map for testing when no SLAM map is available"""
        try:
            from nav_msgs.msg import OccupancyGrid
            import numpy as np

            # Create a simple test map
            width = 100  # cells
            height = 100  # cells
            resolution = 0.1  # meters per cell

            # Create mostly free space map
            map_data = np.zeros((height, width), dtype=np.int8)

            # Add border walls
            map_data[0, :] = 100  # Top wall
            map_data[-1, :] = 100  # Bottom wall
            map_data[:, 0] = 100  # Left wall
            map_data[:, -1] = 100  # Right wall

            # Create OccupancyGrid message
            map_msg = OccupancyGrid()
            map_msg.header.frame_id = 'map'
            map_msg.header.stamp = self.get_clock().now().to_msg()
            map_msg.info.resolution = resolution
            map_msg.info.width = width
            map_msg.info.height = height

            # Set origin
            map_msg.info.origin.position.x = -5.0
            map_msg.info.origin.position.y = -5.0
            map_msg.info.origin.position.z = 0.0
            map_msg.info.origin.orientation.w = 1.0

            # Flatten the map data
            map_msg.data = map_data.flatten().tolist()

            # Set as current map
            self.current_map = map_msg

            self.get_logger().info(f'✅ Created fallback map: {width}x{height} cells, {resolution}m resolution')

        except Exception as e:
            self.get_logger().error(f'❌ Failed to create fallback map: {str(e)}')

    def handle_planning_failure(self):
        """Handle planning failure"""
        self.planning_stats['total_plans'] += 1

        status_msg = String()
        status_msg.data = 'status:FAILED'
        self.status_pub.publish(status_msg)

        self.get_logger().error('❌ Path planning failed')

    def update_planning_stats(self, planning_time: float, path: List[Tuple[int, int]], success: bool):
        """Update planning statistics"""
        self.planning_stats['total_plans'] += 1

        if success:
            self.planning_stats['successful_plans'] += 1

            # Update average planning time
            if self.planning_stats['successful_plans'] == 1:
                self.planning_stats['average_planning_time'] = planning_time
            else:
                alpha = 0.1
                self.planning_stats['average_planning_time'] = (
                    alpha * planning_time +
                    (1 - alpha) * self.planning_stats['average_planning_time']
                )

            # Calculate path length
            path_length = 0.0
            for i in range(len(path) - 1):
                world_curr = self.map_to_world(*path[i])
                world_next = self.map_to_world(*path[i + 1])
                path_length += math.sqrt(
                    (world_next[0] - world_curr[0])**2 +
                    (world_next[1] - world_curr[1])**2
                )

            # Update average path length
            if self.planning_stats['successful_plans'] == 1:
                self.planning_stats['average_path_length'] = path_length
            else:
                self.planning_stats['average_path_length'] = (
                    alpha * path_length +
                    (1 - alpha) * self.planning_stats['average_path_length']
                )

    def publish_path(self, path: List[Tuple[int, int]]):
        """Publish the planned path"""
        path_msg = Path()
        path_msg.header.stamp = self.get_clock().now().to_msg()
        path_msg.header.frame_id = 'map'

        for map_x, map_y in path:
            world_x, world_y = self.map_to_world(map_x, map_y)

            pose = PoseStamped()
            pose.header = path_msg.header
            pose.pose.position.x = world_x
            pose.pose.position.y = world_y
            pose.pose.position.z = 0.0
            pose.pose.orientation.w = 1.0

            path_msg.poses.append(pose)

        self.path_pub.publish(path_msg)
        self.last_path = path_msg

    def check_replanning_needed(self):
        """Check if replanning is needed"""
        if not self.dynamic_replanning or self.current_goal is None:
            return

        robot_pose = self.get_robot_pose()
        if robot_pose is None:
            return

        # Check if robot deviated from path
        if self.last_path is not None and len(self.last_path.poses) > 0:
            current_pos = (
                robot_pose.pose.pose.position.x,
                robot_pose.pose.pose.position.y
            )

            # Find closest point on path
            min_distance = float('inf')
            for pose in self.last_path.poses:
                path_pos = (pose.pose.position.x, pose.pose.position.y)
                distance = math.sqrt(
                    (current_pos[0] - path_pos[0])**2 +
                    (current_pos[1] - path_pos[1])**2
                )
                min_distance = min(min_distance, distance)

            # Replan if too far from path
            if min_distance > self.replanning_threshold:
                self.get_logger().info(f'🔄 Replanning: deviation {min_distance:.2f}m')
                self.planning_stats['replans'] += 1
                self.plan_path()

    def publish_statistics(self):
        """Publish detailed planning statistics"""
        stats_msg = String()

        success_rate = 0.0
        if self.planning_stats['total_plans'] > 0:
            success_rate = (self.planning_stats['successful_plans'] /
                          self.planning_stats['total_plans'])

        stats_data = {
            'total_plans': self.planning_stats['total_plans'],
            'successful_plans': self.planning_stats['successful_plans'],
            'success_rate': f'{success_rate:.3f}',
            'avg_planning_time': f"{self.planning_stats['average_planning_time']:.4f}s",
            'avg_path_length': f"{self.planning_stats['average_path_length']:.2f}m",
            'replans': self.planning_stats['replans'],
            'algorithm': self.planning_algorithm.value
        }

        stats_msg.data = ','.join([f'{k}:{v}' for k, v in stats_data.items()])
        self.stats_pub.publish(stats_msg)

    def replan_service_callback(self, request, response):
        """Service callback for manual replanning"""
        if self.current_goal is not None:
            self.get_logger().info('🔄 Manual replan requested')
            self.plan_path()
        else:
            self.get_logger().warn('⚠️ No goal set for replanning')

        return response

    def change_algorithm_callback(self, request, response):
        """Service callback to change planning algorithm"""
        algorithms = list(PlanningAlgorithm)
        current_index = algorithms.index(self.planning_algorithm)
        next_index = (current_index + 1) % len(algorithms)

        self.planning_algorithm = algorithms[next_index]
        self.get_logger().info(f'🔧 Algorithm changed to: {self.planning_algorithm.value}')

        # Replan with new algorithm
        if self.current_goal is not None:
            self.plan_path()

        return response
        
    def publish_status(self):
        """Publish planner status"""
        status_msg = String()
        
        if self.planning_active:
            status_data = {'status': 'PLANNING'}
        elif self.current_goal is not None:
            status_data = {'status': 'READY', 'algorithm': self.planning_algorithm.value}
        else:
            status_data = {'status': 'IDLE'}
            
        status_msg.data = ','.join([f'{k}:{v}' for k, v in status_data.items()])
        self.status_pub.publish(status_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = AdvancedGlobalPlanner()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
