#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Path, OccupancyGrid
from geometry_msgs.msg import Twist, PoseWithCovarianceStamped
from sensor_msgs.msg import LaserScan
from std_msgs.msg import Float32MultiArray, String
import numpy as np
import math
import time

class LocalPlanner(Node):
    def __init__(self):
        super().__init__('local_planner')
        
        # Parameters
        self.declare_parameter('max_linear_velocity', 0.5)
        self.declare_parameter('max_angular_velocity', 1.0)
        self.declare_parameter('min_obstacle_distance', 0.5)
        self.declare_parameter('lookahead_distance', 1.0)
        self.declare_parameter('goal_tolerance', 0.2)
        self.declare_parameter('path_following_method', 'pure_pursuit')  # 'pure_pursuit', 'stanley'
        self.declare_parameter('dynamic_window_enabled', True)
        self.declare_parameter('obstacle_stop_time', 5.0)  # seconds to wait before turning around
        
        self.max_linear_vel = self.get_parameter('max_linear_velocity').value
        self.max_angular_vel = self.get_parameter('max_angular_velocity').value
        self.min_obstacle_dist = self.get_parameter('min_obstacle_distance').value
        self.lookahead_dist = self.get_parameter('lookahead_distance').value
        self.goal_tolerance = self.get_parameter('goal_tolerance').value
        self.path_following_method = self.get_parameter('path_following_method').value
        self.dynamic_window_enabled = self.get_parameter('dynamic_window_enabled').value
        self.obstacle_stop_time = self.get_parameter('obstacle_stop_time').value
        
        # State variables
        self.current_path = None
        self.current_pose = None
        self.current_scan = None
        self.path_index = 0
        self.obstacle_detected = False
        self.obstacle_start_time = None
        self.is_turning_around = False
        
        # Subscribers
        self.path_sub = self.create_subscription(
            Path, '/global_path', self.path_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        self.obstacles_sub = self.create_subscription(
            Float32MultiArray, '/perception/fused_obstacles', self.obstacles_callback, 10)
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        self.local_path_pub = self.create_publisher(Path, '/local_path', 10)
        self.status_pub = self.create_publisher(String, '/local_planner/status', 10)
        
        # Timer for control loop
        self.control_timer = self.create_timer(0.1, self.control_loop)  # 10 Hz
        
        self.get_logger().info(f'Local Planner initialized with {self.path_following_method} method')
        
    def path_callback(self, msg):
        """Receive new global path"""
        self.current_path = msg
        self.path_index = 0
        self.is_turning_around = False
        self.get_logger().info(f'New path received with {len(msg.poses)} points')
        
    def pose_callback(self, msg):
        """Update current robot pose"""
        self.current_pose = msg
        
    def scan_callback(self, msg):
        """Process laser scan for obstacle detection"""
        self.current_scan = msg
        
    def obstacles_callback(self, msg):
        """Process fused obstacle data"""
        # Check for obstacles in front of robot
        if len(msg.data) == 0:
            self.obstacle_detected = False
            return
            
        # Process obstacles (format: x, y, confidence)
        obstacles_in_front = False
        min_distance = float('inf')
        
        for i in range(0, len(msg.data), 3):
            if i + 2 >= len(msg.data):
                break
                
            x, y, confidence = msg.data[i:i+3]
            
            # Check if obstacle is in front of robot (within a cone)
            distance = math.sqrt(x*x + y*y)
            angle = math.atan2(y, x)
            
            # Consider obstacles within 60 degrees in front and within min distance
            if abs(angle) < math.pi/3 and distance < self.min_obstacle_dist:
                obstacles_in_front = True
                min_distance = min(min_distance, distance)
                
        self.obstacle_detected = obstacles_in_front
        
        if obstacles_in_front:
            if self.obstacle_start_time is None:
                self.obstacle_start_time = time.time()
                self.get_logger().warn(f'Obstacle detected at {min_distance:.2f}m, stopping...')
        else:
            self.obstacle_start_time = None
            
    def find_lookahead_point(self):
        """Find lookahead point on the path"""
        if self.current_path is None or self.current_pose is None:
            return None
            
        current_x = self.current_pose.pose.pose.position.x
        current_y = self.current_pose.pose.pose.position.y
        
        # Find closest point on path
        min_distance = float('inf')
        closest_index = self.path_index
        
        for i in range(self.path_index, len(self.current_path.poses)):
            pose = self.current_path.poses[i]
            dx = pose.pose.position.x - current_x
            dy = pose.pose.position.y - current_y
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance < min_distance:
                min_distance = distance
                closest_index = i
                
        self.path_index = closest_index
        
        # Find lookahead point
        for i in range(closest_index, len(self.current_path.poses)):
            pose = self.current_path.poses[i]
            dx = pose.pose.position.x - current_x
            dy = pose.pose.position.y - current_y
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance >= self.lookahead_dist:
                return pose.pose.position.x, pose.pose.position.y
                
        # If no point found, use the last point
        if len(self.current_path.poses) > 0:
            last_pose = self.current_path.poses[-1]
            return last_pose.pose.position.x, last_pose.pose.position.y
            
        return None
        
    def pure_pursuit_control(self, target_x, target_y):
        """Pure pursuit path following algorithm"""
        if self.current_pose is None:
            return Twist()
            
        # Current robot state
        current_x = self.current_pose.pose.pose.position.x
        current_y = self.current_pose.pose.pose.position.y
        
        # Current robot orientation
        quat = self.current_pose.pose.pose.orientation
        current_yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                               1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
        
        # Calculate distance and angle to target
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)
        target_angle = math.atan2(dy, dx)
        
        # Calculate angle error
        angle_error = target_angle - current_yaw
        
        # Normalize angle error
        while angle_error > math.pi:
            angle_error -= 2 * math.pi
        while angle_error < -math.pi:
            angle_error += 2 * math.pi
            
        # Pure pursuit control
        cmd = Twist()
        
        # Check if we've reached the goal
        if distance < self.goal_tolerance:
            cmd.linear.x = 0.0
            cmd.angular.z = 0.0
            return cmd
            
        # Calculate curvature for pure pursuit
        lookahead_distance = max(distance, 0.1)  # Avoid division by zero
        curvature = 2 * math.sin(angle_error) / lookahead_distance
        
        # Calculate velocities
        if abs(angle_error) > math.pi/4:  # Large angle error, turn in place
            cmd.linear.x = 0.0
            cmd.angular.z = self.max_angular_vel * (1.0 if angle_error > 0 else -1.0)
        else:
            # Scale linear velocity based on angle error
            linear_scale = 1.0 - abs(angle_error) / (math.pi/2)
            cmd.linear.x = self.max_linear_vel * linear_scale
            cmd.angular.z = curvature * cmd.linear.x
            
        # Limit velocities
        cmd.linear.x = max(-self.max_linear_vel, min(self.max_linear_vel, cmd.linear.x))
        cmd.angular.z = max(-self.max_angular_vel, min(self.max_angular_vel, cmd.angular.z))
        
        return cmd
        
    def dynamic_window_approach(self, target_x, target_y):
        """Dynamic Window Approach for local obstacle avoidance"""
        if self.current_scan is None:
            return self.pure_pursuit_control(target_x, target_y)
            
        # Simplified DWA - evaluate velocity candidates
        best_cmd = Twist()
        best_score = -float('inf')
        
        # Velocity search space
        v_samples = np.linspace(0, self.max_linear_vel, 5)
        w_samples = np.linspace(-self.max_angular_vel, self.max_angular_vel, 9)
        
        current_x = self.current_pose.pose.pose.position.x
        current_y = self.current_pose.pose.pose.position.y
        quat = self.current_pose.pose.pose.orientation
        current_yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                               1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
        
        for v in v_samples:
            for w in w_samples:
                # Simulate trajectory
                score = self.evaluate_trajectory(v, w, target_x, target_y, current_x, current_y, current_yaw)
                
                if score > best_score:
                    best_score = score
                    best_cmd.linear.x = v
                    best_cmd.angular.z = w
                    
        return best_cmd
        
    def evaluate_trajectory(self, v, w, target_x, target_y, start_x, start_y, start_yaw):
        """Evaluate a trajectory for DWA"""
        # Simulate trajectory for 1 second
        dt = 0.1
        steps = 10
        
        x, y, yaw = start_x, start_y, start_yaw
        min_obstacle_dist = float('inf')
        
        for _ in range(steps):
            # Update pose
            x += v * math.cos(yaw) * dt
            y += v * math.sin(yaw) * dt
            yaw += w * dt
            
            # Check obstacle distance
            obstacle_dist = self.get_min_obstacle_distance(x, y)
            min_obstacle_dist = min(min_obstacle_dist, obstacle_dist)
            
        # Calculate final distance to target
        final_dist_to_target = math.sqrt((target_x - x)**2 + (target_y - y)**2)
        
        # Scoring function
        target_score = 1.0 / (1.0 + final_dist_to_target)  # Closer to target is better
        obstacle_score = min_obstacle_dist  # Farther from obstacles is better
        speed_score = v / self.max_linear_vel  # Faster is better
        
        # Combined score
        total_score = target_score + 2.0 * obstacle_score + 0.5 * speed_score
        
        # Penalize if too close to obstacles
        if min_obstacle_dist < self.min_obstacle_dist:
            total_score -= 10.0
            
        return total_score
        
    def get_min_obstacle_distance(self, x, y):
        """Get minimum distance to obstacles from a point"""
        if self.current_scan is None:
            return float('inf')
            
        min_dist = float('inf')
        
        for i, range_val in enumerate(self.current_scan.ranges):
            if math.isnan(range_val) or math.isinf(range_val):
                continue
                
            if range_val < self.current_scan.range_min or range_val > self.current_scan.range_max:
                continue
                
            angle = self.current_scan.angle_min + i * self.current_scan.angle_increment
            obs_x = x + range_val * math.cos(angle)
            obs_y = y + range_val * math.sin(angle)
            
            dist = math.sqrt((obs_x - x)**2 + (obs_y - y)**2)
            min_dist = min(min_dist, dist)
            
        return min_dist
        
    def turn_around_behavior(self):
        """Turn around when stuck"""
        cmd = Twist()
        cmd.linear.x = 0.0
        cmd.angular.z = self.max_angular_vel * 0.5  # Turn at half speed
        
        self.get_logger().info('Turning around due to persistent obstacle')
        return cmd
        
    def control_loop(self):
        """Main control loop"""
        cmd = Twist()
        
        # Check if we have a path to follow
        if self.current_path is None or len(self.current_path.poses) == 0:
            self.cmd_vel_pub.publish(cmd)  # Stop
            return
            
        # Handle obstacle behavior
        if self.obstacle_detected:
            if self.obstacle_start_time is not None:
                elapsed_time = time.time() - self.obstacle_start_time
                
                if elapsed_time > self.obstacle_stop_time:
                    # Turn around after waiting
                    cmd = self.turn_around_behavior()
                    self.is_turning_around = True
                else:
                    # Stop and wait
                    cmd = Twist()
                    
                status_msg = String()
                status_msg.data = f'OBSTACLE_DETECTED:WAITING:{elapsed_time:.1f}'
                self.status_pub.publish(status_msg)
                
                self.cmd_vel_pub.publish(cmd)
                return
        else:
            self.is_turning_around = False
            
        # Find target point
        target = self.find_lookahead_point()
        if target is None:
            self.cmd_vel_pub.publish(cmd)  # Stop
            return
            
        target_x, target_y = target
        
        # Choose control method
        if self.dynamic_window_enabled and not self.is_turning_around:
            cmd = self.dynamic_window_approach(target_x, target_y)
        else:
            cmd = self.pure_pursuit_control(target_x, target_y)
            
        # Publish command
        self.cmd_vel_pub.publish(cmd)
        
        # Publish status
        status_msg = String()
        if self.is_turning_around:
            status_msg.data = 'TURNING_AROUND'
        else:
            distance_to_target = math.sqrt((target_x - self.current_pose.pose.pose.position.x)**2 + 
                                         (target_y - self.current_pose.pose.pose.position.y)**2)
            status_msg.data = f'FOLLOWING_PATH:TARGET_DIST:{distance_to_target:.2f}'
            
        self.status_pub.publish(status_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = LocalPlanner()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
