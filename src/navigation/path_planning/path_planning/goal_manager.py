#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from std_msgs.msg import String
from std_srvs.srv import Empty
import json
import math

class GoalManager(Node):
    def __init__(self):
        super().__init__('goal_manager')
        
        # Parameters
        self.declare_parameter('predefined_goals_file', 'goals.json')
        self.declare_parameter('auto_cycle_goals', False)
        self.declare_parameter('cycle_interval', 30.0)  # seconds
        
        self.goals_file = self.get_parameter('predefined_goals_file').value
        self.auto_cycle = self.get_parameter('auto_cycle_goals').value
        self.cycle_interval = self.get_parameter('cycle_interval').value
        
        # State variables
        self.predefined_goals = []
        self.current_goal_index = 0
        self.current_pose = None
        self.last_goal_time = None
        
        # Load predefined goals
        self.load_predefined_goals()
        
        # Subscribers
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.executor_status_sub = self.create_subscription(
            String, '/path_executor/status', self.executor_status_callback, 10)
        
        # Publishers
        self.goal_pub = self.create_publisher(PoseStamped, '/move_base_simple/goal', 10)
        self.status_pub = self.create_publisher(String, '/goal_manager/status', 10)
        
        # Services
        self.next_goal_srv = self.create_service(Empty, 'next_goal', self.next_goal_callback)
        self.prev_goal_srv = self.create_service(Empty, 'prev_goal', self.prev_goal_callback)
        self.random_goal_srv = self.create_service(Empty, 'random_goal', self.random_goal_callback)
        self.home_goal_srv = self.create_service(Empty, 'go_home', self.home_goal_callback)
        
        # Timer for auto cycling
        if self.auto_cycle:
            self.cycle_timer = self.create_timer(self.cycle_interval, self.auto_cycle_goals)
            
        self.get_logger().info(f'Goal Manager initialized with {len(self.predefined_goals)} predefined goals')
        
    def load_predefined_goals(self):
        """Load predefined goals from file"""
        try:
            with open(self.goals_file, 'r') as f:
                goals_data = json.load(f)
                
            self.predefined_goals = []
            for goal_data in goals_data.get('goals', []):
                goal = {
                    'name': goal_data.get('name', 'Unnamed'),
                    'x': goal_data.get('x', 0.0),
                    'y': goal_data.get('y', 0.0),
                    'yaw': goal_data.get('yaw', 0.0),
                    'description': goal_data.get('description', '')
                }
                self.predefined_goals.append(goal)
                
            self.get_logger().info(f'Loaded {len(self.predefined_goals)} predefined goals')
            
        except FileNotFoundError:
            self.get_logger().warn(f'Goals file {self.goals_file} not found, creating default goals')
            self.create_default_goals()
        except Exception as e:
            self.get_logger().error(f'Error loading goals file: {e}')
            self.create_default_goals()
            
    def create_default_goals(self):
        """Create default goals for testing"""
        self.predefined_goals = [
            {'name': 'Home', 'x': 0.0, 'y': 0.0, 'yaw': 0.0, 'description': 'Starting position'},
            {'name': 'Point A', 'x': 2.0, 'y': 2.0, 'yaw': 0.0, 'description': 'Test point A'},
            {'name': 'Point B', 'x': -2.0, 'y': 2.0, 'yaw': 1.57, 'description': 'Test point B'},
            {'name': 'Point C', 'x': -2.0, 'y': -2.0, 'yaw': 3.14, 'description': 'Test point C'},
            {'name': 'Point D', 'x': 2.0, 'y': -2.0, 'yaw': -1.57, 'description': 'Test point D'},
        ]
        
        # Save default goals to file
        self.save_goals_to_file()
        
    def save_goals_to_file(self):
        """Save current goals to file"""
        try:
            goals_data = {'goals': self.predefined_goals}
            with open(self.goals_file, 'w') as f:
                json.dump(goals_data, f, indent=2)
            self.get_logger().info(f'Goals saved to {self.goals_file}')
        except Exception as e:
            self.get_logger().error(f'Error saving goals: {e}')
            
    def pose_callback(self, msg):
        """Update current robot pose"""
        self.current_pose = msg
        
    def executor_status_callback(self, msg):
        """Handle path executor status updates"""
        status = msg.data
        
        if status.startswith('COMPLETED'):
            self.get_logger().info('Goal reached!')
            if self.auto_cycle:
                # Automatically go to next goal after completion
                self.cycle_to_next_goal()
                
    def create_goal_message(self, goal_data):
        """Create PoseStamped message from goal data"""
        goal_msg = PoseStamped()
        goal_msg.header.stamp = self.get_clock().now().to_msg()
        goal_msg.header.frame_id = 'map'
        
        goal_msg.pose.position.x = goal_data['x']
        goal_msg.pose.position.y = goal_data['y']
        goal_msg.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        yaw = goal_data['yaw']
        goal_msg.pose.orientation.x = 0.0
        goal_msg.pose.orientation.y = 0.0
        goal_msg.pose.orientation.z = math.sin(yaw / 2.0)
        goal_msg.pose.orientation.w = math.cos(yaw / 2.0)
        
        return goal_msg
        
    def publish_goal(self, goal_data):
        """Publish a goal"""
        goal_msg = self.create_goal_message(goal_data)
        self.goal_pub.publish(goal_msg)
        
        self.get_logger().info(f'Published goal: {goal_data["name"]} at ({goal_data["x"]:.2f}, {goal_data["y"]:.2f})')
        
        # Publish status
        status_msg = String()
        status_msg.data = f'GOAL_SENT:{goal_data["name"]}:({goal_data["x"]:.2f},{goal_data["y"]:.2f})'
        self.status_pub.publish(status_msg)
        
    def next_goal_callback(self, request, response):
        """Service callback to go to next goal"""
        if len(self.predefined_goals) == 0:
            self.get_logger().warn('No predefined goals available')
            return response
            
        self.current_goal_index = (self.current_goal_index + 1) % len(self.predefined_goals)
        goal = self.predefined_goals[self.current_goal_index]
        self.publish_goal(goal)
        
        return response
        
    def prev_goal_callback(self, request, response):
        """Service callback to go to previous goal"""
        if len(self.predefined_goals) == 0:
            self.get_logger().warn('No predefined goals available')
            return response
            
        self.current_goal_index = (self.current_goal_index - 1) % len(self.predefined_goals)
        goal = self.predefined_goals[self.current_goal_index]
        self.publish_goal(goal)
        
        return response
        
    def random_goal_callback(self, request, response):
        """Service callback to go to random goal"""
        if len(self.predefined_goals) == 0:
            self.get_logger().warn('No predefined goals available')
            return response
            
        import random
        self.current_goal_index = random.randint(0, len(self.predefined_goals) - 1)
        goal = self.predefined_goals[self.current_goal_index]
        self.publish_goal(goal)
        
        return response
        
    def home_goal_callback(self, request, response):
        """Service callback to go home (first goal)"""
        if len(self.predefined_goals) == 0:
            self.get_logger().warn('No predefined goals available')
            return response
            
        self.current_goal_index = 0
        goal = self.predefined_goals[0]
        self.publish_goal(goal)
        
        return response
        
    def cycle_to_next_goal(self):
        """Cycle to next goal in auto mode"""
        if len(self.predefined_goals) == 0:
            return
            
        self.current_goal_index = (self.current_goal_index + 1) % len(self.predefined_goals)
        goal = self.predefined_goals[self.current_goal_index]
        
        self.get_logger().info(f'Auto-cycling to next goal: {goal["name"]}')
        self.publish_goal(goal)
        
    def auto_cycle_goals(self):
        """Timer callback for auto cycling goals"""
        if len(self.predefined_goals) == 0:
            return
            
        # Only cycle if robot is not currently navigating
        # This is a simple implementation - could be improved with better state tracking
        self.cycle_to_next_goal()
        
    def add_current_pose_as_goal(self, name):
        """Add current robot pose as a new goal"""
        if self.current_pose is None:
            self.get_logger().warn('No current pose available')
            return False
            
        # Extract current pose
        x = self.current_pose.pose.pose.position.x
        y = self.current_pose.pose.pose.position.y
        
        # Extract yaw from quaternion
        quat = self.current_pose.pose.pose.orientation
        yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                        1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
        
        # Create new goal
        new_goal = {
            'name': name,
            'x': x,
            'y': y,
            'yaw': yaw,
            'description': f'Added from current pose at {self.get_clock().now().to_msg()}'
        }
        
        self.predefined_goals.append(new_goal)
        self.save_goals_to_file()
        
        self.get_logger().info(f'Added new goal: {name} at ({x:.2f}, {y:.2f})')
        return True
        
    def list_goals(self):
        """List all predefined goals"""
        self.get_logger().info('Predefined goals:')
        for i, goal in enumerate(self.predefined_goals):
            marker = '→' if i == self.current_goal_index else ' '
            self.get_logger().info(f'{marker} {i}: {goal["name"]} at ({goal["x"]:.2f}, {goal["y"]:.2f}) - {goal["description"]}')

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = GoalManager()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
