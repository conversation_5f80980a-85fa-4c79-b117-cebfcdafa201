global_planner:
  ros__parameters:
    # Planning algorithm: 'astar' or 'dijkstra'
    planning_algorithm: 'astar'
    
    # A* parameters
    heuristic_weight: 1.0
    diagonal_movement: true
    
    # Path optimization
    smoothing_enabled: true
    path_resolution: 0.1  # meters
    
    # Safety parameters
    obstacle_inflation: 0.2  # meters

local_planner:
  ros__parameters:
    # Velocity limits
    max_linear_velocity: 0.5   # m/s
    max_angular_velocity: 1.0  # rad/s
    
    # Safety parameters
    min_obstacle_distance: 0.5  # meters
    lookahead_distance: 1.0     # meters
    goal_tolerance: 0.2         # meters
    
    # Path following method: 'pure_pursuit' or 'stanley'
    path_following_method: 'pure_pursuit'
    
    # Dynamic Window Approach
    dynamic_window_enabled: true
    
    # Obstacle behavior
    obstacle_stop_time: 5.0  # seconds to wait before turning around

path_executor:
  ros__parameters:
    # Goal parameters
    goal_tolerance: 0.3      # meters
    path_timeout: 30.0       # seconds
    replan_threshold: 2.0    # meters
    max_retries: 3

goal_manager:
  ros__parameters:
    # Goal management
    predefined_goals_file: 'goals.json'
    auto_cycle_goals: false
    cycle_interval: 30.0     # seconds
