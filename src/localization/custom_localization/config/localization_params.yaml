scan_matcher:
  ros__parameters:
    # ICP parameters
    max_iterations: 50
    convergence_threshold: 0.001
    max_correspondence_distance: 0.5
    scan_downsample_factor: 2

particle_filter:
  ros__parameters:
    # Particle filter parameters
    num_particles: 1000
    
    # Motion model noise parameters
    alpha1: 0.2    # Rotation noise from rotation
    alpha2: 0.2    # Rotation noise from translation
    alpha3: 0.2    # Translation noise from translation
    alpha4: 0.2    # Translation noise from rotation
    
    # Sensor model parameters
    z_hit: 0.7     # Hit probability
    z_short: 0.1   # Short reading probability
    z_max: 0.1     # Max range probability
    z_rand: 0.1    # Random reading probability
    sigma_hit: 0.2 # Hit standard deviation
    lambda_short: 0.1  # Short reading parameter
    
    # Resampling
    resample_threshold: 0.5

pose_estimator:
  ros__parameters:
    # Fusion method: 'kalman' or 'weighted'
    fusion_method: 'kalman'
    
    # Weights for weighted fusion
    scan_matcher_weight: 0.7
    particle_filter_weight: 0.3
    odometry_weight: 0.1
    
    # Timing parameters
    pose_timeout: 1.0      # seconds
    publish_rate: 20.0     # Hz
