#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_custom_localization = get_package_share_directory('custom_localization')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(pkg_custom_localization, 'config', 'localization_params.yaml'),
        description='Full path to localization parameters file'
    )
    
    use_scan_matcher_arg = DeclareLaunchArgument(
        'use_scan_matcher',
        default_value='true',
        description='Enable scan matcher'
    )
    
    use_particle_filter_arg = DeclareLaunchArgument(
        'use_particle_filter',
        default_value='true',
        description='Enable particle filter'
    )
    
    use_pose_estimator_arg = DeclareLaunchArgument(
        'use_pose_estimator',
        default_value='true',
        description='Enable pose estimator (fusion)'
    )
    
    # Scan Matcher Node
    scan_matcher_node = Node(
        package='custom_localization',
        executable='scan_matcher',
        name='scan_matcher',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_scan_matcher'))
    )
    
    # Particle Filter Node
    particle_filter_node = Node(
        package='custom_localization',
        executable='particle_filter',
        name='particle_filter',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_particle_filter'))
    )
    
    # Pose Estimator Node (Fusion)
    pose_estimator_node = Node(
        package='custom_localization',
        executable='pose_estimator',
        name='pose_estimator',
        output='screen',
        parameters=[
            LaunchConfiguration('params_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_pose_estimator'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        params_file_arg,
        use_scan_matcher_arg,
        use_particle_filter_arg,
        use_pose_estimator_arg,
        
        # Nodes
        scan_matcher_node,
        particle_filter_node,
        pose_estimator_node,
    ])
