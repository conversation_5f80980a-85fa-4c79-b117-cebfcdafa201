#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
from nav_msgs.msg import OccupancyGrid
from geometry_msgs.msg import PoseStamped, Transform
import numpy as np
import math
from scipy.optimize import minimize

class ScanMatcher(Node):
    def __init__(self):
        super().__init__('scan_matcher')
        
        # Parameters
        self.declare_parameter('max_iterations', 50)
        self.declare_parameter('convergence_threshold', 0.001)
        self.declare_parameter('max_correspondence_distance', 0.5)
        self.declare_parameter('scan_downsample_factor', 2)
        
        self.max_iterations = self.get_parameter('max_iterations').value
        self.convergence_threshold = self.get_parameter('convergence_threshold').value
        self.max_correspondence_distance = self.get_parameter('max_correspondence_distance').value
        self.scan_downsample_factor = self.get_parameter('scan_downsample_factor').value
        
        # State
        self.reference_map = None
        self.current_scan = None
        self.last_pose = np.array([0.0, 0.0, 0.0])  # x, y, theta
        
        # Subscribers
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.map_callback, 10)
        
        # Publishers
        self.matched_pose_pub = self.create_publisher(
            PoseStamped, '/scan_matcher/pose', 10)
        self.transform_pub = self.create_publisher(
            Transform, '/scan_matcher/transform', 10)
        
        self.get_logger().info('Scan Matcher initialized')
        
    def scan_callback(self, msg):
        """Process incoming laser scan"""
        self.current_scan = msg
        
        if self.reference_map is not None:
            # Perform scan matching
            matched_pose = self.match_scan_to_map()
            
            if matched_pose is not None:
                self.publish_matched_pose(matched_pose)
                
    def map_callback(self, msg):
        """Store reference map for scan matching"""
        self.reference_map = msg
        self.get_logger().info('Reference map received for scan matching')
        
    def scan_to_points(self, scan):
        """Convert laser scan to 2D points"""
        points = []
        
        for i in range(0, len(scan.ranges), self.scan_downsample_factor):
            range_val = scan.ranges[i]
            
            if math.isnan(range_val) or math.isinf(range_val):
                continue
                
            if range_val < scan.range_min or range_val > scan.range_max:
                continue
                
            angle = scan.angle_min + i * scan.angle_increment
            x = range_val * math.cos(angle)
            y = range_val * math.sin(angle)
            points.append([x, y])
            
        return np.array(points)
        
    def transform_points(self, points, pose):
        """Transform points by pose [x, y, theta]"""
        if len(points) == 0:
            return points
            
        x, y, theta = pose
        
        # Rotation matrix
        cos_theta = math.cos(theta)
        sin_theta = math.sin(theta)
        
        # Transform points
        transformed = np.zeros_like(points)
        transformed[:, 0] = cos_theta * points[:, 0] - sin_theta * points[:, 1] + x
        transformed[:, 1] = sin_theta * points[:, 0] + cos_theta * points[:, 1] + y
        
        return transformed
        
    def map_to_points(self, occupancy_grid):
        """Extract occupied points from occupancy grid"""
        if occupancy_grid is None:
            return np.array([])
            
        # Convert occupancy grid to numpy array
        width = occupancy_grid.info.width
        height = occupancy_grid.info.height
        resolution = occupancy_grid.info.resolution
        origin_x = occupancy_grid.info.origin.position.x
        origin_y = occupancy_grid.info.origin.position.y
        
        grid_data = np.array(occupancy_grid.data).reshape((height, width))
        
        # Find occupied cells (value > 50)
        occupied_indices = np.where(grid_data > 50)
        
        # Convert to world coordinates
        points = []
        for i, j in zip(occupied_indices[0], occupied_indices[1]):
            x = j * resolution + origin_x
            y = i * resolution + origin_y
            points.append([x, y])
            
        return np.array(points)
        
    def find_correspondences(self, scan_points, map_points):
        """Find closest correspondences between scan and map points"""
        if len(scan_points) == 0 or len(map_points) == 0:
            return [], []
            
        correspondences = []
        
        for scan_point in scan_points:
            # Find closest map point
            distances = np.linalg.norm(map_points - scan_point, axis=1)
            min_idx = np.argmin(distances)
            min_distance = distances[min_idx]
            
            # Only accept if within threshold
            if min_distance < self.max_correspondence_distance:
                correspondences.append((scan_point, map_points[min_idx]))
                
        return correspondences
        
    def compute_transform_icp(self, correspondences):
        """Compute transformation using ICP algorithm"""
        if len(correspondences) < 3:
            return None
            
        # Extract source and target points
        source_points = np.array([corr[0] for corr in correspondences])
        target_points = np.array([corr[1] for corr in correspondences])
        
        # Compute centroids
        source_centroid = np.mean(source_points, axis=0)
        target_centroid = np.mean(target_points, axis=0)
        
        # Center the points
        source_centered = source_points - source_centroid
        target_centered = target_points - target_centroid
        
        # Compute cross-covariance matrix
        H = source_centered.T @ target_centered
        
        # SVD
        U, S, Vt = np.linalg.svd(H)
        
        # Compute rotation
        R = Vt.T @ U.T
        
        # Ensure proper rotation (det(R) = 1)
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
            
        # Compute translation
        t = target_centroid - R @ source_centroid
        
        # Convert to pose format [x, y, theta]
        theta = math.atan2(R[1, 0], R[0, 0])
        
        return np.array([t[0], t[1], theta])
        
    def match_scan_to_map(self):
        """Perform scan-to-map matching using ICP"""
        if self.current_scan is None or self.reference_map is None:
            return None
            
        # Convert scan to points
        scan_points = self.scan_to_points(self.current_scan)
        
        if len(scan_points) == 0:
            return None
            
        # Get map points
        map_points = self.map_to_points(self.reference_map)
        
        if len(map_points) == 0:
            return None
            
        # Initial pose estimate (use last known pose)
        current_pose = self.last_pose.copy()
        
        # ICP iterations
        for iteration in range(self.max_iterations):
            # Transform scan points with current pose estimate
            transformed_scan = self.transform_points(scan_points, current_pose)
            
            # Find correspondences
            correspondences = self.find_correspondences(transformed_scan, map_points)
            
            if len(correspondences) < 3:
                self.get_logger().warn(f'Insufficient correspondences: {len(correspondences)}')
                break
                
            # Compute transformation
            delta_pose = self.compute_transform_icp(correspondences)
            
            if delta_pose is None:
                break
                
            # Update pose
            old_pose = current_pose.copy()
            current_pose += delta_pose
            
            # Check convergence
            pose_change = np.linalg.norm(current_pose - old_pose)
            if pose_change < self.convergence_threshold:
                self.get_logger().debug(f'ICP converged after {iteration + 1} iterations')
                break
                
        # Update last pose
        self.last_pose = current_pose
        
        return current_pose
        
    def publish_matched_pose(self, pose):
        """Publish matched pose"""
        pose_msg = PoseStamped()
        pose_msg.header.stamp = self.get_clock().now().to_msg()
        pose_msg.header.frame_id = 'map'
        
        # Position
        pose_msg.pose.position.x = pose[0]
        pose_msg.pose.position.y = pose[1]
        pose_msg.pose.position.z = 0.0
        
        # Orientation (convert yaw to quaternion)
        yaw = pose[2]
        pose_msg.pose.orientation.x = 0.0
        pose_msg.pose.orientation.y = 0.0
        pose_msg.pose.orientation.z = math.sin(yaw / 2.0)
        pose_msg.pose.orientation.w = math.cos(yaw / 2.0)
        
        self.matched_pose_pub.publish(pose_msg)
        
        # Also publish transform
        transform_msg = Transform()
        transform_msg.translation.x = pose[0]
        transform_msg.translation.y = pose[1]
        transform_msg.translation.z = 0.0
        transform_msg.rotation = pose_msg.pose.orientation
        
        self.transform_pub.publish(transform_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = ScanMatcher()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
