#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
from nav_msgs.msg import OccupancyGrid, Odometry
from geometry_msgs.msg import PoseStamped, PoseArray, Pose
import numpy as np
import math
import random

class ParticleFilter(Node):
    def __init__(self):
        super().__init__('particle_filter')
        
        # Parameters
        self.declare_parameter('num_particles', 1000)
        self.declare_parameter('alpha1', 0.2)  # Rotation noise from rotation
        self.declare_parameter('alpha2', 0.2)  # Rotation noise from translation
        self.declare_parameter('alpha3', 0.2)  # Translation noise from translation
        self.declare_parameter('alpha4', 0.2)  # Translation noise from rotation
        self.declare_parameter('z_hit', 0.7)
        self.declare_parameter('z_short', 0.1)
        self.declare_parameter('z_max', 0.1)
        self.declare_parameter('z_rand', 0.1)
        self.declare_parameter('sigma_hit', 0.2)
        self.declare_parameter('lambda_short', 0.1)
        self.declare_parameter('resample_threshold', 0.5)
        
        self.num_particles = self.get_parameter('num_particles').value
        self.alpha1 = self.get_parameter('alpha1').value
        self.alpha2 = self.get_parameter('alpha2').value
        self.alpha3 = self.get_parameter('alpha3').value
        self.alpha4 = self.get_parameter('alpha4').value
        self.z_hit = self.get_parameter('z_hit').value
        self.z_short = self.get_parameter('z_short').value
        self.z_max = self.get_parameter('z_max').value
        self.z_rand = self.get_parameter('z_rand').value
        self.sigma_hit = self.get_parameter('sigma_hit').value
        self.lambda_short = self.get_parameter('lambda_short').value
        self.resample_threshold = self.get_parameter('resample_threshold').value
        
        # Particle state
        self.particles = None
        self.weights = None
        self.map_data = None
        self.last_odom = None
        
        # Subscribers
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        self.odom_sub = self.create_subscription(
            Odometry, '/odom', self.odom_callback, 10)
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.map_callback, 10)
        
        # Publishers
        self.pose_pub = self.create_publisher(
            PoseStamped, '/particle_filter/pose', 10)
        self.particles_pub = self.create_publisher(
            PoseArray, '/particle_filter/particles', 10)
        
        self.get_logger().info('Particle Filter initialized')
        
    def initialize_particles(self, map_bounds):
        """Initialize particles randomly within map bounds"""
        min_x, max_x, min_y, max_y = map_bounds
        
        self.particles = np.zeros((self.num_particles, 3))  # x, y, theta
        self.weights = np.ones(self.num_particles) / self.num_particles
        
        for i in range(self.num_particles):
            self.particles[i, 0] = random.uniform(min_x, max_x)  # x
            self.particles[i, 1] = random.uniform(min_y, max_y)  # y
            self.particles[i, 2] = random.uniform(-math.pi, math.pi)  # theta
            
        self.get_logger().info(f'Initialized {self.num_particles} particles')
        
    def map_callback(self, msg):
        """Store map data for likelihood calculation"""
        self.map_data = msg
        
        # Initialize particles if not done yet
        if self.particles is None:
            width = msg.info.width
            height = msg.info.height
            resolution = msg.info.resolution
            origin_x = msg.info.origin.position.x
            origin_y = msg.info.origin.position.y
            
            max_x = origin_x + width * resolution
            max_y = origin_y + height * resolution
            
            self.initialize_particles((origin_x, max_x, origin_y, max_y))
            
    def odom_callback(self, msg):
        """Process odometry for motion model"""
        if self.last_odom is not None and self.particles is not None:
            # Calculate motion
            delta_x = msg.pose.pose.position.x - self.last_odom.pose.pose.position.x
            delta_y = msg.pose.pose.position.y - self.last_odom.pose.pose.position.y
            
            # Calculate rotation change
            curr_quat = msg.pose.pose.orientation
            last_quat = self.last_odom.pose.pose.orientation
            
            curr_yaw = math.atan2(2.0 * (curr_quat.w * curr_quat.z + curr_quat.x * curr_quat.y),
                                1.0 - 2.0 * (curr_quat.y * curr_quat.y + curr_quat.z * curr_quat.z))
            last_yaw = math.atan2(2.0 * (last_quat.w * last_quat.z + last_quat.x * last_quat.y),
                                1.0 - 2.0 * (last_quat.y * last_quat.y + last_quat.z * last_quat.z))
            
            delta_theta = curr_yaw - last_yaw
            
            # Normalize angle
            while delta_theta > math.pi:
                delta_theta -= 2 * math.pi
            while delta_theta < -math.pi:
                delta_theta += 2 * math.pi
                
            # Apply motion model
            self.motion_model(delta_x, delta_y, delta_theta)
            
        self.last_odom = msg
        
    def motion_model(self, delta_x, delta_y, delta_theta):
        """Apply motion model to particles"""
        delta_trans = math.sqrt(delta_x*delta_x + delta_y*delta_y)
        
        if delta_trans < 0.01 and abs(delta_theta) < 0.01:
            return  # No significant motion
            
        for i in range(self.num_particles):
            # Add noise to motion
            noisy_delta_trans = delta_trans + np.random.normal(0, 
                self.alpha3 * delta_trans + self.alpha1 * abs(delta_theta))
            noisy_delta_theta = delta_theta + np.random.normal(0,
                self.alpha1 * abs(delta_theta) + self.alpha2 * delta_trans)
            
            # Calculate motion direction
            if delta_trans > 0:
                motion_angle = math.atan2(delta_y, delta_x)
            else:
                motion_angle = self.particles[i, 2]
                
            # Update particle position
            self.particles[i, 0] += noisy_delta_trans * math.cos(motion_angle)
            self.particles[i, 1] += noisy_delta_trans * math.sin(motion_angle)
            self.particles[i, 2] += noisy_delta_theta
            
            # Normalize angle
            while self.particles[i, 2] > math.pi:
                self.particles[i, 2] -= 2 * math.pi
            while self.particles[i, 2] < -math.pi:
                self.particles[i, 2] += 2 * math.pi
                
    def scan_callback(self, msg):
        """Process laser scan for measurement update"""
        if self.particles is None or self.map_data is None:
            return
            
        # Update particle weights based on scan likelihood
        self.measurement_model(msg)
        
        # Resample if needed
        effective_particles = 1.0 / np.sum(self.weights ** 2)
        if effective_particles < self.resample_threshold * self.num_particles:
            self.resample()
            
        # Publish results
        self.publish_pose_estimate()
        self.publish_particles()
        
    def measurement_model(self, scan):
        """Update particle weights based on scan likelihood"""
        for i in range(self.num_particles):
            self.weights[i] *= self.calculate_scan_likelihood(scan, self.particles[i])
            
        # Normalize weights
        weight_sum = np.sum(self.weights)
        if weight_sum > 0:
            self.weights /= weight_sum
        else:
            # Reset weights if all are zero
            self.weights = np.ones(self.num_particles) / self.num_particles
            
    def calculate_scan_likelihood(self, scan, particle):
        """Calculate likelihood of scan given particle pose"""
        likelihood = 1.0
        
        # Sample every nth ray for efficiency
        step = max(1, len(scan.ranges) // 20)
        
        for i in range(0, len(scan.ranges), step):
            range_val = scan.ranges[i]
            
            if math.isnan(range_val) or math.isinf(range_val):
                continue
                
            if range_val < scan.range_min or range_val > scan.range_max:
                continue
                
            # Calculate expected range from map
            angle = scan.angle_min + i * scan.angle_increment + particle[2]
            expected_range = self.ray_cast(particle[0], particle[1], angle)
            
            # Calculate likelihood using beam model
            ray_likelihood = self.beam_model(range_val, expected_range, scan.range_max)
            likelihood *= ray_likelihood
            
        return likelihood
        
    def ray_cast(self, x, y, angle):
        """Cast ray from position in given direction and return range to obstacle"""
        if self.map_data is None:
            return float('inf')
            
        resolution = self.map_data.info.resolution
        origin_x = self.map_data.info.origin.position.x
        origin_y = self.map_data.info.origin.position.y
        width = self.map_data.info.width
        height = self.map_data.info.height
        
        # Ray casting parameters
        max_range = 12.0
        step_size = resolution / 2.0
        
        # Cast ray
        current_range = 0.0
        while current_range < max_range:
            # Calculate current position
            curr_x = x + current_range * math.cos(angle)
            curr_y = y + current_range * math.sin(angle)
            
            # Convert to map coordinates
            map_x = int((curr_x - origin_x) / resolution)
            map_y = int((curr_y - origin_y) / resolution)
            
            # Check bounds
            if map_x < 0 or map_x >= width or map_y < 0 or map_y >= height:
                return max_range
                
            # Check occupancy
            map_index = map_y * width + map_x
            if map_index < len(self.map_data.data):
                occupancy = self.map_data.data[map_index]
                if occupancy > 50:  # Occupied
                    return current_range
                    
            current_range += step_size
            
        return max_range
        
    def beam_model(self, measured_range, expected_range, max_range):
        """Calculate beam model likelihood"""
        if measured_range >= max_range:
            return self.z_max
            
        # Hit probability
        if abs(measured_range - expected_range) < 3 * self.sigma_hit:
            p_hit = self.z_hit * math.exp(-0.5 * ((measured_range - expected_range) / self.sigma_hit) ** 2)
        else:
            p_hit = 0.0
            
        # Short probability
        if measured_range < expected_range:
            p_short = self.z_short * self.lambda_short * math.exp(-self.lambda_short * measured_range)
        else:
            p_short = 0.0
            
        # Random probability
        p_rand = self.z_rand / max_range
        
        return p_hit + p_short + p_rand
        
    def resample(self):
        """Resample particles based on weights"""
        # Low variance resampling
        new_particles = np.zeros_like(self.particles)
        new_weights = np.ones(self.num_particles) / self.num_particles
        
        # Cumulative weights
        cumulative_weights = np.cumsum(self.weights)
        
        # Random start
        r = random.random() / self.num_particles
        
        j = 0
        for i in range(self.num_particles):
            u = r + i / self.num_particles
            
            while u > cumulative_weights[j]:
                j += 1
                
            new_particles[i] = self.particles[j].copy()
            
        self.particles = new_particles
        self.weights = new_weights
        
    def publish_pose_estimate(self):
        """Publish weighted average pose estimate"""
        if self.particles is None:
            return
            
        # Calculate weighted average
        avg_x = np.average(self.particles[:, 0], weights=self.weights)
        avg_y = np.average(self.particles[:, 1], weights=self.weights)
        
        # Handle circular average for angle
        sin_sum = np.sum(self.weights * np.sin(self.particles[:, 2]))
        cos_sum = np.sum(self.weights * np.cos(self.particles[:, 2]))
        avg_theta = math.atan2(sin_sum, cos_sum)
        
        # Publish pose
        pose_msg = PoseStamped()
        pose_msg.header.stamp = self.get_clock().now().to_msg()
        pose_msg.header.frame_id = 'map'
        
        pose_msg.pose.position.x = avg_x
        pose_msg.pose.position.y = avg_y
        pose_msg.pose.position.z = 0.0
        
        pose_msg.pose.orientation.x = 0.0
        pose_msg.pose.orientation.y = 0.0
        pose_msg.pose.orientation.z = math.sin(avg_theta / 2.0)
        pose_msg.pose.orientation.w = math.cos(avg_theta / 2.0)
        
        self.pose_pub.publish(pose_msg)
        
    def publish_particles(self):
        """Publish particle cloud for visualization"""
        if self.particles is None:
            return
            
        particles_msg = PoseArray()
        particles_msg.header.stamp = self.get_clock().now().to_msg()
        particles_msg.header.frame_id = 'map'
        
        for i in range(self.num_particles):
            pose = Pose()
            pose.position.x = self.particles[i, 0]
            pose.position.y = self.particles[i, 1]
            pose.position.z = 0.0
            
            theta = self.particles[i, 2]
            pose.orientation.x = 0.0
            pose.orientation.y = 0.0
            pose.orientation.z = math.sin(theta / 2.0)
            pose.orientation.w = math.cos(theta / 2.0)
            
            particles_msg.poses.append(pose)
            
        self.particles_pub.publish(particles_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = ParticleFilter()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
