#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from nav_msgs.msg import Odometry
from std_msgs.msg import Float32
import numpy as np
import math
import time

class PoseEstimator(Node):
    def __init__(self):
        super().__init__('pose_estimator')
        
        # Parameters
        self.declare_parameter('fusion_method', 'kalman')  # 'kalman' or 'weighted'
        self.declare_parameter('scan_matcher_weight', 0.7)
        self.declare_parameter('particle_filter_weight', 0.3)
        self.declare_parameter('odometry_weight', 0.1)
        self.declare_parameter('pose_timeout', 1.0)
        self.declare_parameter('publish_rate', 20.0)
        
        self.fusion_method = self.get_parameter('fusion_method').value
        self.scan_matcher_weight = self.get_parameter('scan_matcher_weight').value
        self.particle_filter_weight = self.get_parameter('particle_filter_weight').value
        self.odometry_weight = self.get_parameter('odometry_weight').value
        self.pose_timeout = self.get_parameter('pose_timeout').value
        self.publish_rate = self.get_parameter('publish_rate').value
        
        # State variables
        self.scan_matcher_pose = None
        self.particle_filter_pose = None
        self.odometry_pose = None
        self.fused_pose = None
        
        # Timestamps
        self.scan_matcher_time = None
        self.particle_filter_time = None
        self.odometry_time = None
        
        # Kalman filter state
        self.state = np.zeros(6)  # [x, y, theta, vx, vy, vtheta]
        self.covariance = np.eye(6) * 0.1
        
        # Subscribers
        self.scan_matcher_sub = self.create_subscription(
            PoseStamped, '/scan_matcher/pose', self.scan_matcher_callback, 10)
        self.particle_filter_sub = self.create_subscription(
            PoseStamped, '/particle_filter/pose', self.particle_filter_callback, 10)
        self.odom_sub = self.create_subscription(
            Odometry, '/odom', self.odometry_callback, 10)
        
        # Publishers
        self.fused_pose_pub = self.create_publisher(
            PoseWithCovarianceStamped, '/localization/pose', 10)
        self.confidence_pub = self.create_publisher(
            Float32, '/localization/confidence', 10)
        
        # Timer for fusion updates
        self.fusion_timer = self.create_timer(1.0 / self.publish_rate, self.update_fusion)
        
        self.get_logger().info(f'Pose Estimator initialized with {self.fusion_method} fusion')
        
    def scan_matcher_callback(self, msg):
        """Process scan matcher pose"""
        self.scan_matcher_pose = msg
        self.scan_matcher_time = time.time()
        
    def particle_filter_callback(self, msg):
        """Process particle filter pose"""
        self.particle_filter_pose = msg
        self.particle_filter_time = time.time()
        
    def odometry_callback(self, msg):
        """Process odometry data"""
        # Convert odometry to pose
        pose_msg = PoseStamped()
        pose_msg.header = msg.header
        pose_msg.pose = msg.pose.pose
        
        self.odometry_pose = pose_msg
        self.odometry_time = time.time()
        
    def update_fusion(self):
        """Update fused pose estimate"""
        current_time = time.time()
        
        # Check which poses are available and recent
        scan_matcher_valid = (self.scan_matcher_pose is not None and 
                            self.scan_matcher_time is not None and
                            (current_time - self.scan_matcher_time) < self.pose_timeout)
        
        particle_filter_valid = (self.particle_filter_pose is not None and 
                                self.particle_filter_time is not None and
                                (current_time - self.particle_filter_time) < self.pose_timeout)
        
        odometry_valid = (self.odometry_pose is not None and 
                         self.odometry_time is not None and
                         (current_time - self.odometry_time) < self.pose_timeout)
        
        if not any([scan_matcher_valid, particle_filter_valid, odometry_valid]):
            return  # No valid poses available
            
        # Perform fusion based on method
        if self.fusion_method == 'kalman':
            self.kalman_fusion(scan_matcher_valid, particle_filter_valid, odometry_valid)
        else:
            self.weighted_fusion(scan_matcher_valid, particle_filter_valid, odometry_valid)
            
        # Publish fused pose
        self.publish_fused_pose()
        
    def weighted_fusion(self, scan_matcher_valid, particle_filter_valid, odometry_valid):
        """Perform weighted average fusion"""
        poses = []
        weights = []
        
        if scan_matcher_valid:
            poses.append(self.scan_matcher_pose)
            weights.append(self.scan_matcher_weight)
            
        if particle_filter_valid:
            poses.append(self.particle_filter_pose)
            weights.append(self.particle_filter_weight)
            
        if odometry_valid:
            poses.append(self.odometry_pose)
            weights.append(self.odometry_weight)
            
        if len(poses) == 0:
            return
            
        # Normalize weights
        weights = np.array(weights)
        weights = weights / np.sum(weights)
        
        # Calculate weighted average position
        avg_x = 0.0
        avg_y = 0.0
        sin_sum = 0.0
        cos_sum = 0.0
        
        for i, pose in enumerate(poses):
            weight = weights[i]
            
            avg_x += weight * pose.pose.position.x
            avg_y += weight * pose.pose.position.y
            
            # Convert quaternion to yaw
            quat = pose.pose.orientation
            yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                           1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
            
            sin_sum += weight * math.sin(yaw)
            cos_sum += weight * math.cos(yaw)
            
        # Calculate average orientation
        avg_yaw = math.atan2(sin_sum, cos_sum)
        
        # Update state
        self.state[0] = avg_x
        self.state[1] = avg_y
        self.state[2] = avg_yaw
        
        # Simple covariance estimate
        self.covariance = np.eye(6) * 0.1
        
    def kalman_fusion(self, scan_matcher_valid, particle_filter_valid, odometry_valid):
        """Perform Kalman filter fusion"""
        dt = 1.0 / self.publish_rate
        
        # Prediction step
        self.predict_state(dt)
        
        # Update step with available measurements
        if scan_matcher_valid:
            self.update_with_pose(self.scan_matcher_pose, measurement_noise=0.1)
            
        if particle_filter_valid:
            self.update_with_pose(self.particle_filter_pose, measurement_noise=0.2)
            
        if odometry_valid:
            self.update_with_pose(self.odometry_pose, measurement_noise=0.3)
            
    def predict_state(self, dt):
        """Kalman filter prediction step"""
        # State transition matrix (constant velocity model)
        F = np.eye(6)
        F[0, 3] = dt  # x += vx * dt
        F[1, 4] = dt  # y += vy * dt
        F[2, 5] = dt  # theta += vtheta * dt
        
        # Predict state
        self.state = F @ self.state
        
        # Process noise
        Q = np.eye(6) * 0.01
        Q[3:, 3:] *= 0.1  # Higher noise for velocities
        
        # Predict covariance
        self.covariance = F @ self.covariance @ F.T + Q
        
    def update_with_pose(self, pose_msg, measurement_noise=0.1):
        """Kalman filter update step with pose measurement"""
        # Extract pose
        x = pose_msg.pose.position.x
        y = pose_msg.pose.position.y
        
        quat = pose_msg.pose.orientation
        theta = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                          1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
        
        # Measurement vector
        z = np.array([x, y, theta])
        
        # Measurement matrix (observe position and orientation)
        H = np.zeros((3, 6))
        H[0, 0] = 1.0  # x
        H[1, 1] = 1.0  # y
        H[2, 2] = 1.0  # theta
        
        # Measurement noise
        R = np.eye(3) * measurement_noise
        
        # Innovation
        y_innovation = z - H @ self.state
        
        # Handle angle wrapping
        while y_innovation[2] > math.pi:
            y_innovation[2] -= 2 * math.pi
        while y_innovation[2] < -math.pi:
            y_innovation[2] += 2 * math.pi
            
        # Innovation covariance
        S = H @ self.covariance @ H.T + R
        
        # Kalman gain
        K = self.covariance @ H.T @ np.linalg.inv(S)
        
        # Update state
        self.state += K @ y_innovation
        
        # Update covariance
        I = np.eye(6)
        self.covariance = (I - K @ H) @ self.covariance
        
    def calculate_confidence(self):
        """Calculate localization confidence"""
        # Base confidence on covariance trace
        position_uncertainty = math.sqrt(self.covariance[0, 0] + self.covariance[1, 1])
        orientation_uncertainty = math.sqrt(self.covariance[2, 2])
        
        # Normalize to 0-1 range
        pos_confidence = max(0.0, 1.0 - (position_uncertainty / 1.0))  # 1m threshold
        orient_confidence = max(0.0, 1.0 - (orientation_uncertainty / (math.pi / 2)))  # 90 deg threshold
        
        return (pos_confidence + orient_confidence) / 2.0
        
    def publish_fused_pose(self):
        """Publish fused pose estimate"""
        pose_msg = PoseWithCovarianceStamped()
        pose_msg.header.stamp = self.get_clock().now().to_msg()
        pose_msg.header.frame_id = 'map'
        
        # Position
        pose_msg.pose.pose.position.x = self.state[0]
        pose_msg.pose.pose.position.y = self.state[1]
        pose_msg.pose.pose.position.z = 0.0
        
        # Orientation
        theta = self.state[2]
        pose_msg.pose.pose.orientation.x = 0.0
        pose_msg.pose.pose.orientation.y = 0.0
        pose_msg.pose.pose.orientation.z = math.sin(theta / 2.0)
        pose_msg.pose.pose.orientation.w = math.cos(theta / 2.0)
        
        # Covariance (6x6 matrix)
        covariance_6x6 = np.zeros((6, 6))
        covariance_6x6[:3, :3] = self.covariance[:3, :3]  # Position and orientation
        pose_msg.pose.covariance = covariance_6x6.flatten().tolist()
        
        self.fused_pose_pub.publish(pose_msg)
        
        # Publish confidence
        confidence = self.calculate_confidence()
        confidence_msg = Float32()
        confidence_msg.data = confidence
        self.confidence_pub.publish(confidence_msg)

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = PoseEstimator()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
