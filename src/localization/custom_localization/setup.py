from setuptools import setup

package_name = 'custom_localization'

setup(
    name=package_name,
    version='1.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/custom_localization.launch.py']),
        ('share/' + package_name + '/config', ['config/localization_params.yaml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='Custom localization using LiDAR and odometry for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'scan_matcher = custom_localization.scan_matcher:main',
            'particle_filter = custom_localization.particle_filter:main',
            'pose_estimator = custom_localization.pose_estimator:main',
        ],
    },
)
