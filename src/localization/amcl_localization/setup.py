from setuptools import setup

package_name = 'amcl_localization'

setup(
    name=package_name,
    version='1.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/amcl_localization.launch.py']),
        ('share/' + package_name + '/config', ['config/amcl_params.yaml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='AMCL-based localization for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'localization_monitor = amcl_localization.localization_monitor:main',
            'pose_initializer = amcl_localization.pose_initializer:main',
        ],
    },
)
