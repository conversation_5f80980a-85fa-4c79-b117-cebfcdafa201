amcl:
  ros__parameters:
    # Robot model parameters
    robot_model_type: "nav2_amcl::DifferentialMotionModel"
    
    # Frame parameters
    base_frame_id: "base_footprint"
    odom_frame_id: "odom"
    global_frame_id: "map"
    scan_topic: "scan"
    
    # Transform tolerance
    transform_tolerance: 0.1
    
    # Particle filter parameters
    max_particles: 2000
    min_particles: 500
    
    # Initial pose parameters
    set_initial_pose: false
    initial_pose:
      x: 0.0
      y: 0.0
      z: 0.0
      yaw: 0.0
    
    # Covariance parameters
    initial_cov_xx: 0.25
    initial_cov_yy: 0.25
    initial_cov_aa: 0.068
    
    # Update parameters
    update_min_d: 0.2      # Minimum translation before update
    update_min_a: 0.5      # Minimum rotation before update
    resample_interval: 1   # Number of filter updates before resampling
    
    # Laser model parameters
    laser_model_type: "likelihood_field"
    laser_likelihood_max_dist: 2.0
    laser_max_range: 12.0
    laser_min_range: 0.15
    
    # Likelihood field parameters
    laser_z_hit: 0.5
    laser_z_short: 0.05
    laser_z_max: 0.05
    laser_z_rand: 0.5
    laser_sigma_hit: 0.2
    
    # Motion model parameters (differential drive)
    alpha1: 0.2    # Rotation noise from rotation
    alpha2: 0.2    # Rotation noise from translation
    alpha3: 0.2    # Translation noise from translation
    alpha4: 0.2    # Translation noise from rotation
    alpha5: 0.2    # Translation noise from strafe (not used for diff drive)
    
    # Recovery parameters
    recovery_alpha_slow: 0.001
    recovery_alpha_fast: 0.1
    
    # Beam model parameters (if using beam model)
    laser_z_short: 0.05
    laser_z_max: 0.05
    laser_z_rand: 0.5
    laser_lambda_short: 0.1
    
    # Performance parameters
    do_beamskip: false
    beam_skip_distance: 0.5
    beam_skip_threshold: 0.3
    beam_skip_error_threshold: 0.9
    
    # Spatial sampling parameters
    pf_err: 0.05
    pf_z: 0.99
    
    # Temporal parameters
    save_pose_rate: 0.5
    
    # Always publish transform
    tf_broadcast: true

amcl_map_client:
  ros__parameters:
    use_sim_time: true

amcl_rclcpp_node:
  ros__parameters:
    use_sim_time: true

bt_navigator:
  ros__parameters:
    use_sim_time: true
