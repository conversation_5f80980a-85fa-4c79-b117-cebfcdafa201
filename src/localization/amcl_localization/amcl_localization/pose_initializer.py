#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseWithCovarianceStamped, PoseStamped
from std_srvs.srv import Empty
from std_msgs.msg import String
import math

class PoseInitializer(Node):
    def __init__(self):
        super().__init__('pose_initializer')
        
        # Parameters
        self.declare_parameter('initial_x', 0.0)
        self.declare_parameter('initial_y', 0.0)
        self.declare_parameter('initial_yaw', 0.0)
        self.declare_parameter('initial_cov_x', 0.25)
        self.declare_parameter('initial_cov_y', 0.25)
        self.declare_parameter('initial_cov_yaw', 0.068)
        self.declare_parameter('auto_initialize', True)
        
        self.initial_x = self.get_parameter('initial_x').value
        self.initial_y = self.get_parameter('initial_y').value
        self.initial_yaw = self.get_parameter('initial_yaw').value
        self.initial_cov_x = self.get_parameter('initial_cov_x').value
        self.initial_cov_y = self.get_parameter('initial_cov_y').value
        self.initial_cov_yaw = self.get_parameter('initial_cov_yaw').value
        self.auto_initialize = self.get_parameter('auto_initialize').value
        
        # State
        self.initialized = False
        
        # Publishers
        self.initial_pose_pub = self.create_publisher(
            PoseWithCovarianceStamped, '/initialpose', 10)
        self.status_pub = self.create_publisher(
            String, '/pose_initializer/status', 10)
        
        # Services
        self.initialize_srv = self.create_service(
            Empty, 'initialize_pose', self.initialize_pose_callback)
        
        # Timer for auto-initialization
        if self.auto_initialize:
            self.init_timer = self.create_timer(2.0, self.auto_initialize_pose)
        
        self.get_logger().info('Pose Initializer started')
        self.get_logger().info(f'Initial pose: ({self.initial_x}, {self.initial_y}, {self.initial_yaw})')
        
    def auto_initialize_pose(self):
        """Automatically initialize pose on startup"""
        if not self.initialized:
            self.publish_initial_pose()
            self.initialized = True
            self.init_timer.cancel()  # Stop auto-initialization timer
            
    def initialize_pose_callback(self, request, response):
        """Service callback to initialize pose"""
        self.publish_initial_pose()
        self.initialized = True
        self.get_logger().info('Pose initialized via service call')
        return response
        
    def publish_initial_pose(self):
        """Publish initial pose estimate"""
        pose_msg = PoseWithCovarianceStamped()
        
        # Header
        pose_msg.header.stamp = self.get_clock().now().to_msg()
        pose_msg.header.frame_id = 'map'
        
        # Position
        pose_msg.pose.pose.position.x = self.initial_x
        pose_msg.pose.pose.position.y = self.initial_y
        pose_msg.pose.pose.position.z = 0.0
        
        # Orientation (convert yaw to quaternion)
        yaw = self.initial_yaw
        pose_msg.pose.pose.orientation.x = 0.0
        pose_msg.pose.pose.orientation.y = 0.0
        pose_msg.pose.pose.orientation.z = math.sin(yaw / 2.0)
        pose_msg.pose.pose.orientation.w = math.cos(yaw / 2.0)
        
        # Covariance matrix (6x6)
        covariance = [0.0] * 36
        covariance[0] = self.initial_cov_x    # x-x
        covariance[7] = self.initial_cov_y    # y-y
        covariance[35] = self.initial_cov_yaw # yaw-yaw
        pose_msg.pose.covariance = covariance
        
        # Publish
        self.initial_pose_pub.publish(pose_msg)
        
        # Publish status
        status_msg = String()
        status_msg.data = f"Initialized at ({self.initial_x:.2f}, {self.initial_y:.2f}, {self.initial_yaw:.2f})"
        self.status_pub.publish(status_msg)
        
        self.get_logger().info(f'Published initial pose: ({self.initial_x}, {self.initial_y}, {self.initial_yaw})')

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = PoseInitializer()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
