#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseWithCovarianceStamped, PoseStamped, Twist
from nav_msgs.msg import OccupancyGrid
from std_msgs.msg import Float32, String
import numpy as np
import math
import time

class LocalizationMonitor(Node):
    def __init__(self):
        super().__init__('localization_monitor')
        
        # Parameters
        self.declare_parameter('confidence_threshold', 0.8)
        self.declare_parameter('covariance_threshold', 0.5)
        self.declare_parameter('update_rate', 10.0)
        self.declare_parameter('pose_timeout', 2.0)
        
        self.confidence_threshold = self.get_parameter('confidence_threshold').value
        self.covariance_threshold = self.get_parameter('covariance_threshold').value
        self.update_rate = self.get_parameter('update_rate').value
        self.pose_timeout = self.get_parameter('pose_timeout').value
        
        # State variables
        self.current_pose = None
        self.last_pose_time = None
        self.pose_history = []
        self.localization_quality = 0.0
        self.is_localized = False
        
        # Subscribers
        self.amcl_pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.amcl_pose_callback, 10)
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10)
        self.map_sub = self.create_subscription(
            OccupancyGrid, '/map', self.map_callback, 10)
        
        # Publishers
        self.localization_quality_pub = self.create_publisher(
            Float32, '/localization/quality', 10)
        self.localization_status_pub = self.create_publisher(
            String, '/localization/status', 10)
        self.filtered_pose_pub = self.create_publisher(
            PoseStamped, '/localization/filtered_pose', 10)
        
        # Timer for monitoring
        self.monitor_timer = self.create_timer(1.0 / self.update_rate, self.monitor_localization)
        
        self.get_logger().info('Localization Monitor initialized')
        
    def amcl_pose_callback(self, msg):
        """Process AMCL pose updates"""
        self.current_pose = msg
        self.last_pose_time = time.time()
        
        # Add to history (keep last 50 poses)
        self.pose_history.append(msg)
        if len(self.pose_history) > 50:
            self.pose_history.pop(0)
            
        # Calculate localization quality
        self.calculate_localization_quality()
        
        # Publish filtered pose
        self.publish_filtered_pose()
        
    def cmd_vel_callback(self, msg):
        """Monitor robot movement for localization validation"""
        # This can be used to validate localization during movement
        pass
        
    def map_callback(self, msg):
        """Store map for localization validation"""
        # Store map for future use in localization validation
        pass
        
    def calculate_localization_quality(self):
        """Calculate localization quality based on covariance and consistency"""
        if self.current_pose is None:
            self.localization_quality = 0.0
            return
            
        # Extract covariance matrix
        cov = np.array(self.current_pose.pose.covariance).reshape(6, 6)
        
        # Calculate position uncertainty (x, y)
        pos_uncertainty = math.sqrt(cov[0, 0] + cov[1, 1])
        
        # Calculate orientation uncertainty
        orient_uncertainty = math.sqrt(cov[5, 5])
        
        # Normalize uncertainties to quality score (0-1)
        pos_quality = max(0.0, 1.0 - (pos_uncertainty / self.covariance_threshold))
        orient_quality = max(0.0, 1.0 - (orient_uncertainty / (math.pi / 4)))
        
        # Calculate consistency quality based on pose history
        consistency_quality = self.calculate_consistency_quality()
        
        # Combined quality score
        self.localization_quality = (pos_quality * 0.4 + 
                                   orient_quality * 0.3 + 
                                   consistency_quality * 0.3)
        
        # Update localization status
        self.is_localized = self.localization_quality > self.confidence_threshold
        
    def calculate_consistency_quality(self):
        """Calculate quality based on pose consistency over time"""
        if len(self.pose_history) < 5:
            return 0.5  # Neutral score for insufficient data
            
        # Calculate pose variations in recent history
        recent_poses = self.pose_history[-10:]
        
        positions = []
        orientations = []
        
        for pose_msg in recent_poses:
            pose = pose_msg.pose.pose
            positions.append([pose.position.x, pose.position.y])
            
            # Convert quaternion to yaw
            quat = pose.orientation
            yaw = math.atan2(2.0 * (quat.w * quat.z + quat.x * quat.y),
                           1.0 - 2.0 * (quat.y * quat.y + quat.z * quat.z))
            orientations.append(yaw)
            
        # Calculate standard deviations
        positions = np.array(positions)
        pos_std = np.std(positions, axis=0)
        orient_std = np.std(orientations)
        
        # Convert to quality scores
        pos_consistency = max(0.0, 1.0 - (np.mean(pos_std) / 0.1))  # 10cm threshold
        orient_consistency = max(0.0, 1.0 - (orient_std / 0.1))     # ~6 degree threshold
        
        return (pos_consistency + orient_consistency) / 2.0
        
    def publish_filtered_pose(self):
        """Publish filtered pose estimate"""
        if self.current_pose is None:
            return
            
        filtered_pose = PoseStamped()
        filtered_pose.header = self.current_pose.header
        filtered_pose.pose = self.current_pose.pose.pose
        
        self.filtered_pose_pub.publish(filtered_pose)
        
    def monitor_localization(self):
        """Monitor localization status and publish diagnostics"""
        current_time = time.time()
        
        # Check for pose timeout
        pose_timeout = (self.last_pose_time is None or 
                       (current_time - self.last_pose_time) > self.pose_timeout)
        
        # Publish quality score
        quality_msg = Float32()
        quality_msg.data = self.localization_quality
        self.localization_quality_pub.publish(quality_msg)
        
        # Publish status message
        status_msg = String()
        
        if pose_timeout:
            status_msg.data = "TIMEOUT: No pose updates received"
            self.is_localized = False
        elif self.is_localized:
            status_msg.data = f"LOCALIZED: Quality {self.localization_quality:.2f}"
        else:
            status_msg.data = f"POOR_LOCALIZATION: Quality {self.localization_quality:.2f}"
            
        self.localization_status_pub.publish(status_msg)
        
        # Log status periodically
        if hasattr(self, '_last_log_time'):
            if current_time - self._last_log_time > 5.0:  # Log every 5 seconds
                self.log_status()
                self._last_log_time = current_time
        else:
            self._last_log_time = current_time
            
    def log_status(self):
        """Log current localization status"""
        if self.current_pose is None:
            self.get_logger().warn('No localization data available')
            return
            
        pose = self.current_pose.pose.pose
        cov = np.array(self.current_pose.pose.covariance).reshape(6, 6)
        
        pos_uncertainty = math.sqrt(cov[0, 0] + cov[1, 1])
        orient_uncertainty = math.sqrt(cov[5, 5])
        
        self.get_logger().info(
            f'Localization Status: '
            f'Quality={self.localization_quality:.2f}, '
            f'Pos=({pose.position.x:.2f}, {pose.position.y:.2f}), '
            f'PosUnc={pos_uncertainty:.3f}m, '
            f'OrientUnc={orient_uncertainty:.3f}rad, '
            f'Localized={self.is_localized}'
        )

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = LocalizationMonitor()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
