#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_amcl_localization = get_package_share_directory('amcl_localization')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(pkg_amcl_localization, 'config', 'amcl_params.yaml'),
        description='Full path to AMCL parameters file'
    )
    
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        default_value='',
        description='Full path to map file'
    )
    
    use_lifecycle_manager_arg = DeclareLaunchArgument(
        'use_lifecycle_manager',
        default_value='true',
        description='Enable lifecycle manager'
    )
    
    autostart_arg = DeclareLaunchArgument(
        'autostart',
        default_value='true',
        description='Automatically startup the nav2 stack'
    )
    
    use_pose_initializer_arg = DeclareLaunchArgument(
        'use_pose_initializer',
        default_value='true',
        description='Enable automatic pose initialization'
    )
    
    initial_pose_x_arg = DeclareLaunchArgument(
        'initial_pose_x',
        default_value='0.0',
        description='Initial pose X coordinate'
    )
    
    initial_pose_y_arg = DeclareLaunchArgument(
        'initial_pose_y',
        default_value='0.0',
        description='Initial pose Y coordinate'
    )
    
    initial_pose_yaw_arg = DeclareLaunchArgument(
        'initial_pose_yaw',
        default_value='0.0',
        description='Initial pose yaw angle'
    )
    
    # Map Server Node (if map file provided)
    map_server_node = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'yaml_filename': LaunchConfiguration('map_file')
        }],
        condition=IfCondition(LaunchConfiguration('map_file'))
    )
    
    # AMCL Node
    amcl_node = Node(
        package='nav2_amcl',
        executable='amcl',
        name='amcl',
        output='screen',
        parameters=[LaunchConfiguration('params_file')],
        remappings=[
            ('/tf', 'tf'),
            ('/tf_static', 'tf_static')
        ]
    )
    
    # Lifecycle Manager
    lifecycle_manager_node = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_localization',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'autostart': LaunchConfiguration('autostart'),
            'node_names': ['map_server', 'amcl']
        }],
        condition=IfCondition(LaunchConfiguration('use_lifecycle_manager'))
    )
    
    # Localization Monitor
    localization_monitor_node = Node(
        package='amcl_localization',
        executable='localization_monitor',
        name='localization_monitor',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'confidence_threshold': 0.8,
            'covariance_threshold': 0.5,
            'update_rate': 10.0,
            'pose_timeout': 2.0
        }]
    )
    
    # Pose Initializer
    pose_initializer_node = Node(
        package='amcl_localization',
        executable='pose_initializer',
        name='pose_initializer',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'initial_x': LaunchConfiguration('initial_pose_x'),
            'initial_y': LaunchConfiguration('initial_pose_y'),
            'initial_yaw': LaunchConfiguration('initial_pose_yaw'),
            'initial_cov_x': 0.25,
            'initial_cov_y': 0.25,
            'initial_cov_yaw': 0.068,
            'auto_initialize': True
        }],
        condition=IfCondition(LaunchConfiguration('use_pose_initializer'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        params_file_arg,
        map_file_arg,
        use_lifecycle_manager_arg,
        autostart_arg,
        use_pose_initializer_arg,
        initial_pose_x_arg,
        initial_pose_y_arg,
        initial_pose_yaw_arg,
        
        # Nodes
        map_server_node,
        amcl_node,
        lifecycle_manager_node,
        localization_monitor_node,
        pose_initializer_node,
    ])
