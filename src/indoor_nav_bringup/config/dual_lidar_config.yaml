# Dual LiDAR Configuration
# Configuration for robots with front and rear LiDAR sensors

dual_lidar_merger:
  ros__parameters:
    # Topics
    front_topic: "/scan_front"
    rear_topic: "/scan_rear"
    output_topic: "/scan"
    
    # Frame configuration
    frame_id: "laser_frame"
    
    # Merge settings
    merge_method: "closest"  # Options: closest, average, front_priority
    
    # Range settings
    max_range: 8.0
    min_range: 0.3
    angle_resolution: 360  # Number of rays in output scan
    
    # Quality settings
    use_sim_time: false

# Front LiDAR configuration (for real robot)
front_lidar_node:
  ros__parameters:
    # Hardware settings
    port: "/dev/ttyUSB0"  # Adjust for your front LiDAR
    baud_rate: 115200
    
    # Scan settings
    frame_id: "laser_front_frame"
    topic_name: "/scan_front"
    scan_frequency: 10.0
    
    # Range settings
    range_max: 8.0
    range_min: 0.3
    
    # Quality settings
    use_sim_time: false

# Rear LiDAR configuration (for real robot)
rear_lidar_node:
  ros__parameters:
    # Hardware settings
    port: "/dev/ttyUSB1"  # Adjust for your rear LiDAR
    baud_rate: 115200
    
    # Scan settings
    frame_id: "laser_rear_frame"
    topic_name: "/scan_rear"
    scan_frequency: 10.0
    
    # Range settings
    range_max: 8.0
    range_min: 0.3
    
    # Quality settings
    use_sim_time: false

# Transform configuration
# Static transforms for LiDAR positions relative to base_link
static_transforms:
  # Front LiDAR position (adjust for your robot)
  front_lidar:
    x: 0.15      # 15cm forward from center
    y: 0.0       # Centered
    z: 0.12      # 12cm above base
    roll: 0.0
    pitch: 0.0
    yaw: 0.0     # Facing forward
    
  # Rear LiDAR position (adjust for your robot)
  rear_lidar:
    x: -0.15     # 15cm backward from center
    y: 0.0       # Centered
    z: 0.12      # 12cm above base
    roll: 0.0
    pitch: 0.0
    yaw: 3.14159 # Facing backward (180 degrees)

# Navigation integration
navigation:
  # Which scan to use for navigation
  primary_scan_topic: "/scan"  # Use merged scan
  
  # Backup scan topics (if merger fails)
  backup_scan_topics:
    - "/scan_front"
    - "/scan_rear"
  
  # Safety settings
  obstacle_detection:
    use_both_lidars: true
    front_priority_zones:
      - [-0.785, 0.785]  # ±45 degrees front
    rear_priority_zones:
      - [2.356, -2.356]  # ±45 degrees rear

# Real robot hardware notes:
# 1. Connect front LiDAR to /dev/ttyUSB0
# 2. Connect rear LiDAR to /dev/ttyUSB1
# 3. Ensure both LiDAR have different device IDs if using same model
# 4. Adjust port names based on your system
# 5. Update transform values to match your robot's physical dimensions
