<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="indoor_robot">

  <!-- Properties -->
  <xacro:property name="base_width" value="0.3"/>
  <xacro:property name="base_length" value="0.3"/>
  <xacro:property name="base_height" value="0.1"/>
  <xacro:property name="wheel_radius" value="0.05"/>
  <xacro:property name="wheel_width" value="0.02"/>
  <xacro:property name="wheel_separation" value="0.32"/>

  <!-- Materials -->
  <material name="blue">
    <color rgba="0.0 0.0 1.0 1.0"/>
  </material>
  
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  
  <material name="green">
    <color rgba="0.0 1.0 0.0 1.0"/>
  </material>
  
  <material name="red">
    <color rgba="1.0 0.0 0.0 1.0"/>
  </material>

  <!-- Base Link -->
  <link name="base_link">
    <visual>
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="5.0"/>
      <!-- Keep center of mass at center - don't offset it! -->
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <!-- Simple inertia values that work -->
      <inertia ixx="0.0833" ixy="0.0" ixz="0.0"
               iyy="99999.0" iyz="0.0"
               izz="0.1667"/>
    </inertial>
  </link>

  <!-- Base Footprint -->
  <link name="base_footprint"/>
  
  <joint name="base_footprint_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="base_link"/>
    <origin xyz="0 0 ${wheel_radius}" rpy="0 0 0"/>
  </joint>

  <!-- Front LiDAR Link -->
  <link name="laser_front_frame">
    <visual>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0"
               iyy="0.001" iyz="0.0"
               izz="0.001"/>
    </inertial>
  </link>

  <!-- Front LiDAR Joint - Positioned at front of robot -->
  <joint name="laser_front_joint" type="fixed">
    <parent link="base_link"/>
    <child link="laser_front_frame"/>
    <origin xyz="${base_length/2 - 0.05} 0.0 ${base_height/2 + 0.08}" rpy="0.0 0.0 0.0"/>
  </joint>

  <!-- Rear LiDAR Link -->
  <link name="laser_rear_frame">
    <visual>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0"
               iyy="0.001" iyz="0.0"
               izz="0.001"/>
    </inertial>
  </link>

  <!-- Rear LiDAR Joint - Positioned at rear of robot, facing backward -->
  <joint name="laser_rear_joint" type="fixed">
    <parent link="base_link"/>
    <child link="laser_rear_frame"/>
    <origin xyz="${-base_length/2 + 0.05} 0.0 ${base_height/2 + 0.08}" rpy="0.0 0.0 ${pi}"/>
  </joint>

  <!-- Legacy laser_frame for backward compatibility -->
  <link name="laser_frame">
    <visual>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.05" length="0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0"
               iyy="0.001" iyz="0.0"
               izz="0.001"/>
    </inertial>
  </link>

  <!-- Legacy LiDAR Joint - Center position for merged scan -->
  <joint name="laser_joint" type="fixed">
    <parent link="base_link"/>
    <child link="laser_frame"/>
    <origin xyz="0.0 0.0 ${base_height/2 + 0.08}" rpy="0.0 0.0 0.0"/>
  </joint>

  <!-- Wheels -->
  <!-- Left Wheel -->
  <link name="left_wheel">
    <visual>
      <geometry>
        <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.00125" ixy="0.0" ixz="0.0"
               iyy="0.00125" iyz="0.0"
               izz="0.00125"/>
    </inertial>
  </link>

  <joint name="left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="left_wheel"/>
    <origin xyz="0.0 ${wheel_separation/2} ${-base_height/2}" rpy="-1.57 0.0 0.0"/>
    <axis xyz="0 0 1"/>
  </joint>

  <!-- Right Wheel -->
  <link name="right_wheel">
    <visual>
      <geometry>
        <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.00125" ixy="0.0" ixz="0.0"
               iyy="0.00125" iyz="0.0"
               izz="0.00125"/>
    </inertial>
  </link>

  <joint name="right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="right_wheel"/>
    <origin xyz="0.0 ${-wheel_separation/2} ${-base_height/2}" rpy="-1.57 0.0 0.0"/>
    <axis xyz="0 0 1"/>
  </joint>



  <!-- Ultrasonic Sensors -->
  <xacro:macro name="ultrasonic_sensor" params="name x y z yaw">
    <link name="ultrasonic_${name}">
      <visual>
        <geometry>
          <box size="0.02 0.02 0.01"/>
        </geometry>
        <material name="green"/>
      </visual>
      <collision>
        <geometry>
          <box size="0.02 0.02 0.01"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="0.01"/>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <inertia ixx="0.0001" ixy="0.0" ixz="0.0" 
                 iyy="0.0001" iyz="0.0" 
                 izz="0.0001"/>
      </inertial>
    </link>

    <joint name="ultrasonic_${name}_joint" type="fixed">
      <parent link="base_link"/>
      <child link="ultrasonic_${name}"/>
      <origin xyz="${x} ${y} ${z}" rpy="0.0 0.0 ${yaw}"/>
    </joint>
  </xacro:macro>

  <!-- Instantiate ultrasonic sensors -->
  <xacro:ultrasonic_sensor name="front" x="0.15" y="0.0" z="0.05" yaw="0.0"/>
  <xacro:ultrasonic_sensor name="left" x="0.0" y="0.15" z="0.05" yaw="1.57"/>
  <xacro:ultrasonic_sensor name="right" x="0.0" y="-0.15" z="0.05" yaw="-1.57"/>
  <xacro:ultrasonic_sensor name="back" x="-0.15" y="0.0" z="0.05" yaw="3.14"/>

  <!-- Gazebo Plugins -->

  <!-- Differential Drive Plugin -->
  <gazebo>
    <plugin name="differential_drive_controller" filename="libgazebo_ros_diff_drive.so">
      <ros>
        <namespace>/</namespace>
        <remapping>cmd_vel:=cmd_vel</remapping>
        <remapping>odom:=odom</remapping>
      </ros>

      <!-- Wheel Information -->
      <left_joint>left_wheel_joint</left_joint>
      <right_joint>right_wheel_joint</right_joint>
      <wheel_separation>${wheel_separation}</wheel_separation>
      <wheel_diameter>${2*wheel_radius}</wheel_diameter>

      <!-- Limits - Smoother acceleration to prevent leaning -->
      <max_wheel_torque>150</max_wheel_torque>
      <max_wheel_acceleration>6.0</max_wheel_acceleration>

      <!-- Output -->
      <publish_odom>true</publish_odom>
      <publish_odom_tf>true</publish_odom_tf>
      <publish_wheel_tf>true</publish_wheel_tf>

      <odometry_frame>odom</odometry_frame>
      <robot_base_frame>base_footprint</robot_base_frame>

      <!-- Update rate -->
      <update_rate>50</update_rate>

      <!-- Covariance -->
      <covariance_x>0.0001</covariance_x>
      <covariance_y>0.0001</covariance_y>
      <covariance_yaw>0.01</covariance_yaw>
    </plugin>
  </gazebo>

  <!-- Front LiDAR Plugin -->
  <gazebo reference="laser_front_frame">
    <sensor type="ray" name="rplidar_front">
      <pose>0 0 0 0 0 0</pose>
      <visualize>true</visualize>
      <update_rate>10</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>360</samples>
            <resolution>1</resolution>
            <min_angle>-3.14159</min_angle>
            <max_angle>3.14159</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.3</min>
          <max>8.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.01</stddev>
        </noise>
      </ray>
      <plugin name="gazebo_ros_laser_front" filename="libgazebo_ros_ray_sensor.so">
        <ros>
          <remapping>~/out:=scan_front</remapping>
        </ros>
        <output_type>sensor_msgs/LaserScan</output_type>
        <frame_name>laser_front_frame</frame_name>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Rear LiDAR Plugin -->
  <gazebo reference="laser_rear_frame">
    <sensor type="ray" name="rplidar_rear">
      <pose>0 0 0 0 0 0</pose>
      <visualize>true</visualize>
      <update_rate>10</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>360</samples>
            <resolution>1</resolution>
            <min_angle>-3.14159</min_angle>
            <max_angle>3.14159</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.3</min>
          <max>8.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.01</stddev>
        </noise>
      </ray>
      <plugin name="gazebo_ros_laser_rear" filename="libgazebo_ros_ray_sensor.so">
        <ros>
          <remapping>~/out:=scan_rear</remapping>
        </ros>
        <output_type>sensor_msgs/LaserScan</output_type>
        <frame_name>laser_rear_frame</frame_name>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Legacy LiDAR Plugin for merged scan -->
  <gazebo reference="laser_frame">
    <sensor type="ray" name="rplidar_merged">
      <pose>0 0 0 0 0 0</pose>
      <visualize>false</visualize>
      <update_rate>10</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>360</samples>
            <resolution>1</resolution>
            <min_angle>-3.14159</min_angle>
            <max_angle>3.14159</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.3</min>
          <max>8.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.01</stddev>
        </noise>
      </ray>
      <plugin name="gazebo_ros_laser_merged" filename="libgazebo_ros_ray_sensor.so">
        <ros>
          <remapping>~/out:=scan</remapping>
        </ros>
        <output_type>sensor_msgs/LaserScan</output_type>
        <frame_name>laser_frame</frame_name>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Gazebo materials -->
  <gazebo reference="base_link">
    <material>Gazebo/Blue</material>
  </gazebo>

  <gazebo reference="laser_frame">
    <material>Gazebo/Black</material>
  </gazebo>

  <gazebo reference="left_wheel">
    <material>Gazebo/Red</material>
    <mu1>1.0</mu1>
    <mu2>1.0</mu2>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <minDepth>0.001</minDepth>
    <maxVel>1.0</maxVel>
  </gazebo>

  <gazebo reference="right_wheel">
    <material>Gazebo/Red</material>
    <mu1>1.0</mu1>
    <mu2>1.0</mu2>
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <minDepth>0.001</minDepth>
    <maxVel>1.0</maxVel>
  </gazebo>

</robot>
