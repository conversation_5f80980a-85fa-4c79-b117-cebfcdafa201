<?xml version="1.0"?>
<robot name="indoor_robot_enhanced">
  
  <!-- Materials -->
  <material name="blue">
    <color rgba="0.2 0.4 0.8 1.0"/>
  </material>
  <material name="black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.2 0.8 0.2 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.2 0.2 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.5 0.0 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.7 0.7 0.7 1.0"/>
  </material>
  <material name="yellow">
    <color rgba="1.0 1.0 0.0 1.0"/>
  </material>
  
  <!-- Base Footprint -->
  <link name="base_footprint"/>

  <!-- Base Link - Main chassis -->
  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.35 0.25 0.08"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.35 0.25 0.08"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="8.0"/>
      <origin xyz="0 0 -0.02" rpy="0 0 0"/>
      <inertia ixx="0.15" ixy="0.0" ixz="0.0" iyy="0.15" iyz="0.0" izz="0.15"/>
    </inertial>
  </link>

  <joint name="base_footprint_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="base_link"/>
    <origin xyz="0 0 0.06" rpy="0 0 0"/>
  </joint>

  <!-- Fixed world base-->
  <!-- <joint name="base_world_joint" type="revolute">
    <parent>world</patent>
    <child link="base_footprint"/>
    <pose>0 0 0 0 0 0</pose>
    <axis>
      <xyz>0 0 1</xyz>
      <limit>
        <lower>-1000</lower>
        <upper>1000</upper>
      </limit>
    </axis>
  </joint> -->

  <!-- Top Platform -->
  <link name="top_platform">
    <visual>
      <geometry>
        <box size="0.30 0.20 0.02"/>
      </geometry>
      <material name="silver"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.30 0.20 0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.01" ixy="0.0" ixz="0.0" iyy="0.01" iyz="0.0" izz="0.01"/>
    </inertial>
  </link>
  
  <joint name="top_platform_joint" type="fixed">
    <parent link="base_link"/>
    <child link="top_platform"/>
    <origin xyz="0.0 0.0 0.05" rpy="0.0 0.0 0.0"/>
  </joint>
  
  <!-- LiDAR Link - Enhanced design -->
  <link name="laser_frame">
    <visual>
      <geometry>
        <cylinder radius="0.06" length="0.08"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.06" length="0.08"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.3"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <!-- LiDAR Joint -->
  <joint name="laser_joint" type="fixed">
    <parent link="top_platform"/>
    <child link="laser_frame"/>
    <origin xyz="0.0 0.0 0.05" rpy="0.0 0.0 0.0"/>
  </joint>
  
  <!-- LiDAR Lens -->
  <link name="laser_lens">
    <visual>
      <geometry>
        <cylinder radius="0.03" length="0.01"/>
      </geometry>
      <material name="yellow"/>
    </visual>
  </link>
  
  <joint name="laser_lens_joint" type="fixed">
    <parent link="laser_frame"/>
    <child link="laser_lens"/>
    <origin xyz="0.0 0.0 0.045" rpy="0.0 0.0 0.0"/>
  </joint>
  
  <!-- Ultrasonic Sensors - Enhanced design -->
  
  <!-- Front Ultrasonic -->
  <link name="ultrasonic_front">
    <visual>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="ultrasonic_front_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_front"/>
    <origin xyz="0.175 0.0 0.02" rpy="0.0 1.57 0.0"/>
  </joint>
  
  <!-- Left Ultrasonic -->
  <link name="ultrasonic_left">
    <visual>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="ultrasonic_left_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_left"/>
    <origin xyz="0.0 0.125 0.02" rpy="0.0 1.57 1.57"/>
  </joint>
  
  <!-- Right Ultrasonic -->
  <link name="ultrasonic_right">
    <visual>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="ultrasonic_right_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_right"/>
    <origin xyz="0.0 -0.125 0.02" rpy="0.0 1.57 -1.57"/>
  </joint>
  
  <!-- Back Ultrasonic -->
  <link name="ultrasonic_back">
    <visual>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="ultrasonic_back_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_back"/>
    <origin xyz="-0.175 0.0 0.02" rpy="0.0 1.57 3.14"/>
  </joint>
  
  <!-- Additional Ultrasonic Sensors -->
  
  <!-- Front Left Ultrasonic -->
  <link name="ultrasonic_front_left">
    <visual>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="ultrasonic_front_left_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_front_left"/>
    <origin xyz="0.12 0.09 0.02" rpy="0.0 1.57 0.785"/>
  </joint>
  
  <!-- Front Right Ultrasonic -->
  <link name="ultrasonic_front_right">
    <visual>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
      <material name="green"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.015" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="ultrasonic_front_right_joint" type="fixed">
    <parent link="base_link"/>
    <child link="ultrasonic_front_right"/>
    <origin xyz="0.12 -0.09 0.02" rpy="0.0 1.57 -0.785"/>
  </joint>
  
  <!-- Wheels - Enhanced design -->
  
  <!-- Left Wheel -->
  <link name="left_wheel">
    <visual>
      <geometry>
        <cylinder radius="0.06" length="0.03"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.06" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.02" ixy="0.0" ixz="0.0" iyy="0.02" iyz="0.0" izz="0.02"/>
    </inertial>
  </link>
  
  <joint name="left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="left_wheel"/>
    <origin xyz="0.0 0.14 -0.06" rpy="-1.57 0.0 0.0"/>
    <axis xyz="0 0 1"/>
  </joint>
  
  <!-- Right Wheel -->
  <link name="right_wheel">
    <visual>
      <geometry>
        <cylinder radius="0.06" length="0.03"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.06" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.02" ixy="0.0" ixz="0.0" iyy="0.02" iyz="0.0" izz="0.02"/>
    </inertial>
  </link>
  
  <joint name="right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="right_wheel"/>
    <origin xyz="0.0 -0.14 -0.06" rpy="-1.57 0.0 0.0"/>
    <axis xyz="0 0 1"/>
  </joint>
  
  <!-- Caster Wheel (Front) -->
  <link name="front_caster">
    <visual>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
      <material name="silver"/>
    </visual>
    <collision>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="front_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="front_caster"/>
    <origin xyz="0.12 0.0 -0.06" rpy="0.0 0.0 0.0"/>
  </joint>
  
  <!-- Caster Wheel (Back) -->
  <link name="back_caster">
    <visual>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
      <material name="silver"/>
    </visual>
    <collision>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
    </inertial>
  </link>
  
  <joint name="back_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="back_caster"/>
    <origin xyz="-0.12 0.0 -0.06" rpy="0.0 0.0 0.0"/>
  </joint>
  
</robot>
