from setuptools import setup
import os
from glob import glob

package_name = 'indoor_nav_bringup'

setup(
    name=package_name,
    version='1.0.0',
    packages=[],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        # Launch files
        ('share/' + package_name + '/launch', glob('launch/*.launch.py')),
        # Config files
        ('share/' + package_name + '/config', glob('config/*.yaml')),
        # URDF files
        ('share/' + package_name + '/urdf', glob('urdf/*')),
        # RViz files
        ('share/' + package_name + '/rviz', glob('rviz/*.rviz')),
        # World files
        ('share/' + package_name + '/worlds', glob('worlds/*')),
        # Map files
        ('share/' + package_name + '/maps', glob('maps/*')),
        # Scripts
        ('share/' + package_name + '/scripts', glob('scripts/*.py')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='Indoor Vehicle Team',
    maintainer_email='<EMAIL>',
    description='Bringup package for indoor autonomous vehicle',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'advanced_system_monitor = scripts.advanced_system_monitor:main',
        ],
    },
)
