#!/usr/bin/env python3

"""
Real Robot Dual LiDAR Launch File
Launch file for real robot with front and rear LiDAR sensors
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Package directories
    pkg_indoor_nav = get_package_share_directory('indoor_nav_bringup')
    
    # Configuration files
    dual_lidar_config = os.path.join(pkg_indoor_nav, 'config', 'dual_lidar_config.yaml')
    slam_params_file = os.path.join(pkg_indoor_nav, 'config', 'slam_params.yaml')
    
    # Launch arguments
    front_lidar_port_arg = DeclareLaunchArgument(
        'front_lidar_port',
        default_value='/dev/ttyUSB0',
        description='Serial port for front LiDAR'
    )
    
    rear_lidar_port_arg = DeclareLaunchArgument(
        'rear_lidar_port',
        default_value='/dev/ttyUSB1',
        description='Serial port for rear LiDAR'
    )
    
    use_slam_arg = DeclareLaunchArgument(
        'use_slam',
        default_value='true',
        description='Enable SLAM'
    )
    
    use_navigation_arg = DeclareLaunchArgument(
        'use_navigation',
        default_value='true',
        description='Enable navigation'
    )
    
    # Launch configurations
    front_lidar_port = LaunchConfiguration('front_lidar_port')
    rear_lidar_port = LaunchConfiguration('rear_lidar_port')
    use_slam = LaunchConfiguration('use_slam')
    use_navigation = LaunchConfiguration('use_navigation')
    
    # Robot description
    robot_state_publisher_node = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{
            'robot_description': open(os.path.join(pkg_indoor_nav, 'urdf', 'indoor_robot_enhanced.urdf')).read(),
            'use_sim_time': False
        }]
    )
    
    # Joint state publisher
    joint_state_publisher_node = Node(
        package='joint_state_publisher',
        executable='joint_state_publisher',
        name='joint_state_publisher',
        output='screen',
        parameters=[{'use_sim_time': False}]
    )
    
    # Front LiDAR node
    front_lidar_node = Node(
        package='lidar_node',
        executable='lidar_node',
        name='front_lidar_node',
        output='screen',
        parameters=[{
            'port': front_lidar_port,
            'baud_rate': 115200,
            'frame_id': 'laser_front_frame',
            'scan_frequency': 10.0,
            'range_max': 8.0,
            'range_min': 0.3,
            'use_sim_time': False
        }],
        remappings=[
            ('/scan', '/scan_front')
        ]
    )
    
    # Rear LiDAR node
    rear_lidar_node = Node(
        package='lidar_node',
        executable='lidar_node',
        name='rear_lidar_node',
        output='screen',
        parameters=[{
            'port': rear_lidar_port,
            'baud_rate': 115200,
            'frame_id': 'laser_rear_frame',
            'scan_frequency': 10.0,
            'range_max': 8.0,
            'range_min': 0.3,
            'use_sim_time': False
        }],
        remappings=[
            ('/scan', '/scan_rear')
        ]
    )
    
    # Dual LiDAR merger
    dual_lidar_merger_node = Node(
        package='sensor_fusion',
        executable='dual_lidar_merger',
        name='dual_lidar_merger',
        output='screen',
        parameters=[dual_lidar_config]
    )
    
    # Static transform publishers for LiDAR positions
    front_lidar_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='front_lidar_tf',
        arguments=['0.15', '0.0', '0.12', '0.0', '0.0', '0.0', 'base_link', 'laser_front_frame']
    )
    
    rear_lidar_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='rear_lidar_tf',
        arguments=['-0.15', '0.0', '0.12', '0.0', '0.0', '3.14159', 'base_link', 'laser_rear_frame']
    )
    
    # Merged LiDAR frame (for navigation)
    merged_lidar_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='merged_lidar_tf',
        arguments=['0.0', '0.0', '0.12', '0.0', '0.0', '0.0', 'base_link', 'laser_frame']
    )
    
    # SLAM Toolbox
    slam_toolbox_node = Node(
        package='slam_toolbox',
        executable='async_slam_toolbox_node',
        name='slam_toolbox',
        output='screen',
        parameters=[
            slam_params_file,
            {'use_sim_time': False}
        ],
        condition=IfCondition(use_slam)
    )
    
    # Navigation (obstacle avoidance)
    obstacle_avoidance_node = Node(
        package='indoor_nav_bringup',
        executable='run_obstacle_avoidance.sh',
        name='obstacle_avoidance',
        output='screen',
        parameters=[{
            'use_sim_time': False,
            'max_linear_speed': 0.3,
            'max_angular_speed': 1.0,
            'obstacle_distance_threshold': 0.8,
            'emergency_stop_distance': 0.2
        }],
        condition=IfCondition(use_navigation)
    )
    
    return LaunchDescription([
        # Launch arguments
        front_lidar_port_arg,
        rear_lidar_port_arg,
        use_slam_arg,
        use_navigation_arg,
        
        # Robot description
        robot_state_publisher_node,
        joint_state_publisher_node,
        
        # LiDAR nodes
        front_lidar_node,
        rear_lidar_node,
        dual_lidar_merger_node,
        
        # Transform publishers
        front_lidar_tf,
        rear_lidar_tf,
        merged_lidar_tf,
        
        # Navigation
        slam_toolbox_node,
        obstacle_avoidance_node,
    ])
