#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_indoor_nav_bringup = get_package_share_directory('indoor_nav_bringup')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    use_gazebo_arg = DeclareLaunchArgument(
        'use_gazebo',
        default_value='false',
        description='Launch with Gazebo simulation'
    )
    
    use_sensor_fusion_arg = DeclareLaunchArgument(
        'use_sensor_fusion',
        default_value='true',
        description='Enable sensor fusion'
    )
    
    use_mapping_arg = DeclareLaunchArgument(
        'use_mapping',
        default_value='true',
        description='Enable mapping'
    )
    
    use_slam_arg = DeclareLaunchArgument(
        'use_slam',
        default_value='false',
        description='Enable SLAM (mutually exclusive with static mapping)'
    )
    
    static_map_file_arg = DeclareLaunchArgument(
        'static_map_file',
        default_value='',
        description='Path to static map file'
    )
    
    # Include Gazebo simulation if requested
    gazebo_simulation = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('indoor_nav_bringup'),
                'launch',
                'gazebo_simulation.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_gazebo'))
    )
    
    # Include sensor fusion
    sensor_fusion_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('sensor_fusion'),
                'launch',
                'sensor_fusion.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_sensor_fusion'))
    )
    
    # Include mapping (without SLAM)
    mapping_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('mapping'),
                'launch',
                'mapping.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'static_map_file': LaunchConfiguration('static_map_file'),
            'use_sensor_fusion': 'false',  # Already launched above
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_mapping'))
    )
    
    # Include SLAM
    slam_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('mapping'),
                'launch',
                'slam.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_slam'))
    )
    
    # Sensor nodes (only when not using Gazebo)
    lidar_node = Node(
        package='lidar_node',
        executable='lidar_node',
        name='lidar_node',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'port': '/dev/ttyUSB0',
            'frame_id': 'laser_frame',
            'scan_frequency': 10.0
        }],
        condition=IfCondition('false')  # Disabled when using Gazebo
    )
    
    ultrasonic_array_node = Node(
        package='ultrasonic_array',
        executable='ultrasonic_array_node',
        name='ultrasonic_array_node',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'sensor_count': 4,
            'max_range': 4.0,
            'update_rate': 10.0
        }],
        condition=IfCondition('false')  # Disabled when using Gazebo
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        use_gazebo_arg,
        use_sensor_fusion_arg,
        use_mapping_arg,
        use_slam_arg,
        static_map_file_arg,
        
        # Conditional launches
        gazebo_simulation,
        sensor_fusion_launch,
        mapping_launch,
        slam_launch,
        
        # Sensor nodes (for real hardware)
        lidar_node,
        ultrasonic_array_node,
    ])
