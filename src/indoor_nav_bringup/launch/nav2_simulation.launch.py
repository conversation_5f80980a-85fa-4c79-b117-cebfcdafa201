#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, SetEnvironmentVariable
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Package directories
    pkg_indoor_nav_bringup = get_package_share_directory('indoor_nav_bringup')
    pkg_nav2_bringup = get_package_share_directory('nav2_bringup')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    map_file_arg = DeclareLaunchArgument(
        'map',
        default_value='',  # NO MAP FILE - use SLAM instead
        description='Full path to map file to load'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(pkg_indoor_nav_bringup, 'config', 'nav2_params.yaml'),
        description='Full path to param file to load'
    )
    
    autostart_arg = DeclareLaunchArgument(
        'autostart',
        default_value='true',
        description='Automatically startup the nav2 stack'
    )
    
    use_composition_arg = DeclareLaunchArgument(
        'use_composition',
        default_value='True',
        description='Use composed bringup if True'
    )
    
    use_respawn_arg = DeclareLaunchArgument(
        'use_respawn',
        default_value='False',
        description='Whether to respawn if a node crashes'
    )
    
    # Set environment variables
    stdout_linebuf_envvar = SetEnvironmentVariable(
        'RCUTILS_LOGGING_BUFFERED_STREAM', '1'
    )
    
    # Include Gazebo simulation
    gazebo_simulation = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('indoor_nav_bringup'),
                'launch',
                'gazebo_simulation.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items()
    )
    
    # Nav2 bringup
    nav2_bringup = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('nav2_bringup'),
                'launch',
                'bringup_launch.py'
            ])
        ]),
        launch_arguments={
            'map': LaunchConfiguration('map'),
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'params_file': LaunchConfiguration('params_file'),
            'autostart': LaunchConfiguration('autostart'),
            'use_composition': LaunchConfiguration('use_composition'),
            'use_respawn': LaunchConfiguration('use_respawn'),
        }.items()
    )
    
    # Indoor navigation nodes
    navigation_manager_node = Node(
        package='indoor_navigation',
        executable='navigation_manager',
        name='navigation_manager',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'max_linear_speed': 0.5,
            'max_angular_speed': 1.0,
            'goal_tolerance': 0.2,
            'obstacle_threshold': 0.5,
            'emergency_threshold': 0.3,
        }]
    )
    
    waypoint_manager_node = Node(
        package='indoor_navigation',
        executable='waypoint_manager',
        name='waypoint_manager',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'waypoints_file': 'waypoints/saved_waypoints.json',
            'auto_save': True,
            'position_tolerance': 0.1,
        }]
    )
    
    # Web interface
    web_server_node = Node(
        package='web_interface',
        executable='web_server',
        name='web_server',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'port': 8080,
            'host': '0.0.0.0',
        }]
    )
    
    return LaunchDescription([
        # Set env var to print messages immediately to stdout
        stdout_linebuf_envvar,
        
        # Launch arguments
        use_sim_time_arg,
        map_file_arg,
        params_file_arg,
        autostart_arg,
        use_composition_arg,
        use_respawn_arg,
        
        # Launch Gazebo simulation
        gazebo_simulation,
        
        # Launch Nav2
        nav2_bringup,
        
        # Launch indoor navigation nodes
        navigation_manager_node,
        waypoint_manager_node,
        web_server_node,
    ])
