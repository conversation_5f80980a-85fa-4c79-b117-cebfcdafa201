#!/usr/bin/env python3

"""
Simplified Nav2 Launch for Real Robot
Uses only available packages without requiring additional installations
Compatible with both Gazebo simulation and real hardware
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, SetEnvironmentVariable
from launch.conditions import IfCondition, UnlessCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # Package directories
    pkg_indoor_nav = get_package_share_directory('indoor_nav_bringup')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time if true'
    )
    
    use_gazebo_arg = DeclareLaunchArgument(
        'use_gazebo',
        default_value='true',
        description='Launch Gazebo simulation if true'
    )
    
    use_rviz_arg = DeclareLaunchArgument(
        'use_rviz',
        default_value='false',  # Disabled by default to avoid issues
        description='Launch RViz if true'
    )

    use_dual_lidar_arg = DeclareLaunchArgument(
        'use_dual_lidar',
        default_value='true',
        description='Use dual LiDAR merger for front and rear sensors'
    )
    
    # Configuration files
    slam_params_file = os.path.join(pkg_indoor_nav, 'config', 'slam_params.yaml')
    
    # Launch configurations
    use_sim_time = LaunchConfiguration('use_sim_time')
    use_gazebo = LaunchConfiguration('use_gazebo')
    use_rviz = LaunchConfiguration('use_rviz')
    use_dual_lidar = LaunchConfiguration('use_dual_lidar')
    
    # Set environment variables
    set_env_vars = [
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),
    ]
    
    # Gazebo simulation (only if use_gazebo=true)
    gazebo_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('indoor_nav_bringup'),
                'launch',
                'gazebo_simulation.launch.py'
            ])
        ]),
        condition=IfCondition(use_gazebo),
        launch_arguments={
            'use_sim_time': use_sim_time,
        }.items()
    )

    # Dual LiDAR merger node (when enabled)
    dual_lidar_merger_node = Node(
        package='sensor_fusion',
        executable='dual_lidar_merger',
        name='dual_lidar_merger',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
            'front_topic': '/scan_front',
            'rear_topic': '/scan_rear',
            'output_topic': '/scan',
            'merge_method': 'closest',
            'frame_id': 'laser_frame',
            'max_range': 8.0,
            'min_range': 0.3,
            'angle_resolution': 360
        }],
        condition=IfCondition(use_dual_lidar)
    )
    
    # SLAM Toolbox
    slam_toolbox_node = Node(
        package='slam_toolbox',
        executable='async_slam_toolbox_node',
        name='slam_toolbox',
        output='screen',
        parameters=[
            slam_params_file,
            {'use_sim_time': use_sim_time}
        ],
        remappings=[
            ('/scan', '/scan'),
            ('/tf', '/tf'),
            ('/tf_static', '/tf_static'),
        ]
    )
    
    # Map Server (using available nav2_map_server)
    map_server_node = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[
            {'yaml_filename': os.path.join(pkg_indoor_nav, 'maps', 'indoor_map.yaml')},
            {'use_sim_time': use_sim_time}
        ]
    )
    
    # Simple Obstacle Avoidance Node (custom implementation)
    obstacle_avoidance_node = Node(
        package='indoor_nav_bringup',
        executable='run_obstacle_avoidance.sh',
        name='simple_obstacle_avoidance',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'max_linear_speed': 0.26},
            {'max_angular_speed': 1.0},
            {'obstacle_distance_threshold': 0.6},
            {'emergency_stop_distance': 0.15},
            {'goal_tolerance': 0.1},
            {'robot_length': 0.5},
            {'robot_width': 0.5}
        ],
        remappings=[
            ('/scan', '/scan'),
            ('/cmd_vel', '/cmd_vel'),
            ('/odom', '/odom'),
            ('/goal_pose', '/goal_pose')
        ]
    )
    
    # RViz (disabled due to Qt compatibility issues)
    # rviz_node = Node(
    #     package='rviz2',
    #     executable='rviz2',
    #     name='rviz2',
    #     output='screen',
    #     arguments=['-d', os.path.join(pkg_indoor_nav, 'rviz', 'indoor_navigation.rviz')],
    #     condition=IfCondition(use_rviz),
    #     parameters=[{'use_sim_time': use_sim_time}]
    # )
    
    return LaunchDescription(
        set_env_vars + [
            use_sim_time_arg,
            use_gazebo_arg,
            use_rviz_arg,
            use_dual_lidar_arg,

            # Launch nodes
            gazebo_launch,
            dual_lidar_merger_node,
            slam_toolbox_node,
            map_server_node,
            obstacle_avoidance_node,
            # rviz_node,  # Disabled due to Qt issues
        ]
    )
