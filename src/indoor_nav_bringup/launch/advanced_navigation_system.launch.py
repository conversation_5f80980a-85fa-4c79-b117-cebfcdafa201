#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, GroupAction
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    """
    Launch file for Phase 2: Advanced Features
    - Enhanced SLAM with real-time mapping
    - Advanced path planning with multiple algorithms
    """
    
    # Package directories
    pkg_bringup = get_package_share_directory('indoor_nav_bringup')
    pkg_mapping = get_package_share_directory('mapping')
    pkg_path_planning = get_package_share_directory('path_planning')
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    slam_enabled_arg = DeclareLaunchArgument(
        'slam_enabled',
        default_value='true',
        description='Enable SLAM'
    )
    
    planning_algorithm_arg = DeclareLaunchArgument(
        'planning_algorithm',
        default_value='astar',
        description='Path planning algorithm: astar, dijkstra, rrt, rrt_star'
    )
    
    advanced_features_arg = DeclareLaunchArgument(
        'advanced_features',
        default_value='true',
        description='Enable advanced features'
    )
    
    rviz_enabled_arg = DeclareLaunchArgument(
        'rviz_enabled',
        default_value='true',
        description='Launch RViz for visualization'
    )
    
    # Include base system (robot, sensors, etc.)
    base_system_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(pkg_bringup, 'launch', 'gazebo_simulation.launch.py')
        ]),
        launch_arguments=[
            ('use_sim_time', LaunchConfiguration('use_sim_time')),
            ('gui', 'true')
        ]
    )
    
    # Enhanced SLAM System
    slam_group = GroupAction([
        # SLAM Toolbox
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([
                os.path.join(pkg_mapping, 'launch', 'slam.launch.py')
            ]),
            launch_arguments=[
                ('use_sim_time', LaunchConfiguration('use_sim_time')),
                ('slam_mode', 'mapping')
            ],
            condition=IfCondition(LaunchConfiguration('slam_enabled'))
        ),
        
        # Real-time SLAM Manager
        Node(
            package='mapping',
            executable='realtime_slam_manager',
            name='realtime_slam_manager',
            output='screen',
            parameters=[{
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'map_update_rate': 5.0,
                'pose_update_rate': 20.0,
                'quality_threshold': 0.7,
                'auto_save_interval': 60.0,
                'loop_closure_enabled': True,
                'dynamic_obstacles_enabled': True
            }],
            condition=IfCondition(LaunchConfiguration('advanced_features'))
        )
    ])
    
    # Advanced Path Planning System
    planning_group = GroupAction([
        # Advanced Global Planner
        Node(
            package='path_planning',
            executable='advanced_global_planner',
            name='advanced_global_planner',
            output='screen',
            parameters=[{
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'planning_algorithm': LaunchConfiguration('planning_algorithm'),
                'heuristic_weight': 1.2,
                'diagonal_movement': True,
                'smoothing_enabled': True,
                'path_resolution': 0.1,
                'obstacle_inflation': 0.25,
                'max_planning_time': 5.0,
                'replanning_threshold': 1.0,
                'dynamic_replanning': True,
                'path_optimization': True,
                # RRT parameters
                'rrt_max_iterations': 5000,
                'rrt_step_size': 0.5,
                'rrt_goal_bias': 0.1,
                'rrt_star_radius': 1.0
            }],
            condition=IfCondition(LaunchConfiguration('advanced_features'))
        ),
        
        # Include standard path planning as fallback
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([
                os.path.join(pkg_path_planning, 'launch', 'path_planning.launch.py')
            ]),
            launch_arguments=[
                ('use_sim_time', LaunchConfiguration('use_sim_time'))
            ]
        )
    ])
    
    # Navigation Stack Integration
    nav2_group = GroupAction([
        # Nav2 Bringup (for local planning and control)
        Node(
            package='nav2_controller',
            executable='controller_server',
            name='controller_server',
            output='screen',
            parameters=[{
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'controller_frequency': 20.0,
                'min_x_velocity_threshold': 0.001,
                'min_y_velocity_threshold': 0.5,
                'min_theta_velocity_threshold': 0.001,
                'failure_tolerance': 0.3,
                'progress_checker_plugin': 'nav2_controller::SimpleProgressChecker',
                'goal_checker_plugins': ['general_goal_checker'],
                'controller_plugins': ['FollowPath'],
                'general_goal_checker': {
                    'stateful': True,
                    'plugin': 'nav2_controller::SimpleGoalChecker',
                    'xy_goal_tolerance': 0.25,
                    'yaw_goal_tolerance': 0.25
                },
                'FollowPath': {
                    'plugin': 'nav2_controller::DWBLocalPlanner',
                    'debug_trajectory_details': True,
                    'min_vel_x': 0.0,
                    'min_vel_y': 0.0,
                    'max_vel_x': 0.26,
                    'max_vel_y': 0.0,
                    'max_vel_theta': 1.0,
                    'min_speed_xy': 0.0,
                    'max_speed_xy': 0.26,
                    'min_speed_theta': 0.0,
                    'acc_lim_x': 2.5,
                    'acc_lim_y': 0.0,
                    'acc_lim_theta': 3.2,
                    'decel_lim_x': -2.5,
                    'decel_lim_y': 0.0,
                    'decel_lim_theta': -3.2,
                    'vx_samples': 20,
                    'vy_samples': 5,
                    'vtheta_samples': 20,
                    'sim_time': 1.7,
                    'linear_granularity': 0.05,
                    'angular_granularity': 0.025,
                    'transform_tolerance': 0.2,
                    'xy_goal_tolerance': 0.25,
                    'trans_stopped_velocity': 0.25,
                    'short_circuit_trajectory_evaluation': True,
                    'stateful': True,
                    'critics': ['RotateToGoal', 'Oscillation', 'BaseObstacle', 'GoalAlign', 'PathAlign', 'PathDist', 'GoalDist'],
                    'BaseObstacle.scale': 0.02,
                    'PathAlign.scale': 32.0,
                    'PathAlign.forward_point_distance': 0.1,
                    'GoalAlign.scale': 24.0,
                    'GoalAlign.forward_point_distance': 0.1,
                    'PathDist.scale': 32.0,
                    'GoalDist.scale': 24.0,
                    'RotateToGoal.scale': 32.0,
                    'RotateToGoal.slowing_factor': 5.0,
                    'RotateToGoal.lookahead_time': -1.0
                }
            }]
        ),
        
        # Lifecycle Manager
        Node(
            package='nav2_lifecycle_manager',
            executable='lifecycle_manager',
            name='lifecycle_manager_navigation',
            output='screen',
            parameters=[{
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'autostart': True,
                'node_names': ['controller_server']
            }]
        )
    ])
    
    # Visualization
    rviz_config = os.path.join(pkg_bringup, 'rviz', 'advanced_navigation.rviz')
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        output='screen',
        arguments=['-d', rviz_config],
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }],
        condition=IfCondition(LaunchConfiguration('rviz_enabled'))
    )
    
    # System Monitor (optional - can be launched separately)
    # system_monitor_node = Node(
    #     package='indoor_nav_bringup',
    #     executable='python3',
    #     arguments=[os.path.join(pkg_bringup, 'scripts', 'advanced_system_monitor.py')],
    #     name='advanced_system_monitor',
    #     output='screen',
    #     parameters=[{
    #         'use_sim_time': LaunchConfiguration('use_sim_time'),
    #         'monitor_rate': 2.0,
    #         'slam_enabled': LaunchConfiguration('slam_enabled'),
    #         'advanced_features': LaunchConfiguration('advanced_features')
    #     }],
    #     condition=IfCondition(LaunchConfiguration('advanced_features'))
    # )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        slam_enabled_arg,
        planning_algorithm_arg,
        advanced_features_arg,
        rviz_enabled_arg,
        
        # System components
        base_system_launch,
        slam_group,
        planning_group,
        nav2_group,
        
        # Visualization
        rviz_node,
        # system_monitor_node,  # Optional - launch separately if needed
    ])
