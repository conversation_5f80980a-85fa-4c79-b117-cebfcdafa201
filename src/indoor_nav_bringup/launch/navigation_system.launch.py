#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    
    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )
    
    use_gazebo_arg = DeclareLaunchArgument(
        'use_gazebo',
        default_value='true',
        description='Launch with Gazebo simulation'
    )
    
    use_perception_arg = DeclareLaunchArgument(
        'use_perception',
        default_value='true',
        description='Enable perception system'
    )
    
    use_localization_arg = DeclareLaunchArgument(
        'use_localization',
        default_value='true',
        description='Enable localization system'
    )
    
    use_path_planning_arg = DeclareLaunchArgument(
        'use_path_planning',
        default_value='true',
        description='Enable path planning system'
    )
    
    use_decision_making_arg = DeclareLaunchArgument(
        'use_decision_making',
        default_value='true',
        description='Enable decision making system'
    )
    
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        default_value='',
        description='Path to map file for localization'
    )
    
    # Include Gazebo simulation
    gazebo_simulation = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('indoor_nav_bringup'),
                'launch',
                'gazebo_simulation.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_gazebo'))
    )
    
    # Include perception system
    perception_system = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('indoor_nav_bringup'),
                'launch',
                'test_perception_working.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_perception'))
    )
    
    # Include localization system
    localization_system = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('indoor_nav_bringup'),
                'launch',
                'test_localization_working.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'map_file': LaunchConfiguration('map_file'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_localization'))
    )
    
    # Include path planning system
    path_planning_system = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('path_planning'),
                'launch',
                'path_planning.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_path_planning'))
    )
    
    # Include decision making system
    decision_making_system = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('decision_making'),
                'launch',
                'decision_making.launch.py'
            ])
        ]),
        launch_arguments={
            'use_sim_time': LaunchConfiguration('use_sim_time'),
        }.items(),
        condition=IfCondition(LaunchConfiguration('use_decision_making'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        use_gazebo_arg,
        use_perception_arg,
        use_localization_arg,
        use_path_planning_arg,
        use_decision_making_arg,
        map_file_arg,
        
        # System launches
        gazebo_simulation,
        perception_system,
        localization_system,
        path_planning_system,
        decision_making_system,
    ])
