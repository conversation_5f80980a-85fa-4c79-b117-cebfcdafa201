#!/usr/bin/env python3

"""
Simple Obstacle Avoidance for Real Robot
Production-ready obstacle avoidance that works on both simulation and real hardware
Uses LiDAR data for intelligent navigation with goal-seeking behavior
"""

import rclpy
import tf2_ros
from tf2_geometry_msgs import PointStamped
from rclpy.node import Node
from sensor_msgs.msg import LaserScan
from geometry_msgs.msg import Twist, PoseStamped, PoseWithCovarianceStamped
from nav_msgs.msg import Odometry
import math
import numpy as np
from typing import Optional, Tuple

class SimpleObstacleAvoidance(Node):
    def __init__(self):
        super().__init__('simple_obstacle_avoidance')

        self.pose_buffer =[]
        self.buffer_size = 10
        
        # Parameters (check if already declared to avoid conflicts)
        try:
            self.declare_parameter('max_linear_speed', 0.26)
        except:
            pass
        try:
            self.declare_parameter('max_angular_speed', 1.0)
        except:
            pass
        try:
            self.declare_parameter('obstacle_distance_threshold', 0.6)
        except:
            pass
        try:
            self.declare_parameter('robot_length', 0.5)
        except:
            pass
        try:
            self.declare_parameter('robot_width', 0.5)
        except:
            pass
        try:
            self.declare_parameter('goal_tolerance', 0.1)
        except:
            pass
        try:
            self.declare_parameter('emergency_stop_distance', 0.15)
        except:
            pass
        try:
            self.declare_parameter('use_sim_time', True)
        except:
            pass
        
        self.max_linear_speed = self.get_parameter('max_linear_speed').value
        self.max_angular_speed = self.get_parameter('max_angular_speed').value
        self.obstacle_threshold = self.get_parameter('obstacle_distance_threshold').value
        self.goal_tolerance = self.get_parameter('goal_tolerance').value
        self.emergency_stop_distance = self.get_parameter('emergency_stop_distance').value
        self.robot_length = self.get_parameter('robot_length').value
        self.robot_width = self.get_parameter('robot_width').value
        
        # State variables
        self.current_pose: Optional[Tuple[float, float, float]] = None  # x, y, yaw
        self.goal_pose: Optional[Tuple[float, float]] = None  # x, y
        self.laser_data: Optional[LaserScan] = None
        self.is_goal_reached = False
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        
        # Subscribers
        self.laser_sub = self.create_subscription(
            LaserScan, '/scan', self.laser_callback, 10)
        self.odom_sub = self.create_subscription(
            Odometry, '/odom', self.odom_callback, 10)
        self.goal_sub = self.create_subscription(
            PoseStamped, '/goal_pose', self.goal_callback, 10)
        
        # Timer for control loop
        self.control_timer = self.create_timer(0.1, self.control_loop)  # 10 Hz
        
        self.get_logger().info("🤖 Simple Obstacle Avoidance Node Started")
        self.get_logger().info(f"📊 Parameters:")
        self.get_logger().info(f"   Max Linear Speed: {self.max_linear_speed} m/s")
        self.get_logger().info(f"   Max Angular Speed: {self.max_angular_speed} rad/s")
        self.get_logger().info(f"   Obstacle Threshold: {self.obstacle_threshold} m")
        self.get_logger().info(f"   Emergency Stop Distance: {self.emergency_stop_distance} m")
        self.get_logger().info(f"   Goal Tolerance: {self.goal_tolerance} m")

        #variables to save previous points
        self.last_pose: Optional[Tuple[float, float, float]] = None
        self.teleport_threshold = 1.0  # meters

        #buffer, listener
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)

        #detect obstacle threshold
        self.effective_obstacle_threshold = self.obstacle_threshold + max(self.robot_length/2, self.robot_width/2)
        self.get_logger().info(f"Effective Obstacle Threshold: {self.effective_obstacle_threshold} m due to robot size")

    def check_tf_teleport(self):
        """Check teleport by TF"""
        try:
            trans = self.tf_buffer.lookup_transform('map', 'base_footprint', rclpy.time.Time())
            x,y = trans.transform.translation.x, trans.transform.translation.y
            if self.last_tf_pose is not None:
                last_x, last_y = self.last_tf_pose
                distance = math.sqrt((x-last_x)**2 + (y-last_y)**2)
                if (distance > self.teleport_threshold and
                    math.sqrt(x**2 + y**2) < 0.1):
                    self.get_logger().warn(f" Teleport detected! Robot jump from ({last_x:.2f}, {last_y:.2f}) to ({x:.2f}, {y:.2f})")
                    cmd = Twist()
                    self.cmd_vel_pub.publish(cmd)
            self.last_tf_pose = (x,y)
        except (tf2_ros.LookupException, tf2_ros.ConnectivityException, tf2_ros.ExtrapolationException):
            pass
    
    def laser_callback(self, msg: LaserScan):
        """Process LiDAR data for obstacle detection"""
        self.laser_data = msg
    
    def odom_callback(self, msg: Odometry):
        """Update current robot pose from odometry"""
        try:
            position = msg.pose.pose.position
            orientation = msg.pose.pose.orientation
            
            # Convert quaternion to yaw
            yaw = math.atan2(
                2.0 * (orientation.w * orientation.z + orientation.x * orientation.y),
                1.0 - 2.0 * (orientation.y * orientation.y + orientation.z * orientation.z)
            )
            
            current_pose = (position.x, position.y, yaw)

            if any(not np.isfinite(x) for x in current_pose):
                self.get_logger.warm(" Invalid pose data detected, skipping update")
                return
            if self.last_pose is not None:
                self.pose_buffer.append(current_pose)
                if len(self.pose_buffer) > self.buffer_size:
                    self.pose_buffer.pop(0)
                last_x, last_y, _ = self.last_pose
                current_x, current_y, _ = current_pose

                dx = current_x - last_x
                dy = current_y - last_y
                if abs(dx) > 1000 or abs(dy) > 1000:
                    self.get_logger().warn(f" Suspected teleport or invalid data : dx={dx:.2f}, dy={dy:.2f}")
                    self.current_pose = self.last_pose
                    cmd = Twist()
                    self.cmd_vel_pub.publish(cmd)
                else:
                    distance = math.sqrt(dx**2 + dy**2)
                    
                    if (distance > self.teleport_threshold and math.sqrt(current_x**2 + current_y**2) < 0.1):
                        self.get_logger().warn(f" Teleport detected! Robot jump from ({last_x:.2f}, {last_y:.2f}) to ({current_x:.2f}, {current_y:.2f})")
                        self.current_pose = self.last_pose
                        cmd = Twist()
                        self.cmd_vel_pub.publish(cmd)
                    else:
                        self.current_pose = current_pose
            self.last_pose = current_pose
        except Exception as e:
            self.get_logger().error(f"Error in odom_callback: {e}")
            self.current_pose = self.last_pose if self.last_pose is not None else (0.0, 0.0, 0.0)
            cmd = Twist()
            self.cmd_vel_pub.publish(cmd)
    
    def goal_callback(self, msg: PoseStamped):
        """Set new navigation goal"""
        goal_x = msg.pose.position.x
        goal_y = msg.pose.position.y
        self.goal_pose = (goal_x, goal_y)
        self.is_goal_reached = False
        
        self.get_logger().info(f"🎯 New Goal Set: ({goal_x:.2f}, {goal_y:.2f})")
    
    def detect_obstacles(self) -> Tuple[bool, float, float]:
        """
        Detect obstacles using LiDAR data
        Returns: (obstacle_detected, min_distance, obstacle_angle)
        """
        if self.laser_data is None:
            return False, float('inf'), 0.0
        
        ranges = np.array(self.laser_data.ranges)
        angles = np.linspace(
            self.laser_data.angle_min,
            self.laser_data.angle_max,
            len(ranges)
        )
        
        # Filter out invalid readings
        valid_mask = np.isfinite(ranges) & (ranges > self.laser_data.range_min) & (ranges < self.laser_data.range_max)
        valid_ranges = ranges[valid_mask]
        valid_angles = angles[valid_mask]
        
        if len(valid_ranges) == 0:
            return False, float('inf'), 0.0
        
        # Find closest obstacle
        min_distance = np.min(valid_ranges)
        min_idx = np.argmin(valid_ranges)
        obstacle_angle = valid_angles[min_idx]
        
        obstacle_detected = min_distance < self.effective_obstacle_threshold

        return obstacle_detected, min_distance, obstacle_angle
    
    def calculate_goal_direction(self) -> Optional[float]:
        """Calculate direction to goal relative to robot"""
        if self.current_pose is None or self.goal_pose is None:
            return None
        
        robot_x, robot_y, robot_yaw = self.current_pose
        goal_x, goal_y = self.goal_pose
        
        # Calculate angle to goal
        dx = goal_x - robot_x
        dy = goal_y - robot_y
        goal_angle = math.atan2(dy, dx)
        
        # Relative angle to goal
        relative_angle = goal_angle - robot_yaw
        
        # Normalize angle to [-pi, pi]
        while relative_angle > math.pi:
            relative_angle -= 2 * math.pi
        while relative_angle < -math.pi:
            relative_angle += 2 * math.pi
        
        return relative_angle
    
    def is_goal_reached_check(self) -> bool:
        """Check if robot has reached the goal"""
        if self.current_pose is None or self.goal_pose is None:
            return False
        
        robot_x, robot_y, _ = self.current_pose
        goal_x, goal_y = self.goal_pose
        
        distance = math.sqrt((goal_x - robot_x)**2 + (goal_y - robot_y)**2)
        return distance < self.goal_tolerance
    
    def control_loop(self):
        """Main control loop for obstacle avoidance and navigation"""
        if self.current_pose is None:
            return
        
        cmd = Twist()
        
        # Check if goal is reached
        if self.goal_pose is not None and self.is_goal_reached_check():
            if not self.is_goal_reached:
                self.get_logger().info("🎉 Goal Reached!")
                self.is_goal_reached = True
            # Stop the robot
            self.cmd_vel_pub.publish(cmd)
            return
        
        # Detect obstacles
        obstacle_detected, min_distance, obstacle_angle = self.detect_obstacles()
        
        # Calculate direction to goal
        goal_direction = self.calculate_goal_direction()
        
        if obstacle_detected:
            # OBSTACLE AVOIDANCE MODE
            self.get_logger().info(f"🚧 Obstacle detected at {min_distance:.2f}m, angle {math.degrees(obstacle_angle):.1f}°")

            # Check for emergency stop
            if min_distance < self.emergency_stop_distance:
                # EMERGENCY STOP - Complete stop
                self.get_logger().warn(f"🛑 EMERGENCY STOP! Obstacle too close: {min_distance:.2f}m < {self.emergency_stop_distance:.2f}m")
                cmd.linear.x = 0.0
                cmd.angular.z = 0.0
            else:
                cmd.linear.x = 0.0
                cmd.angular.z = self.max_angular_speed*0.7 if obstacle_angle > 0 else -self.max_angular_speed*0.7
            self.cmd_vel_pub.publish(cmd)
            return
            
        elif goal_direction is not None:
            # GOAL SEEKING MODE
            # Move towards goal
            cmd.linear.x = self.max_linear_speed * 0.7  # Reduced speed for safety
            
            # Adjust heading towards goal
            angular_gain = 2.0  # Proportional gain for turning
            cmd.angular.z = angular_gain * goal_direction
            
            # Limit angular velocity
            cmd.angular.z = max(-self.max_angular_speed, min(self.max_angular_speed, cmd.angular.z))
            
            robot_x, robot_y, _ = self.current_pose
            goal_x, goal_y = self.goal_pose
            distance = math.sqrt((goal_x - robot_x)**2 + (goal_y - robot_y)**2)
            
            self.get_logger().info(f"🎯 Moving to goal: distance={distance:.2f}m, angle={math.degrees(goal_direction):.1f}°")
        
        else:
            # NO GOAL SET - STOP
            cmd.linear.x = 0.0
            cmd.angular.z = 0.0
        
        # Publish command
        self.cmd_vel_pub.publish(cmd)
        self.check_tf_teleport()

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = SimpleObstacleAvoidance()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        rclpy.shutdown()

if __name__ == '__main__':
    main()
