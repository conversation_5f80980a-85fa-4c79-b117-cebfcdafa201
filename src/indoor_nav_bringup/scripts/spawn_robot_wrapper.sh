#!/bin/bash

# =============================================================================
# ROBOT SPAWNING WRAPPER SCRIPT
# =============================================================================
# Wrapper script for spawning robot in Gazebo simulation
# Uses ros2 run gazebo_ros spawn_entity.py command
# =============================================================================

# Source ROS2 environment
source /opt/ros/humble/setup.bash

# Default values
TOPIC="robot_description"
ENTITY="indoor_robot"
X=0.0
Y=0.0
Z=0.05
ROLL=0.0
PITCH=0.0
YAW=0.0

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -topic)
            TOPIC="$2"
            shift 2
            ;;
        -entity)
            ENTITY="$2"
            shift 2
            ;;
        -x)
            X="$2"
            shift 2
            ;;
        -y)
            Y="$2"
            shift 2
            ;;
        -z)
            Z="$2"
            shift 2
            ;;
        -R)
            ROLL="$2"
            shift 2
            ;;
        -P)
            PITCH="$2"
            shift 2
            ;;
        -Y)
            YAW="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Spawning robot with parameters:"
echo "  Topic: $TOPIC"
echo "  Entity: $ENTITY"
echo "  Position: x=$X, y=$Y, z=$Z"
echo "  Orientation: R=$ROLL, P=$PITCH, Y=$YAW"

# Wait for Gazebo to be ready
echo "Waiting for Gazebo to be ready..."
sleep 5

# Spawn the robot
ros2 run gazebo_ros spawn_entity.py \
    -topic "$TOPIC" \
    -entity "$ENTITY" \
    -x "$X" \
    -y "$Y" \
    -z "$Z" \
    -R "$ROLL" \
    -P "$PITCH" \
    -Y "$YAW"

echo "Robot spawning completed"
