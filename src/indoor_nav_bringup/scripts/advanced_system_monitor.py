#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Bool
from nav_msgs.msg import OccupancyGrid, Path
from geometry_msgs.msg import PoseWithCovarianceStamped, Twist
from sensor_msgs.msg import LaserScan
import time
import json
from typing import Dict, Any, Optional

class AdvancedSystemMonitor(Node):
    """
    Advanced System Monitor for Phase 2: Advanced Features
    Monitors SLAM, path planning, and overall system performance
    """
    
    def __init__(self):
        super().__init__('advanced_system_monitor')
        
        # Parameters
        self.declare_parameter('monitor_rate', 2.0)  # Hz
        self.declare_parameter('slam_enabled', True)
        self.declare_parameter('advanced_features', True)
        self.declare_parameter('log_to_file', False)
        
        self.monitor_rate = self.get_parameter('monitor_rate').value
        self.slam_enabled = self.get_parameter('slam_enabled').value
        self.advanced_features = self.get_parameter('advanced_features').value
        self.log_to_file = self.get_parameter('log_to_file').value
        
        # System state
        self.system_status = {
            'overall': 'INITIALIZING',
            'slam': 'UNKNOWN',
            'planning': 'UNKNOWN',
            'navigation': 'UNKNOWN',
            'sensors': 'UNKNOWN'
        }
        
        # Performance metrics
        self.metrics = {
            'slam': {
                'map_quality': 0.0,
                'coverage': 0.0,
                'loop_closures': 0,
                'processing_time': 0.0
            },
            'planning': {
                'success_rate': 0.0,
                'avg_planning_time': 0.0,
                'avg_path_length': 0.0,
                'replans': 0
            },
            'navigation': {
                'current_speed': 0.0,
                'distance_to_goal': 0.0,
                'path_following_error': 0.0
            },
            'sensors': {
                'lidar_rate': 0.0,
                'pose_rate': 0.0,
                'last_scan_time': 0.0,
                'last_pose_time': 0.0
            }
        }
        
        # Data storage
        self.last_scan: Optional[LaserScan] = None
        self.last_pose: Optional[PoseWithCovarianceStamped] = None
        self.last_cmd_vel: Optional[Twist] = None
        self.last_path: Optional[Path] = None
        
        # Subscribers for monitoring
        self.slam_status_sub = self.create_subscription(
            String, '/slam/status', self.slam_status_callback, 10)
        self.slam_quality_sub = self.create_subscription(
            String, '/slam/quality', self.slam_quality_callback, 10)
        self.slam_stats_sub = self.create_subscription(
            String, '/slam/statistics', self.slam_stats_callback, 10)
        
        self.planner_status_sub = self.create_subscription(
            String, '/planner/status', self.planner_status_callback, 10)
        self.planner_stats_sub = self.create_subscription(
            String, '/planner/statistics', self.planner_stats_callback, 10)
        
        self.scan_sub = self.create_subscription(
            LaserScan, '/scan', self.scan_callback, 10)
        self.pose_sub = self.create_subscription(
            PoseWithCovarianceStamped, '/amcl_pose', self.pose_callback, 10)
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10)
        self.path_sub = self.create_subscription(
            Path, '/global_path', self.path_callback, 10)
        
        # Publishers
        self.system_status_pub = self.create_publisher(
            String, '/system/status', 10)
        self.system_metrics_pub = self.create_publisher(
            String, '/system/metrics', 10)
        self.system_health_pub = self.create_publisher(
            String, '/system/health', 10)
        
        # Timers
        self.monitor_timer = self.create_timer(
            1.0 / self.monitor_rate, self.monitor_system)
        self.metrics_timer = self.create_timer(5.0, self.publish_metrics)
        self.health_timer = self.create_timer(10.0, self.publish_health_report)
        
        # Initialize
        self.start_time = time.time()
        self.last_metrics_update = time.time()
        
        self.get_logger().info('🔍 Advanced System Monitor initialized')
        self.get_logger().info(f'📊 Monitor rate: {self.monitor_rate} Hz')
        self.get_logger().info(f'🗺️ SLAM enabled: {self.slam_enabled}')
        self.get_logger().info(f'⚡ Advanced features: {self.advanced_features}')
        
    def slam_status_callback(self, msg: String):
        """Process SLAM status updates"""
        try:
            status_data = self.parse_status_string(msg.data)
            self.system_status['slam'] = status_data.get('status', 'UNKNOWN')
        except Exception as e:
            self.get_logger().warn(f'Failed to parse SLAM status: {e}')
            
    def slam_quality_callback(self, msg: String):
        """Process SLAM quality updates"""
        try:
            quality_data = self.parse_status_string(msg.data)
            self.metrics['slam']['map_quality'] = float(quality_data.get('quality', 0.0))
            self.metrics['slam']['coverage'] = float(quality_data.get('coverage', 0.0))
        except Exception as e:
            self.get_logger().warn(f'Failed to parse SLAM quality: {e}')
            
    def slam_stats_callback(self, msg: String):
        """Process SLAM statistics updates"""
        try:
            stats_data = self.parse_status_string(msg.data)
            self.metrics['slam']['loop_closures'] = int(stats_data.get('loop_closures', 0))
            
            # Parse processing time
            proc_time_str = stats_data.get('avg_processing_time', '0.0s')
            self.metrics['slam']['processing_time'] = float(proc_time_str.replace('s', ''))
        except Exception as e:
            self.get_logger().warn(f'Failed to parse SLAM stats: {e}')
            
    def planner_status_callback(self, msg: String):
        """Process planner status updates"""
        try:
            status_data = self.parse_status_string(msg.data)
            self.system_status['planning'] = status_data.get('status', 'UNKNOWN')
        except Exception as e:
            self.get_logger().warn(f'Failed to parse planner status: {e}')
            
    def planner_stats_callback(self, msg: String):
        """Process planner statistics updates"""
        try:
            stats_data = self.parse_status_string(msg.data)
            self.metrics['planning']['success_rate'] = float(stats_data.get('success_rate', 0.0))
            self.metrics['planning']['replans'] = int(stats_data.get('replans', 0))
            
            # Parse timing and distance
            time_str = stats_data.get('avg_planning_time', '0.0s')
            self.metrics['planning']['avg_planning_time'] = float(time_str.replace('s', ''))
            
            length_str = stats_data.get('avg_path_length', '0.0m')
            self.metrics['planning']['avg_path_length'] = float(length_str.replace('m', ''))
        except Exception as e:
            self.get_logger().warn(f'Failed to parse planner stats: {e}')
            
    def scan_callback(self, msg: LaserScan):
        """Monitor LiDAR data"""
        current_time = time.time()
        
        if self.last_scan is not None:
            time_diff = current_time - self.metrics['sensors']['last_scan_time']
            if time_diff > 0:
                # Calculate rate with smoothing
                new_rate = 1.0 / time_diff
                alpha = 0.1
                self.metrics['sensors']['lidar_rate'] = (
                    alpha * new_rate + (1 - alpha) * self.metrics['sensors']['lidar_rate']
                )
                
        self.last_scan = msg
        self.metrics['sensors']['last_scan_time'] = current_time
        
    def pose_callback(self, msg: PoseWithCovarianceStamped):
        """Monitor pose data"""
        current_time = time.time()
        
        if self.last_pose is not None:
            time_diff = current_time - self.metrics['sensors']['last_pose_time']
            if time_diff > 0:
                # Calculate rate with smoothing
                new_rate = 1.0 / time_diff
                alpha = 0.1
                self.metrics['sensors']['pose_rate'] = (
                    alpha * new_rate + (1 - alpha) * self.metrics['sensors']['pose_rate']
                )
                
        self.last_pose = msg
        self.metrics['sensors']['last_pose_time'] = current_time
        
    def cmd_vel_callback(self, msg: Twist):
        """Monitor velocity commands"""
        self.last_cmd_vel = msg
        
        # Calculate current speed
        linear_speed = abs(msg.linear.x)
        angular_speed = abs(msg.angular.z)
        self.metrics['navigation']['current_speed'] = linear_speed
        
    def path_callback(self, msg: Path):
        """Monitor path updates"""
        self.last_path = msg
        
    def parse_status_string(self, status_str: str) -> Dict[str, str]:
        """Parse status string into dictionary"""
        result = {}
        for item in status_str.split(','):
            if ':' in item:
                key, value = item.split(':', 1)
                result[key.strip()] = value.strip()
        return result
        
    def monitor_system(self):
        """Main system monitoring function"""
        current_time = time.time()
        
        # Update sensor status
        if current_time - self.metrics['sensors']['last_scan_time'] > 2.0:
            self.system_status['sensors'] = 'LIDAR_TIMEOUT'
        elif current_time - self.metrics['sensors']['last_pose_time'] > 2.0:
            self.system_status['sensors'] = 'POSE_TIMEOUT'
        else:
            self.system_status['sensors'] = 'ACTIVE'
            
        # Update navigation status
        if self.last_cmd_vel is not None:
            if abs(self.last_cmd_vel.linear.x) > 0.01 or abs(self.last_cmd_vel.angular.z) > 0.01:
                self.system_status['navigation'] = 'MOVING'
            else:
                self.system_status['navigation'] = 'IDLE'
        else:
            self.system_status['navigation'] = 'NO_COMMANDS'
            
        # Determine overall status
        self.update_overall_status()
        
    def update_overall_status(self):
        """Update overall system status"""
        statuses = list(self.system_status.values())
        
        if 'TIMEOUT' in str(statuses) or 'FAILED' in str(statuses):
            self.system_status['overall'] = 'ERROR'
        elif 'UNKNOWN' in statuses:
            self.system_status['overall'] = 'INITIALIZING'
        elif self.system_status['navigation'] == 'MOVING':
            self.system_status['overall'] = 'ACTIVE'
        else:
            self.system_status['overall'] = 'READY'
            
    def publish_metrics(self):
        """Publish system metrics"""
        metrics_msg = String()
        
        # Flatten metrics for publishing
        flat_metrics = {}
        for category, metrics in self.metrics.items():
            for key, value in metrics.items():
                flat_metrics[f'{category}_{key}'] = value
                
        metrics_msg.data = json.dumps(flat_metrics)
        self.system_metrics_pub.publish(metrics_msg)
        
    def publish_health_report(self):
        """Publish comprehensive health report"""
        uptime = time.time() - self.start_time
        
        health_data = {
            'uptime': f'{uptime:.1f}s',
            'overall_status': self.system_status['overall'],
            'slam_quality': f"{self.metrics['slam']['map_quality']:.3f}",
            'planning_success': f"{self.metrics['planning']['success_rate']:.3f}",
            'lidar_rate': f"{self.metrics['sensors']['lidar_rate']:.1f}Hz",
            'pose_rate': f"{self.metrics['sensors']['pose_rate']:.1f}Hz"
        }
        
        health_msg = String()
        health_msg.data = ','.join([f'{k}:{v}' for k, v in health_data.items()])
        self.system_health_pub.publish(health_msg)
        
        # Log health report
        self.get_logger().info(
            f'🏥 Health: {self.system_status["overall"]} | '
            f'SLAM: {self.metrics["slam"]["map_quality"]:.2f} | '
            f'Planning: {self.metrics["planning"]["success_rate"]:.2f} | '
            f'Sensors: {self.metrics["sensors"]["lidar_rate"]:.1f}Hz'
        )

def main(args=None):
    rclpy.init(args=args)
    
    try:
        node = AdvancedSystemMonitor()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
