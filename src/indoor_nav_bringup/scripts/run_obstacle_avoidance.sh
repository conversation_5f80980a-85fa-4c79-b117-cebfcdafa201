#!/bin/bash

# Wrapper script to run obstacle avoidance with correct Python environment
# Fixes Python version conflicts between Conda and ROS2

# Deactivate conda environment
conda deactivate 2>/dev/null || true

# Clear conda environment variables
unset CONDA_DEFAULT_ENV
unset CONDA_EXE
unset CONDA_PREFIX
unset CONDA_PROMPT_MODIFIER
unset CONDA_PYTHON_EXE
unset CONDA_SHLVL

# Set clean PATH without conda
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

# Source ROS2 environment
source /opt/ros/humble/setup.bash

# Run the Python script with system Python
exec /usr/bin/python3 "$(dirname "$0")/simple_obstacle_avoidance.py" "$@"
