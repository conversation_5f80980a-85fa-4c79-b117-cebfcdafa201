# Remote Deployment Guide

This guide explains how to run ROS on one computer and the web interface on a different computer.

## 🏗️ Architecture

```
┌─────────────────────┐    Network    ┌─────────────────────┐
│   Computer A        │◄─────────────►│   Computer B        │
│   (ROS System)      │               │   (Web Interface)   │
├─────────────────────┤               ├─────────────────────┤
│ • ROS Noetic        │               │ • Backend Server    │
│ • Robot Drivers     │               │ • Frontend          │
│ • SLAM/Navigation   │               │ • No ROS required   │
│ • ROS Bridge        │               │                     │
└─────────────────────┘               └─────────────────────┘
```

## 📋 Prerequisites

### Computer A (ROS System):
- ROS Noetic installed and working
- Your robot drivers running
- Python 3 with `rospy`, `websocket-client`, `requests`
- Network connectivity to Computer B

### Computer B (Web Interface):
- Python 3.7+
- Node.js (for frontend)
- No ROS installation required
- Network connectivity to Computer A

## 🚀 Setup Instructions

### Step 1: Setup Computer B (Web Interface)

1. **Clone the project** (if not already done):
   ```bash
   git clone <your-repo>
   cd indoor_autonomous_vehicle
   ```

2. **Start the remote backend**:
   ```bash
   ./start_backend_remote.sh
   ```

3. **Note the IP address** of Computer B:
   ```bash
   hostname -I
   # Example output: *************
   ```

4. **Verify backend is running**:
   ```bash
   curl http://localhost:8000/health
   ```

### Step 2: Setup Computer A (ROS System)

1. **Copy bridge files** to Computer A:
   ```bash
   # Copy these files to Computer A:
   scp ros_noetic_bridge.py user@computer-a:~/
   scp start_bridge.sh user@computer-a:~/
   scp bridge_config.yaml user@computer-a:~/
   ```

2. **On Computer A, start your ROS system**:
   ```bash
   # Terminal 1
   roscore
   
   # Terminal 2
   roslaunch your_robot_package robot.launch
   
   # Terminal 3 (if using navigation)
   roslaunch your_robot_package navigation.launch
   ```

3. **Start the bridge pointing to Computer B**:
   ```bash
   # Replace ************* with Computer B's IP
   ./start_bridge.sh --host *************
   ```

### Step 3: Access Web Interface

1. **Open browser** on any computer in the network
2. **Navigate to**: `http://COMPUTER_B_IP:8000`
3. **You should see** the web interface with live robot data

## 🔧 Configuration

### Backend Configuration (Computer B)

Edit `start_backend_remote.sh` if needed:
```bash
# Change default port
./start_backend_remote.sh --port 3000

# Bind to specific interface
./start_backend_remote.sh --host *************
```

### Bridge Configuration (Computer A)

Edit `bridge_config.yaml`:
```yaml
# Backend server (Computer B)
backend_host: "*************"  # IP of Computer B
backend_port: 8000

# ROS topics (adjust to match your robot)
ros_topics:
  scan: "/scan"
  odom: "/odom"
  cmd_vel: "/cmd_vel"
```

## 🌐 Network Requirements

### Firewall Settings

**Computer B (Web Interface):**
- Allow incoming connections on port 8000 (or your chosen port)
- Allow incoming WebSocket connections

**Computer A (ROS System):**
- Allow outgoing connections to Computer B:8000

### Port Configuration

| Service | Computer | Port | Direction |
|---------|----------|------|-----------|
| Backend API | B | 8000 | Incoming |
| WebSocket | B | 8000 | Incoming |
| Frontend | B | 3000 | Incoming (optional) |
| Bridge | A | - | Outgoing to B:8000 |

## 🔍 Troubleshooting

### Bridge Cannot Connect to Backend

1. **Check network connectivity**:
   ```bash
   # From Computer A
   ping COMPUTER_B_IP
   telnet COMPUTER_B_IP 8000
   ```

2. **Check backend is running**:
   ```bash
   # From Computer B
   curl http://localhost:8000/health
   ```

3. **Check firewall**:
   ```bash
   # Computer B - allow port 8000
   sudo ufw allow 8000
   ```

### Web Interface Shows "Disconnected"

1. **Check bridge logs** on Computer A
2. **Check backend logs** on Computer B
3. **Verify WebSocket connection** in browser console

### Commands Not Reaching Robot

1. **Check bridge receives commands** (bridge logs should show "Received command")
2. **Verify ROS topics**:
   ```bash
   # On Computer A
   rostopic echo /cmd_vel
   ```

### Performance Issues

1. **Reduce update rate** in bridge config:
   ```yaml
   update_rate: 5  # Reduce from 10 Hz to 5 Hz
   ```

2. **Enable data filtering**:
   ```yaml
   data_filters:
     scan_decimation: 2      # Send every 2nd scan
     odom_decimation: 2      # Send every 2nd odom
   ```

## 📊 Monitoring

### Backend Status (Computer B)
```bash
curl http://localhost:8000/api/status
```

### Bridge Status (Computer A)
```bash
curl http://localhost:9090/status
```

### Network Traffic
```bash
# Monitor network usage
iftop -i eth0
```

## 🔒 Security Considerations

### For Production Deployment:

1. **Use HTTPS** instead of HTTP
2. **Configure authentication** in the backend
3. **Use VPN** for secure communication
4. **Restrict firewall rules** to specific IPs
5. **Use reverse proxy** (nginx) for the backend

### Example nginx configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🎯 Quick Start Commands

### Computer B (Web Interface):
```bash
./start_backend_remote.sh
```

### Computer A (ROS System):
```bash
# Start ROS
roscore &
roslaunch your_robot_package robot.launch &

# Start bridge (replace IP)
./start_bridge.sh --host *************
```

### Access:
- Web Interface: `http://COMPUTER_B_IP:8000`
- API Docs: `http://COMPUTER_B_IP:8000/docs`

## 📝 File Checklist

**Computer A (ROS System) needs:**
- `ros_noetic_bridge.py`
- `start_bridge.sh`
- `bridge_config.yaml`

**Computer B (Web Interface) needs:**
- Full project repository
- `start_backend_remote.sh`

This setup allows you to run ROS on a dedicated robot computer while accessing the web interface from any computer on the network!
