# Frontend Configuration
# Copy this file to .env and modify as needed

# Environment (development, production, remote)
VITE_APP_ENV=development

# Backend server configuration
VITE_APP_BACKEND_HOST=localhost
VITE_APP_BACKEND_PORT=8000
VITE_APP_BACKEND_PROTOCOL=http

# For remote development (when backend is on different machine)
# VITE_APP_ENV=remote
# VITE_APP_BACKEND_HOST=************

# Debug settings
VITE_APP_DEBUG=true
VITE_APP_LOG_LEVEL=debug
