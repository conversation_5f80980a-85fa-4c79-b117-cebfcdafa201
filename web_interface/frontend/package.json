{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/three": "^0.160.0", "axios": "^1.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "recharts": "^2.8.0", "three": "^0.160.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "dev:local": "REACT_APP_ENV=development vite", "dev:remote": "REACT_APP_ENV=remote vite", "build": "vite build", "build:local": "REACT_APP_ENV=development vite build", "build:remote": "REACT_APP_ENV=remote vite build", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/bonjour": "^3.5.13", "@types/estree": "^1.0.8", "@types/node": "^22.15.30", "@types/prettier": "^2.7.3", "@types/stats.js": "^0.17.4", "@types/trusted-types": "^2.0.7", "@types/webxr": "^0.5.22", "@vitejs/plugin-react": "^4.7.0", "vite": "^6.3.5"}}