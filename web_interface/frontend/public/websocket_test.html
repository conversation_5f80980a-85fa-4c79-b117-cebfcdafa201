<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; font-weight: bold; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        .log { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; margin: 10px 0; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket Connection Test</h1>
        
        <div id="status" class="status disconnected">🔴 Disconnected</div>
        
        <div>
            <button onclick="testConnection()">Test Connection</button>
            <button onclick="sendSubscription()" id="subBtn" disabled>Send Subscription</button>
            <button onclick="sendCommand()" id="cmdBtn" disabled>Send Command</button>
            <button onclick="disconnect()">Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <h3>📝 Connection Log</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 5;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[WebSocket Test] ${message}`);
        }

        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
            
            const subBtn = document.getElementById('subBtn');
            const cmdBtn = document.getElementById('cmdBtn');
            subBtn.disabled = status !== 'connected';
            cmdBtn.disabled = status !== 'connected';
        }

        function testConnection() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ Already connected');
                return;
            }

            log('🔄 Testing WebSocket connection to ws://localhost:8000/ws');
            updateStatus('connecting', '🟡 Connecting...');

            try {
                ws = new WebSocket('ws://localhost:8000/ws');

                ws.onopen = function(event) {
                    log('✅ WebSocket connected successfully!');
                    updateStatus('connected', '🟢 Connected');
                    reconnectAttempts = 0;
                    
                    log('📡 Connection details:');
                    log(`   - URL: ${ws.url}`);
                    log(`   - ReadyState: ${ws.readyState}`);
                    log(`   - Protocol: ${ws.protocol}`);
                    log(`   - Extensions: ${ws.extensions}`);
                };

                ws.onclose = function(event) {
                    log(`❌ WebSocket closed:`);
                    log(`   - Code: ${event.code}`);
                    log(`   - Reason: ${event.reason}`);
                    log(`   - WasClean: ${event.wasClean}`);
                    updateStatus('disconnected', '🔴 Disconnected');

                    // Auto-reconnect
                    if (reconnectAttempts < maxReconnectAttempts && !event.wasClean) {
                        reconnectAttempts++;
                        const delay = 2000 * reconnectAttempts;
                        log(`🔄 Auto-reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} in ${delay}ms`);
                        setTimeout(testConnection, delay);
                    }
                };

                ws.onerror = function(error) {
                    log(`❌ WebSocket error occurred:`);
                    log(`   - Error: ${error}`);
                    log(`   - Type: ${error.type}`);
                    log(`   - Target: ${error.target}`);
                    updateStatus('disconnected', '🔴 Error');
                };

                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        log(`📨 Received message:`);
                        log(`   - Type: ${message.type}`);
                        log(`   - Data: ${JSON.stringify(message).substring(0, 100)}...`);
                    } catch (e) {
                        log(`📨 Received raw data: ${event.data.substring(0, 100)}...`);
                    }
                };

            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error}`);
                updateStatus('disconnected', '🔴 Failed');
            }
        }

        function sendSubscription() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const subscribeMessage = {
                    type: 'subscribe',
                    topics: ['pose', 'odom', 'scan', 'battery', 'map', 'diagnostics', 'log', 'ultrasonic', 'node_status']
                };
                
                ws.send(JSON.stringify(subscribeMessage));
                log(`📤 Sent subscription: ${JSON.stringify(subscribeMessage)}`);
            } else {
                log('❌ Cannot send subscription - not connected');
            }
        }

        function sendCommand() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testCommand = {
                    type: 'command',
                    command: 'get_status',
                    params: {}
                };
                
                ws.send(JSON.stringify(testCommand));
                log(`📤 Sent command: ${JSON.stringify(testCommand)}`);
            } else {
                log('❌ Cannot send command - not connected');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close(1000, 'Manual disconnect');
                log('🛑 Manual disconnect requested');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Auto-start connection test
        window.onload = function() {
            log('🚀 WebSocket Connection Test Started');
            log('🔍 Testing connection to backend...');
            testConnection();
        };

        // Cleanup on page unload
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
