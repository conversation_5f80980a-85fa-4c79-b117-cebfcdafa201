#!/bin/bash

# =============================================================================
# ROS2 NAVIGATION SYSTEM STARTUP SCRIPT
# =============================================================================
# Starts only the ROS2 navigation system (Gazebo + SLAM + Navigation)
# Run this in a separate terminal from web interface
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    # Prevent multiple cleanup calls
    if [ "$CLEANUP_DONE" = "true" ]; then
        return
    fi
    CLEANUP_DONE=true

    print_header "SHUTTING DOWN ROS2 SYSTEM"
    print_status "Stopping ROS2 processes..."

    # Kill ROS2 processes
    pkill -f "ros2" 2>/dev/null || true
    pkill -f "slam_toolbox" 2>/dev/null || true
    pkill -f "simple_obstacle_avoidance" 2>/dev/null || true

    # Kill Gazebo processes
    killall -9 gazebo gzserver gzclient 2>/dev/null || true

    print_success "ROS2 system shutdown complete"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Parse command line arguments
USE_GAZEBO=true
USE_SIM_TIME=true
USE_REAL_ROBOT=false
USE_DUAL_LIDAR=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --real-robot)
            USE_REAL_ROBOT=true
            USE_GAZEBO=false
            USE_SIM_TIME=false
            shift
            ;;
        --dual-lidar)
            USE_DUAL_LIDAR=true
            shift
            ;;
        --simulation)
            USE_REAL_ROBOT=false
            USE_GAZEBO=true
            USE_SIM_TIME=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --simulation    Run in Gazebo simulation mode (default)"
            echo "  --real-robot    Run with real robot hardware"
            echo "  --dual-lidar    Enable dual LiDAR mode (front + rear sensors)"
            echo "  --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run simulation (default)"
            echo "  $0 --simulation       # Run simulation explicitly"
            echo "  $0 --dual-lidar       # Run simulation with dual LiDAR"
            echo "  $0 --real-robot       # Run with real robot (single LiDAR)"
            echo "  $0 --real-robot --dual-lidar  # Run with real robot (dual LiDAR)"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "ROS2 NAVIGATION SYSTEM"
print_status "Starting ROS2 system in $([ "$USE_REAL_ROBOT" = true ] && echo "REAL ROBOT" || echo "SIMULATION") mode"

# Check if we're in the right directory
if [ ! -f "src/indoor_nav_bringup/package.xml" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: src/indoor_nav_bringup/package.xml"
    exit 1
fi

# Environment Setup
print_header "ENVIRONMENT SETUP"

print_status "Deactivating conda environment..."
conda deactivate 2>/dev/null || true

print_status "Sourcing ROS2 environment..."
source /opt/ros/humble/setup.bash

# Fix Qt platform plugin issues
export QT_QPA_PLATFORM=xcb
export QT_QPA_PLATFORM_PLUGIN_PATH=/usr/lib/x86_64-linux-gnu/qt5/plugins

print_status "Building workspace..."
colcon build --packages-select indoor_nav_bringup
if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

print_status "Sourcing workspace..."
source install/setup.bash

print_success "Environment ready"

# Real robot validation
if [ "$USE_REAL_ROBOT" = true ]; then
    print_header "REAL ROBOT MODE - HARDWARE VALIDATION"
    print_warning "Real robot mode requires proper hardware setup!"
    print_warning "See REAL_ROBOT_SETUP.md for complete requirements"
    echo ""
    
    print_status "Please ensure your robot hardware drivers are running:"
    echo "  📡 LiDAR driver publishing to /scan"
    echo "  🤖 Motor controller subscribing to /cmd_vel"  
    echo "  📍 Odometry system publishing to /odom"
    echo "  🔄 Transform publisher for odom→base_link"
    echo ""
    
    read -p "Press Enter when hardware drivers are ready, or Ctrl+C to abort..."
    echo ""
    
    print_warning "⚠️  SAFETY REMINDER:"
    echo "  🛑 Keep emergency stop ready: Ctrl+C or power switch"
    echo "  🐌 System starts with conservative speeds"
    echo "  👀 Monitor robot behavior closely"
    echo ""
fi

# Start ROS2 Navigation System
print_header "STARTING ROS2 NAVIGATION SYSTEM"
print_status "Cleaning up any existing processes..."
killall -9 gazebo gzserver gzclient ros2 slam_toolbox robot_state_publisher joint_state_publisher 2>/dev/null || true
sleep 2
rm -f /tmp/.gazebo_lock* > /dev/null 2>&1 || true
rm -f ~/.gazebo/server-* > /dev/null 2>&1 || true
sleep 3

if [ "$USE_REAL_ROBOT" = true ]; then
    if [ "$USE_DUAL_LIDAR" = true ]; then
        print_status "Launching real robot with DUAL LiDAR navigation system..."
        print_status "Components starting:"
        echo "  🤖 Robot state publisher"
        echo "  📡 Front LiDAR sensor (/dev/ttyUSB0)"
        echo "  📡 Rear LiDAR sensor (/dev/ttyUSB1)"
        echo "  🔄 Dual LiDAR merger"
        echo "  🗺️  SLAM toolbox (real-time mapping)"
        echo "  🧠 Obstacle avoidance navigation"
        echo ""

        ros2 launch indoor_nav_bringup real_robot_dual_lidar.launch.py \
            front_lidar_port:=/dev/ttyUSB0 \
            rear_lidar_port:=/dev/ttyUSB1 \
            use_slam:=true \
            use_navigation:=true
    else
        print_status "Launching real robot navigation system..."
        print_status "Components starting:"
        echo "  🤖 Robot state publisher"
        echo "  📡 Single LiDAR sensor"
        echo "  🗺️  SLAM toolbox (real-time mapping)"
        echo "  🧠 Obstacle avoidance navigation"
        echo "  📡 Map server"
        echo ""

        ros2 launch indoor_nav_bringup simple_nav2.launch.py \
            use_gazebo:=false \
            use_sim_time:=false \
            use_rviz:=false \
            use_dual_lidar:=false
    fi
else
    if [ "$USE_DUAL_LIDAR" = true ]; then
        print_status "Launching Gazebo simulation with DUAL LiDAR..."
        print_status "Components starting:"
        echo "  🏠 Gazebo indoor house simulation"
        echo "  🤖 Robot with dual LiDAR sensors"
        echo "  📡 Front LiDAR simulation"
        echo "  📡 Rear LiDAR simulation"
        echo "  🔄 Dual LiDAR merger"
        echo "  🗺️  SLAM toolbox (real-time mapping)"
        echo "  🧠 Simple obstacle avoidance navigation"
        echo "  📡 Map server"
        echo ""

        ros2 launch indoor_nav_bringup simple_nav2.launch.py \
            use_gazebo:=true \
            use_sim_time:=true \
            use_rviz:=false \
            use_dual_lidar:=true
    else
        print_status "Launching Gazebo simulation navigation system..."
        print_status "Components starting:"
        echo "  🏠 Gazebo indoor house simulation"
        echo "  🤖 Robot with single LiDAR sensor"
        echo "  🗺️  SLAM toolbox (real-time mapping)"
        echo "  🧠 Simple obstacle avoidance navigation"
        echo "  📡 Map server"
        echo ""

        ros2 launch indoor_nav_bringup simple_nav2.launch.py \
            use_gazebo:=true \
            use_sim_time:=true \
            use_rviz:=false \
            use_dual_lidar:=false
    fi
fi

# This line should not be reached unless ROS2 system exits
print_warning "ROS2 navigation system has stopped"
