# 🚗 HƯỚNG DẪN HỆ THỐNG XE TỰ HÀNH

## 📋 TỔNG QUAN HỆ THỐNG

### 🎯 Giới Thiệu
Hệ thống **Autonomous Vehicle Web Interface** là một giao diện web hiện đại để điều khiển và giám sát xe tự hành sử dụng ROS2-Humble + Gazebo. Hệ thống cung cấp giao diện trực quan, dễ sử dụng với 13 trang chức năng đầy đủ.

### 🏗️ Kiến Trúc Hệ Thống
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   ROS2 System   │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (Humble)      │
│   Port: 3000    │    │   Port: 8000    │    │   Gazebo Sim    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔧 Công Nghệ Sử Dụng
- **Frontend:** React 18 + TypeScript + Material-UI
- **Backend:** FastAPI + Python 3.10
- **ROS2:** Humble Hawksbill
- **Simulation:** Gazebo Classic
- **Communication:** WebSocket + REST API
- **3D Graphics:** Three.js + WebGL

---

## 🚀 CÀI ĐẶT VÀ KHỞI CHẠY

### 📋 Yêu <PERSON>u <PERSON>hống
- **OS:** Ubuntu 22.04 LTS
- **ROS2:** Humble Hawksbill
- **Node.js:** v18+ 
- **Python:** 3.10+
- **RAM:** 8GB+ (khuyến nghị 16GB)
- **GPU:** Hỗ trợ OpenGL 3.3+

### 🔧 Cài Đặt Dependencies

#### 1. Cài Đặt ROS2 Humble
```bash
# Thêm ROS2 repository
sudo apt update && sudo apt install curl gnupg lsb-release
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg

# Cài đặt ROS2 Humble
sudo apt update
sudo apt install ros-humble-desktop-full

# Setup environment
echo "source /opt/ros/humble/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

#### 2. Cài Đặt Gazebo
```bash
sudo apt install gazebo
sudo apt install ros-humble-gazebo-*
```

#### 3. Cài Đặt Node.js và Python Dependencies
```bash
# Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Python dependencies
pip install fastapi uvicorn websockets rclpy
```

### 🏃 Khởi Chạy Hệ Thống

#### 1. Khởi Động ROS2 và Gazebo
```bash
# Terminal 1: Khởi động Gazebo
cd indoor_autonomous_vehicle
source /opt/ros/humble/setup.bash
colcon build
source install/setup.bash
ros2 launch vehicle_simulation simulation.launch.py

# Terminal 2: Khởi động các nodes
ros2 run vehicle_control motion_control_node
ros2 run vehicle_sensors sensor_fusion_node
ros2 run vehicle_navigation path_planning_node
```

#### 2. Khởi Động Backend
```bash
# Terminal 3: Backend API
cd web_interface/backend
python main.py
# Server chạy tại: http://localhost:8000
```

#### 3. Khởi Động Frontend
```bash
# Terminal 4: Frontend Web
cd web_interface/frontend
npm install
npm start
# Web interface tại: http://localhost:3000
```

---

## 🎮 HƯỚNG DẪN SỬ DỤNG

### 🏠 1. Dashboard - Trang Chủ
**Mục đích:** Tổng quan trạng thái hệ thống

**Chức năng chính:**
- 📊 **System Overview:** Hiển thị trạng thái tổng quan
- 🔋 **Battery Status:** Mức pin và thời gian hoạt động
- 🌐 **Connection Status:** Trạng thái kết nối ROS2
- 📈 **Quick Stats:** Thống kê nhanh về hệ thống

**Cách sử dụng:**
1. Mở trình duyệt và truy cập `http://localhost:3000`
2. Kiểm tra trạng thái kết nối (🟢 Connected / 🔴 Disconnected)
3. Theo dõi các chỉ số quan trọng trên dashboard

### 🎮 2. Robot Control - Điều Khiển Robot
**Mục đích:** Điều khiển chuyển động của robot

**3 Chế Độ Điều Khiển:**

#### A. Button Control (Điều Khiển Nút)
- ⬆️ **Forward:** Di chuyển tiến
- ⬇️ **Backward:** Di chuyển lùi  
- ⬅️ **Left:** Quay trái
- ➡️ **Right:** Quay phải
- ⏹️ **Stop:** Dừng ngay lập tức

#### B. Basic Joystick (Joystick Cơ Bản)
- 🕹️ **Drag Control:** Kéo để điều khiển hướng
- 🎯 **Click & Release:** Nhấn và thả để di chuyển
- 📱 **Touch Support:** Hỗ trợ thiết bị cảm ứng

#### C. Advanced Joystick (Joystick Nâng Cao)
- 🎮 **Continuous Control:** Điều khiển liên tục
- ⚡ **Real-time Response:** Phản hồi thời gian thực
- 🔧 **Configurable Speed:** Tùy chỉnh tốc độ

**Cách sử dụng:**
1. Chọn tab "🎮 Robot Control"
2. Chọn chế độ điều khiển phù hợp
3. Sử dụng các nút hoặc joystick để điều khiển
4. Nhấn "Emergency Stop" khi cần dừng khẩn cấp

### 🧭 3. Navigation - Điều Hướng
**Mục đích:** Lập kế hoạch đường đi và điều hướng tự động

**Chức năng chính:**
- 🗺️ **Interactive Map:** Bản đồ tương tác
- 🎯 **Goal Setting:** Đặt điểm đích
- 🛤️ **Path Planning:** Lập kế hoạch đường đi
- 📍 **Waypoint Management:** Quản lý điểm trung gian

**Cách sử dụng:**
1. Chọn tab "🧭 Navigation"
2. Click vào bản đồ để đặt điểm đích
3. Robot sẽ tự động lập kế hoạch và di chuyển
4. Theo dõi tiến trình trên bản đồ

### 📡 4. Sensors - Cảm Biến
**Mục đích:** Giám sát dữ liệu từ các cảm biến

**Loại Cảm Biến:**
- 🔍 **LiDAR:** Quét laser 360°
- 📷 **Camera:** Hình ảnh thời gian thực
- 🔊 **Ultrasonic:** Cảm biến siêu âm
- 🧭 **IMU:** Cảm biến quán tính

**Cách sử dụng:**
1. Chọn tab "📡 Sensors"
2. Xem dữ liệu thời gian thực từ các cảm biến
3. Điều chỉnh cài đặt hiển thị nếu cần

### 🗺️ 5. 2D Map View - Bản Đồ 2D
**Mục đích:** Xem bản đồ 2D chi tiết với khả năng tương tác

**Chức năng nâng cao:**
- 🔍 **Zoom & Pan:** Phóng to/thu nhỏ và di chuyển
- 🎯 **Click Navigation:** Click để điều hướng
- 📊 **Real-time Data:** Dữ liệu thời gian thực
- 🎛️ **Display Controls:** Điều khiển hiển thị

**Cách sử dụng:**
1. Chọn tab "🗺️ 2D Map View"
2. Sử dụng chuột để zoom và pan
3. Click vào vị trí muốn robot di chuyển đến
4. Bật/tắt các lớp hiển thị (LiDAR, Path, Goals)

### 🎮 6. 3D Visualization - Trực Quan 3D
**Mục đích:** Xem môi trường 3D ảo của robot

**Tính năng 3D:**
- 🌍 **Virtual Environment:** Môi trường ảo 3D
- 🤖 **3D Robot Model:** Mô hình robot 3D
- 📡 **LiDAR Visualization:** Hiển thị dữ liệu LiDAR
- 🎮 **Interactive Controls:** Điều khiển tương tác

**Điều khiển 3D:**
- 🖱️ **Mouse Drag:** Kéo để xoay camera
- 🔄 **Scroll Wheel:** Zoom in/out
- 🎯 **Click Ground:** Click để đặt mục tiêu
- 🔄 **Reset View:** Nút reset góc nhìn

**Cách sử dụng:**
1. Chọn tab "🎮 3D Visualization"
2. Đợi scene khởi tạo (có loading indicator)
3. Sử dụng chuột để điều khiển camera
4. Click vào mặt đất để đặt điểm đích

### 📊 7. Real-time Charts - Biểu Đồ Thời Gian Thực
**Mục đích:** Theo dõi dữ liệu qua biểu đồ

**5 Loại Biểu Đồ:**
- 🔋 **Battery Chart:** Biểu đồ pin
- 💻 **CPU Usage:** Sử dụng CPU
- 🧠 **Memory Usage:** Sử dụng bộ nhớ
- 🏃 **Velocity Chart:** Biểu đồ vận tốc
- 📡 **LiDAR Range:** Phạm vi LiDAR

**Cách sử dụng:**
1. Chọn tab "📊 Real-time Charts"
2. Điều chỉnh time range và update rate
3. Xem thống kê min/max/avg cho mỗi biểu đồ

### 🔍 8. Diagnostics - Chẩn Đoán Hệ Thống
**Mục đích:** Kiểm tra sức khỏe hệ thống

**Thông tin chẩn đoán:**
- 💻 **System Resources:** Tài nguyên hệ thống
- 🔧 **Hardware Status:** Trạng thái phần cứng
- 📊 **Performance Metrics:** Chỉ số hiệu suất
- ⚠️ **Error Detection:** Phát hiện lỗi

### 💻 9. Terminal - Giao Diện Dòng Lệnh
**Mục đích:** Thực thi lệnh ROS2 trực tiếp

**Chức năng:**
- 🖥️ **Real PTY Terminal:** Terminal thật
- 📝 **Command History:** Lịch sử lệnh
- 🔍 **Auto-completion:** Tự động hoàn thành
- 📋 **Copy/Paste:** Sao chép/dán

### 📋 10. System Logs - Nhật Ký Hệ Thống
**Mục đích:** Xem và quản lý log hệ thống

**Tính năng log:**
- 🔍 **Real-time Streaming:** Stream thời gian thực
- 🎛️ **Advanced Filtering:** Lọc nâng cao
- 📤 **Export Functionality:** Xuất file log
- 📊 **Log Statistics:** Thống kê log

### ⚙️ 11. Parameters - Quản Lý Tham Số
**Mục đích:** Cấu hình tham số ROS2

**Quản lý tham số:**
- 🔧 **Node Parameters:** Tham số từng node
- 📋 **Parameter Presets:** Cài đặt sẵn
- ✅ **Real-time Validation:** Kiểm tra thời gian thực
- 💾 **Save/Load Config:** Lưu/tải cấu hình

### 🔧 12. Node Manager - Quản Lý Node
**Mục đích:** Điều khiển các ROS2 nodes

**Chức năng quản lý:**
- ▶️ **Start/Stop Nodes:** Khởi động/dừng nodes
- 🔄 **Restart Nodes:** Khởi động lại
- 📊 **Resource Monitoring:** Giám sát tài nguyên
- 📈 **Performance Stats:** Thống kê hiệu suất

### 💻 13. System Status - Trạng Thái Hệ Thống
**Mục đích:** Giám sát tổng thể hệ thống

**Thông tin trạng thái:**
- 🖥️ **Hardware Info:** Thông tin phần cứng
- 🌐 **Network Status:** Trạng thái mạng
- 🔝 **Top Processes:** Tiến trình hàng đầu
- 📊 **System Health:** Sức khỏe hệ thống

---

## 🛠️ XỬ LÝ SỰ CỐ

### ❌ Các Lỗi Thường Gặp

#### 1. Lỗi Kết Nối WebSocket
**Triệu chứng:** Hiển thị "🔴 Disconnected"
**Nguyên nhân:** Backend không chạy hoặc port bị chặn
**Giải pháp:**
```bash
# Kiểm tra backend
cd web_interface/backend
python main.py

# Kiểm tra port
netstat -tulpn | grep 8000
```

#### 2. ROS2 Nodes Không Hoạt Động
**Triệu chứng:** Không có dữ liệu sensor
**Nguyên nhân:** ROS2 environment chưa được setup
**Giải pháp:**
```bash
source /opt/ros/humble/setup.bash
source install/setup.bash
ros2 node list  # Kiểm tra nodes đang chạy
```

#### 3. 3D Visualization Không Tải
**Triệu chứng:** Màn hình đen hoặc loading mãi
**Nguyên nhân:** GPU không hỗ trợ WebGL
**Giải pháp:**
- Cập nhật driver GPU
- Sử dụng trình duyệt Chrome/Firefox mới nhất
- Kiểm tra WebGL: `chrome://gpu/`

#### 4. Frontend Không Compile
**Triệu chứng:** Lỗi TypeScript hoặc dependency
**Giải pháp:**
```bash
cd web_interface/frontend
rm -rf node_modules package-lock.json
npm install
npm start
```

### 🔧 Bảo Trì Hệ Thống

#### Cập Nhật Dependencies
```bash
# Frontend
cd web_interface/frontend
npm update

# Backend
pip install --upgrade fastapi uvicorn

# ROS2
sudo apt update && sudo apt upgrade
```

#### Backup Cấu Hình
```bash
# Backup parameters
ros2 param dump /vehicle_control > config_backup.yaml

# Backup logs
cp -r ~/.ros/log/ backup_logs/
```

#### Monitoring Hiệu Suất
```bash
# Kiểm tra CPU/Memory
htop

# Kiểm tra ROS2 topics
ros2 topic hz /scan
ros2 topic echo /odom

# Kiểm tra network
iftop
```

---

## 📚 TÀI LIỆU THAM KHẢO

### 🔗 Links Hữu Ích
- **ROS2 Documentation:** https://docs.ros.org/en/humble/
- **Gazebo Tutorials:** http://gazebosim.org/tutorials
- **React Documentation:** https://react.dev/
- **Material-UI:** https://mui.com/
- **Three.js:** https://threejs.org/

### 📞 Hỗ Trợ Kỹ Thuật
- **GitHub Issues:** [Repository Issues]
- **ROS2 Community:** https://discourse.ros.org/
- **Stack Overflow:** Tag `ros2`, `gazebo`, `react`

### 🎓 Học Tập Thêm
- **ROS2 Tutorials:** Học cơ bản về ROS2
- **Gazebo Simulation:** Tạo môi trường simulation
- **Web Development:** React + TypeScript
- **3D Graphics:** Three.js và WebGL

---

## 🎉 KẾT LUẬN

Hệ thống **Autonomous Vehicle Web Interface** cung cấp một giải pháp hoàn chỉnh để điều khiển và giám sát xe tự hành. Với 13 trang chức năng đầy đủ, giao diện hiện đại và khả năng tương tác cao, hệ thống phù hợp cho cả mục đích nghiên cứu và ứng dụng thực tế.

**Tính năng nổi bật:**
- ✅ **Giao diện trực quan** dễ sử dụng
- ✅ **Điều khiển đa dạng** (nút, joystick, bản đồ)
- ✅ **Giám sát thời gian thực** toàn diện
- ✅ **Visualization 3D** chuyên nghiệp
- ✅ **Quản lý hệ thống** đầy đủ

Hệ thống sẵn sàng cho việc phát triển và mở rộng thêm các tính năng mới! 🚗🌟

---

## 🔧 THÔNG TIN KỸ THUẬT CHI TIẾT

### 📁 Cấu Trúc Thư Mục
```
indoor_autonomous_vehicle/
├── 🎮 web_interface/
│   ├── frontend/                 # React TypeScript App
│   │   ├── src/
│   │   │   ├── components/       # UI Components
│   │   │   ├── pages/           # 13 Main Pages
│   │   │   ├── hooks/           # Custom Hooks
│   │   │   └── types/           # TypeScript Types
│   │   └── public/              # Static Assets
│   └── backend/                 # FastAPI Server
│       ├── main.py              # Main Server
│       ├── websocket_handler.py # WebSocket Logic
│       └── ros2_bridge.py       # ROS2 Integration
├── 🤖 vehicle_control/          # Motion Control
├── 📡 vehicle_sensors/          # Sensor Processing
├── 🧭 vehicle_navigation/       # Path Planning
├── 🎯 vehicle_simulation/       # Gazebo Simulation
└── 📋 vehicle_interfaces/       # ROS2 Messages
```

### 🌐 API Endpoints

#### REST API (Port 8000)
```
GET  /api/status              # System status
GET  /api/nodes               # ROS2 nodes list
POST /api/command             # Send robot command
GET  /api/parameters          # Get ROS2 parameters
POST /api/parameters          # Set ROS2 parameters
GET  /api/logs                # Get system logs
```

#### WebSocket (Port 8000/ws)
```
/ws                           # Main WebSocket connection
├── subscribe: [topics]       # Subscribe to ROS2 topics
├── unsubscribe: [topics]     # Unsubscribe from topics
└── command: {cmd, params}    # Send real-time commands
```

### 📊 Data Flow Architecture
```
ROS2 Topics → Backend Bridge → WebSocket → Frontend Components
     ↓              ↓              ↓              ↓
  /scan         sensor_data    real-time      SensorsPage
  /odom         robot_data     streaming      Map2DPage
  /cmd_vel      commands       updates        RobotControl
  /diagnostics  system_data    bi-directional ChartsPage
```

### 🎨 Frontend Architecture

#### Component Hierarchy
```
App.tsx
├── Sidebar.tsx               # Navigation menu
├── pages/
│   ├── DashboardPage.tsx     # 🏠 System overview
│   ├── RobotControlPage.tsx  # 🎮 Robot control
│   ├── NavigationPage.tsx    # 🧭 Path planning
│   ├── SensorsPage.tsx       # 📡 Sensor monitoring
│   ├── Map2DPage.tsx         # 🗺️ 2D visualization
│   ├── Map3DPage.tsx         # 🎮 3D visualization
│   ├── ChartsPage.tsx        # 📊 Real-time charts
│   ├── DiagnosticsPage.tsx   # 🔍 System diagnostics
│   ├── TerminalPage.tsx      # 💻 Command interface
│   ├── LogsPage.tsx          # 📋 System logs
│   ├── ParametersPage.tsx    # ⚙️ Parameter management
│   ├── NodesPage.tsx         # 🔧 Node management
│   └── SystemStatusPage.tsx  # 💻 System status
└── components/
    ├── MapViewer2D.tsx       # 2D map component
    ├── Robot3DViewer.tsx     # 3D visualization
    ├── VirtualJoystick.tsx   # Joystick control
    ├── Terminal.tsx          # Terminal emulator
    └── Charts/               # Chart components
```

#### State Management
```typescript
// Custom Hooks
useWebSocket()                # WebSocket connection
useROS2Data()                # ROS2 data management
useChartData()               # Chart data processing
useTerminal()                # Terminal functionality

// Data Types
interface RobotData {
  pose?: PoseData;
  odom?: OdometryData;
  battery?: BatteryData;
}

interface SensorData {
  scan?: LaserScanData;
  ultrasonic?: UltrasonicData;
  map?: OccupancyGridData;
}

interface SystemData {
  cpu_usage?: number;
  memory_usage?: number;
  disk_usage?: number;
  ros_nodes?: NodeInfo[];
  diagnostics?: DiagnosticData;
  logs?: LogEntry[];
}
```

### 🔌 ROS2 Integration

#### Topics Subscribed
```bash
/scan                         # sensor_msgs/LaserScan
/odom                         # nav_msgs/Odometry
/battery_state                # sensor_msgs/BatteryState
/map                          # nav_msgs/OccupancyGrid
/diagnostics                  # diagnostic_msgs/DiagnosticArray
/rosout                       # rcl_interfaces/Log
```

#### Topics Published
```bash
/cmd_vel                      # geometry_msgs/Twist
/move_base_simple/goal        # geometry_msgs/PoseStamped
/initialpose                  # geometry_msgs/PoseWithCovarianceStamped
```

#### Services Called
```bash
/get_parameters               # rcl_interfaces/GetParameters
/set_parameters               # rcl_interfaces/SetParameters
/list_nodes                   # rcl_interfaces/ListNodes
```

### 🎮 3D Visualization Technical Details

#### Three.js Scene Setup
```typescript
class Robot3DScene {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private robot: THREE.Group;
  private lidarPoints: THREE.Group;

  // Camera controls with anti-zoom-out protection
  private setupControls() {
    let needsUpdate = false;
    const updateCamera = () => {
      if (!needsUpdate) return;
      // Update camera position
      needsUpdate = false;
    };
  }
}
```

#### Performance Optimizations
- **Point Cloud Sampling:** Every 5th LiDAR point for performance
- **Frustum Culling:** Automatic by Three.js
- **Level of Detail:** Simplified models at distance
- **Memory Management:** Proper disposal of geometries/materials

### 📊 Real-time Charts Implementation

#### Chart Data Processing
```typescript
class ChartDataProcessor {
  private dataBuffer: Map<string, number[]> = new Map();
  private maxDataPoints = 100;

  addDataPoint(metric: string, value: number) {
    const buffer = this.dataBuffer.get(metric) || [];
    buffer.push(value);
    if (buffer.length > this.maxDataPoints) {
      buffer.shift();
    }
    this.dataBuffer.set(metric, buffer);
  }

  getStatistics(metric: string) {
    const data = this.dataBuffer.get(metric) || [];
    return {
      min: Math.min(...data),
      max: Math.max(...data),
      avg: data.reduce((a, b) => a + b, 0) / data.length
    };
  }
}
```

#### Canvas Rendering
- **Custom Canvas Rendering:** High-performance 2D canvas
- **Smooth Animations:** RequestAnimationFrame for 60fps
- **Responsive Design:** Auto-resize on window changes
- **Color Coding:** Different colors for different metrics

### 🔐 Security Considerations

#### Frontend Security
- **Input Validation:** All user inputs validated
- **XSS Prevention:** React's built-in protection
- **CORS Configuration:** Proper CORS headers
- **Content Security Policy:** CSP headers implemented

#### Backend Security
- **Rate Limiting:** Prevent API abuse
- **Input Sanitization:** All inputs sanitized
- **WebSocket Authentication:** Token-based auth
- **Error Handling:** No sensitive info in errors

#### ROS2 Security
- **Node Permissions:** Proper ROS2 permissions
- **Topic Access Control:** Limited topic access
- **Parameter Validation:** Validate all parameters
- **Service Security:** Secure service calls

### 🚀 Performance Optimization

#### Frontend Optimizations
- **Code Splitting:** Lazy loading of pages
- **Memoization:** React.memo for expensive components
- **Virtual Scrolling:** For large data lists
- **Debouncing:** User input debouncing
- **Bundle Optimization:** Webpack optimizations

#### Backend Optimizations
- **Async Processing:** Non-blocking operations
- **Connection Pooling:** Efficient connections
- **Caching:** Redis for frequently accessed data
- **Compression:** Gzip compression enabled

#### ROS2 Optimizations
- **QoS Profiles:** Optimized Quality of Service
- **Message Filtering:** Filter unnecessary messages
- **Node Lifecycle:** Proper node lifecycle management
- **Resource Monitoring:** Monitor CPU/memory usage

### 🧪 Testing Strategy

#### Unit Tests
```bash
# Frontend tests
cd web_interface/frontend
npm test

# Backend tests
cd web_interface/backend
python -m pytest

# ROS2 tests
colcon test
```

#### Integration Tests
- **WebSocket Communication:** Test real-time data flow
- **ROS2 Integration:** Test topic pub/sub
- **API Endpoints:** Test all REST endpoints
- **UI Components:** Test user interactions

#### Performance Tests
- **Load Testing:** Multiple concurrent users
- **Memory Leaks:** Long-running sessions
- **Network Latency:** Various network conditions
- **Browser Compatibility:** Cross-browser testing

### 📈 Monitoring và Logging

#### Application Monitoring
```python
# Backend logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
```

#### System Metrics
- **CPU Usage:** Real-time CPU monitoring
- **Memory Usage:** RAM and swap monitoring
- **Network Traffic:** Bandwidth utilization
- **Disk I/O:** Read/write operations

#### ROS2 Diagnostics
```bash
# Monitor ROS2 system
ros2 topic hz /scan              # Topic frequency
ros2 node info /vehicle_control  # Node information
ros2 param list                  # Parameter list
ros2 service list               # Available services
```

---

## 🔮 ROADMAP PHÁT TRIỂN

### 🎯 Phase 1: Core Features (✅ Completed)
- ✅ Basic robot control
- ✅ Real-time visualization
- ✅ Sensor monitoring
- ✅ System diagnostics

### 🎯 Phase 2: Advanced Features (🚧 In Progress)
- 🔄 **SLAM Integration:** Real-time mapping
- 🔄 **Path Planning:** Advanced algorithms
- 🔄 **Multi-robot Support:** Fleet management
- 🔄 **AI Integration:** Machine learning features

### 🎯 Phase 3: Production Ready (📋 Planned)
- 📋 **Authentication System:** User management
- 📋 **Database Integration:** Data persistence
- 📋 **Cloud Deployment:** Scalable infrastructure
- 📋 **Mobile App:** Native mobile application

### 🎯 Phase 4: Enterprise Features (💭 Future)
- 💭 **Fleet Management:** Multiple vehicles
- 💭 **Analytics Dashboard:** Business intelligence
- 💭 **API Gateway:** External integrations
- 💭 **Microservices:** Distributed architecture

---

## 📞 LIÊN HỆ VÀ HỖ TRỢ

### 🛠️ Technical Support
- **Email:** <EMAIL>
- **Discord:** [Community Server]
- **GitHub:** [Repository Issues]
- **Documentation:** [Wiki Pages]

### 👥 Community
- **ROS2 Vietnam:** Facebook Group
- **Robotics Forum:** Discussion board
- **Stack Overflow:** Tag `autonomous-vehicle`
- **Reddit:** r/ROS2, r/robotics

### 📚 Training Resources
- **Video Tutorials:** YouTube channel
- **Workshops:** Monthly online workshops
- **Certification:** Professional certification program
- **Consulting:** Custom development services

---

**🎉 Cảm ơn bạn đã sử dụng Autonomous Vehicle Web Interface! 🚗✨**
