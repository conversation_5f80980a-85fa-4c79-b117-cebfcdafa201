# 🚗 Autonomous Vehicle Web Interface

> **Complete autonomous vehicle system with web interface for Gazebo simulation**

[![ROS2](https://img.shields.io/badge/ROS2-Humble-blue.svg)](https://docs.ros.org/en/humble/)
[![React](https://img.shields.io/badge/React-18.0-61dafb.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-3178c6.svg)](https://www.typescriptlang.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100-009688.svg)](https://fastapi.tiangolo.com/)

## 🚀 ONE COMMAND TO RUN EVERYTHING

```bash
./run.sh
```

**This single script starts:**
- ✅ Gazebo simulation with robot
- ✅ Web interface (13 pages)
- ✅ Backend API server
- ✅ All ROS2 nodes

**Then open:** http://localhost:3000

### ✨ Tính Năng Nổi Bật

- 🎮 **13 Trang Chức Năng Hoàn Chỉnh:** Dashboard, Robot Control, Navigation, Sensors, 2D/3D Maps, Charts, Diagnostics, Terminal, Logs, Parameters, Nodes, System Status
- 🕹️ **Điều khiển đa dạng:** 3 chế độ - Button Control, Basic Joystick, Advanced Continuous Joystick
- 🗺️ **Visualization 2D/3D:** Bản đồ 2D tương tác và môi trường 3D immersive với Three.js
- 📊 **Real-time monitoring:** 5 biểu đồ thời gian thực và dashboard tổng quan
- 🔧 **Quản lý hệ thống:** ROS2 nodes, parameters, logs, diagnostics
- 💻 **Terminal tích hợp:** Real PTY terminal với ROS2 commands
- 📱 **Responsive design:** Tương thích mobile và desktop

## 🏗️ Kiến Trúc Hệ Thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   ROS2 System   │
│   React + TS    │◄──►│   FastAPI       │◄──►│   Humble        │
│   Port: 3000    │    │   Port: 8000    │    │   + Gazebo      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Cấu Trúc Project

```
indoor_autonomous_vehicle/
├── 🎮 web_interface/               # Web Interface (HOÀN CHỈNH)
│   ├── frontend/                   # React TypeScript App
│   │   ├── src/
│   │   │   ├── components/         # UI Components
│   │   │   ├── pages/             # 13 Main Pages
│   │   │   ├── hooks/             # Custom Hooks
│   │   │   └── types/             # TypeScript Types
│   │   └── public/                # Static Assets
│   └── backend/                   # FastAPI Server
│       ├── main.py                # Main Server
│       ├── websocket_handler.py   # WebSocket Logic
│       └── ros2_bridge.py         # ROS2 Integration
├── 🤖 vehicle_control/            # Motion Control
├── 📡 vehicle_sensors/            # Sensor Processing
├── 🧭 vehicle_navigation/         # Path Planning
├── 🎯 vehicle_simulation/         # Gazebo Simulation
├── 📋 vehicle_interfaces/         # ROS2 Messages
├── 📚 docs/                       # Documentation
├── 🚀 scripts/                    # Launch Scripts
└── 📖 HUONG_DAN_HE_THONG.md      # Tài Liệu Tiếng Việt
```

## 🚀 Quick Start

### 📋 Yêu Cầu Hệ Thống

- **OS:** Ubuntu 22.04 LTS
- **ROS2:** Humble Hawksbill
- **Node.js:** v18+
- **Python:** 3.10+
- **RAM:** 8GB+ (khuyến nghị 16GB)

### ⚡ Cài Đặt Nhanh

```bash
# 1. Setup ROS2 environment
source /opt/ros/humble/setup.bash
colcon build
source install/setup.bash

# 2. Khởi động backend
cd web_interface/backend
pip install fastapi uvicorn websockets rclpy
python main.py &

# 3. Khởi động frontend
cd ../frontend
npm install
npm start

# 4. Khởi động simulation (terminal mới)
ros2 launch vehicle_simulation simulation.launch.py
```

### 🌐 Truy Cập Hệ Thống

- **Web Interface:** http://localhost:3000
- **API Documentation:** http://localhost:8000/docs
- **WebSocket:** ws://localhost:8000/ws

## 📱 13 Trang Chức Năng Hoàn Chỉnh

| Trang | Mô Tả | Tính Năng Chính |
|-------|-------|-----------------|
| 🏠 **Dashboard** | Tổng quan hệ thống | System status, quick stats, battery info |
| 🎮 **Robot Control** | Điều khiển robot | 3 chế độ: Buttons, Basic/Advanced Joystick |
| 🧭 **Navigation** | Điều hướng tự động | Interactive map, goal setting, path planning |
| 📡 **Sensors** | Giám sát cảm biến | LiDAR, Camera, Ultrasonic, IMU real-time |
| 🗺️ **2D Map View** | Bản đồ 2D chi tiết | Zoom/pan, click navigation, occupancy grid |
| 🎮 **3D Visualization** | Môi trường 3D | Virtual Gazebo environment với Three.js |
| 📊 **Real-time Charts** | Biểu đồ thời gian thực | 5 charts: Battery, CPU, Memory, Velocity, LiDAR |
| 🔍 **Diagnostics** | Chẩn đoán hệ thống | Health monitoring, error detection, resources |
| 💻 **Terminal** | Giao diện dòng lệnh | Real PTY terminal, ROS2 commands, history |
| 📋 **System Logs** | Nhật ký hệ thống | Real-time streaming, filtering, export |
| ⚙️ **Parameters** | Quản lý tham số | ROS2 parameter management, presets |
| 🔧 **Node Manager** | Quản lý nodes | Start/stop/restart nodes, monitoring |
| 💻 **System Status** | Trạng thái hệ thống | Hardware monitoring, network, processes |

## 🎮 Chế Độ Điều Khiển

### 1. 🔘 Button Control
- ⬆️⬇️⬅️➡️ Điều khiển cơ bản
- ⏹️ Emergency stop
- 🎛️ Speed adjustment

### 2. 🕹️ Virtual Joystick
- **Basic:** Click & drag control
- **Advanced:** Continuous real-time control
- 📱 Touch-friendly cho mobile

### 3. 🗺️ Map Navigation
- Click trên bản đồ để đặt mục tiêu
- 🛤️ Automatic path planning
- 📍 Waypoint management

## 🔧 Công Nghệ Sử Dụng

### Frontend Stack
- **React 18** + **TypeScript** - Modern UI framework
- **Material-UI** - Professional component library
- **Three.js** - Advanced 3D graphics
- **WebSocket** - Real-time communication

### Backend Stack
- **FastAPI** - High-performance Python web framework
- **WebSocket** - Real-time data streaming
- **ROS2 Bridge** - Seamless ROS2 integration
- **Uvicorn** - Lightning-fast ASGI server

### ROS2 Integration
- **Topics:** `/scan`, `/odom`, `/cmd_vel`, `/battery_state`, `/map`
- **Services:** Parameter management, node control
- **Actions:** Navigation goals, complex behaviors
- **QoS:** Optimized Quality of Service profiles

## 📚 Tài Liệu

- 📖 **[HƯỚNG DẪN CHI TIẾT](HUONG_DAN_HE_THONG.md)** - Tài liệu đầy đủ bằng tiếng Việt
- 🔧 **[API Documentation](http://localhost:8000/docs)** - FastAPI auto-generated docs
- 🎓 **[ROS2 Tutorials](https://docs.ros.org/en/humble/Tutorials.html)** - Official documentation
- 🎮 **[Three.js Guide](https://threejs.org/docs/)** - 3D graphics documentation

## 🎯 Tính Năng Đã Hoàn Thành

- ✅ **13 Trang Web Hoàn Chỉnh** - Tất cả chức năng đã implement
- ✅ **Real-time Data Streaming** - WebSocket với ROS2 bridge
- ✅ **3D Visualization** - Three.js với anti-zoom-out protection
- ✅ **Interactive Controls** - Multiple control modes
- ✅ **System Management** - Comprehensive monitoring tools
- ✅ **Professional UI/UX** - Material-UI design system
- ✅ **Type-Safe Code** - Full TypeScript implementation
- ✅ **Mobile Responsive** - Works on all devices
- ✅ **Performance Optimized** - Smooth 60fps rendering
- ✅ **Error Handling** - Robust error management

## 🤝 Contributing

Chúng tôi hoan nghênh mọi đóng góp! Vui lòng:

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

Dự án này được phân phối dưới giấy phép MIT. Xem [LICENSE](LICENSE) để biết thêm chi tiết.

## 📞 Support

- 📧 **Email:** <EMAIL>
- 💬 **Discord:** [Community Server](https://discord.gg/your-server)
- 🐛 **Issues:** [GitHub Issues](https://github.com/your-repo/issues)
- 📖 **Wiki:** [Documentation Wiki](https://github.com/your-repo/wiki)

---

**🎉 Hệ thống hoàn chỉnh với 13 trang chức năng đầy đủ! Ready for production! 🚗✨**

---

<div align="center">
  <img src="docs/images/logo.png" alt="Autonomous Vehicle Logo" width="200">

  **Made with ❤️ by the Autonomous Vehicle Team**
</div>
