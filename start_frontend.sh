#!/bin/bash

# =============================================================================
# FRONTEND SERVER STARTUP SCRIPT
# =============================================================================
# Starts the React development server
# Run this in a separate terminal from the ROS2 system
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    print_header "SHUTTING DOWN FRONTEND SERVER"
    print_status "Stopping frontend processes..."
    pkill -f "npm.*dev" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    print_success "Frontend shutdown complete"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

print_header "FRONTEND SERVER STARTUP"

# Check if we're in the right directory
if [ ! -f "web_interface/frontend/package.json" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: web_interface/frontend/package.json"
    exit 1
fi

# Check Node.js installation
print_status "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    print_error "Node.js not found!"
    print_error "Install with: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm not found!"
    print_error "npm should be installed with Node.js"
    exit 1
fi

NODE_VERSION=$(node --version)
print_success "Node.js found: $NODE_VERSION"

# Check if port 3000 is available
if netstat -tulpn 2>/dev/null | grep -q ":3000 "; then
    print_warning "Port 3000 is already in use!"
    print_status "Checking what's using port 3000..."
    netstat -tulpn 2>/dev/null | grep ":3000 "
    echo ""
    read -p "Kill existing process on port 3000? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Killing process on port 3000..."
        pkill -f "npm.*dev" 2>/dev/null || true
        pkill -f "vite" 2>/dev/null || true
        sleep 2
    else
        print_error "Cannot start frontend - port 3000 is busy"
        exit 1
    fi
fi

# Change to frontend directory
print_status "Changing to frontend directory..."
cd web_interface/frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found - installing dependencies..."
    print_status "Running npm install..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "npm install failed"
        exit 1
    fi
    print_success "Dependencies installed"
else
    print_success "Dependencies already installed"
fi

# Check if package-lock.json is corrupted
if [ -f "package-lock.json" ]; then
    if ! npm ls >/dev/null 2>&1; then
        print_warning "Dependency tree issues detected - reinstalling..."
        rm -rf node_modules package-lock.json
        npm install
        if [ $? -ne 0 ]; then
            print_error "npm install failed after cleanup"
            exit 1
        fi
    fi
fi

# Start frontend server
print_header "STARTING FRONTEND SERVER"

print_status "Starting Vite development server..."
print_success "Frontend server starting..."
echo ""
print_status "🌐 Frontend Information:"
echo "  🖥️  Web Interface: http://localhost:3000"
echo "  🗺️  Map Interface: Interactive robot control"
echo "  📊 Real-time Data: Robot position, LiDAR, navigation"
echo "  🎮 Controls:      Click map to set navigation goals"
echo ""
print_status "🔗 Backend Connection:"
echo "  📡 API Endpoint:  http://localhost:8000"
echo "  🔄 WebSocket:     Real-time updates"
echo "  ❤️  Auto-reconnect: If backend restarts"
echo ""
print_status "🎯 Usage:"
echo "  1. Wait for 'compiled successfully' message"
echo "  2. Open browser to http://localhost:3000"
echo "  3. Ensure backend is running (start_backend.sh)"
echo "  4. Click on map to navigate robot"
echo ""
print_warning "Press Ctrl+C to stop the frontend server"
echo ""

# Set environment variable to automatically open browser
export BROWSER=none

# Start the Vite development server
npm run dev:local

# This line should not be reached unless the server exits
print_warning "Frontend server has stopped"
