#!/bin/bash

# =============================================================================
# INDOOR AUTONOMOUS VEHICLE SYSTEM - MASTER STARTUP SCRIPT
# =============================================================================
# This script starts ALL components needed for the indoor navigation system:
# - Gazebo simulation (or real robot hardware)
# - SLAM mapping with obstacle avoidance navigation
# - Web interface (frontend + backend)
# - All ROS2 nodes in ONE TERMINAL
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup on exit
cleanup() {
    print_header "SHUTTING DOWN SYSTEM"
    print_status "Stopping all processes..."

    # Kill all related processes
    killall -9 gazebo gzserver gzclient 2>/dev/null || true
    pkill -f "ros2" 2>/dev/null || true
    pkill -f "slam_toolbox" 2>/dev/null || true
    pkill -f "simple_obstacle_avoidance" 2>/dev/null || true
    pkill -f "npm start" 2>/dev/null || true
    pkill -f "python3 main.py" 2>/dev/null || true

    print_success "System shutdown complete"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Parse command line arguments
USE_GAZEBO=true
USE_SIM_TIME=true
USE_REAL_ROBOT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --real-robot)
            USE_REAL_ROBOT=true
            USE_GAZEBO=false
            USE_SIM_TIME=false
            shift
            ;;
        --simulation)
            USE_REAL_ROBOT=false
            USE_GAZEBO=true
            USE_SIM_TIME=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --simulation    Run in Gazebo simulation mode (default)"
            echo "  --real-robot    Run with real robot hardware"
            echo "  --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0              # Run simulation (default)"
            echo "  $0 --simulation # Run simulation explicitly"
            echo "  $0 --real-robot # Run with real robot (requires hardware setup)"
            echo ""
            echo "For real robot setup, see: REAL_ROBOT_SETUP.md"
            echo "For troubleshooting, use: ./fix_system.sh --help"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "INDOOR AUTONOMOUS VEHICLE SYSTEM"
print_status "Starting system in $([ "$USE_REAL_ROBOT" = true ] && echo "REAL ROBOT" || echo "SIMULATION") mode"

# Check if we're in the right directory
if [ ! -f "src/indoor_nav_bringup/package.xml" ]; then
    print_error "Please run this script from the workspace root directory"
    print_error "Expected to find: src/indoor_nav_bringup/package.xml"
    exit 1
fi

# Real robot validation
if [ "$USE_REAL_ROBOT" = true ]; then
    print_header "REAL ROBOT MODE - HARDWARE VALIDATION"
    print_warning "Real robot mode requires proper hardware setup!"
    print_warning "See REAL_ROBOT_SETUP.md for complete requirements"
    echo ""

    print_status "Checking required hardware topics..."

    # Give user time to start hardware drivers
    print_status "Please ensure your robot hardware drivers are running:"
    echo "  📡 LiDAR driver publishing to /scan"
    echo "  🤖 Motor controller subscribing to /cmd_vel"
    echo "  📍 Odometry system publishing to /odom"
    echo "  🔄 Transform publisher for odom→base_link"
    echo ""

    read -p "Press Enter when hardware drivers are ready, or Ctrl+C to abort..."
    echo ""

    # Check if ROS2 is running and topics exist
    print_status "Validating ROS2 topics..."
    timeout 5 ros2 topic list > /tmp/topics.txt 2>/dev/null || {
        print_error "Cannot connect to ROS2 - ensure ROS2 is sourced and running"
        print_error "Try: source /opt/ros/humble/setup.bash"
        exit 1
    }

    # Check critical topics
    if ! grep -q "/scan" /tmp/topics.txt; then
        print_warning "⚠️  /scan topic not found - LiDAR driver may not be running"
    else
        print_success "✅ /scan topic found"
    fi

    if ! grep -q "/odom" /tmp/topics.txt; then
        print_warning "⚠️  /odom topic not found - odometry system may not be running"
    else
        print_success "✅ /odom topic found"
    fi

    rm -f /tmp/topics.txt

    print_warning "⚠️  SAFETY REMINDER:"
    echo "  🛑 Keep emergency stop ready: Ctrl+C or power switch"
    echo "  🐌 System starts with conservative speeds"
    echo "  👀 Monitor robot behavior closely"
    echo ""
fi

# Step 1: Environment Setup
print_header "STEP 1: ENVIRONMENT SETUP"

print_status "Deactivating conda environment..."
conda deactivate 2>/dev/null || true

print_status "Sourcing ROS2 environment..."
source /opt/ros/humble/setup.bash

print_status "Building workspace..."
colcon build --packages-select indoor_nav_bringup
if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

print_status "Sourcing workspace..."
source install/setup.bash

print_success "Environment ready"

# Step 2: Start Web Interface
print_header "STEP 2: STARTING WEB INTERFACE"

print_status "Starting frontend server..."
cd web_interface/frontend
npm start &
FRONTEND_PID=$!
cd ../..

print_status "Starting backend server..."
cd web_interface/backend/app
python3 main.py &
BACKEND_PID=$!
cd ../../..

print_success "Web interface started"
print_status "Frontend PID: $FRONTEND_PID"
print_status "Backend PID: $BACKEND_PID"

# Wait for web interface to start
sleep 5

# Step 3: Start ROS2 Navigation System
print_header "STEP 3: STARTING ROS2 NAVIGATION SYSTEM"

if [ "$USE_REAL_ROBOT" = true ]; then
    print_status "Launching real robot navigation system..."
    ros2 launch indoor_nav_bringup simple_nav2.launch.py \
        use_gazebo:=false \
        use_sim_time:=false \
        use_rviz:=false &
else
    print_status "Launching Gazebo simulation navigation system..."
    ros2 launch indoor_nav_bringup simple_nav2.launch.py \
        use_gazebo:=true \
        use_sim_time:=true \
        use_rviz:=false &
fi

ROS2_PID=$!

print_success "ROS2 navigation system started"
print_status "ROS2 PID: $ROS2_PID"

# Step 4: System Status
print_header "STEP 4: SYSTEM STATUS"

print_success "🎉 SYSTEM STARTUP COMPLETE!"
echo ""
print_status "📊 System Components:"
echo "  🌐 Frontend:     http://localhost:3000"
echo "  🔧 Backend API:  http://localhost:8000"
if [ "$USE_REAL_ROBOT" = true ]; then
    echo "  🤖 Robot:       Real hardware mode"
    echo "  📡 Sensors:     Connect real LiDAR to /scan topic"
    echo "  ⚙️  Motors:      Connect real motors to /cmd_vel topic"
else
    echo "  🏠 Gazebo:      Indoor house simulation"
    echo "  🤖 Robot:       Simulated with LiDAR"
fi
echo "  🗺️  SLAM:        Real-time mapping active"
echo "  🧠  Navigation:  Obstacle avoidance ready"
echo ""

print_status "🎮 How to use:"
echo "  1. Open web browser: http://localhost:3000"
echo "  2. Click on map to set navigation goals"
echo "  3. Robot will navigate with obstacle avoidance"
echo "  4. Press Ctrl+C to stop all systems"
echo ""

print_status "📡 ROS2 Topics:"
echo "  /scan          - LiDAR data"
echo "  /cmd_vel       - Robot velocity commands"
echo "  /odom          - Robot odometry"
echo "  /goal_pose     - Navigation goals"
echo "  /map           - SLAM map data"
echo ""

print_warning "System is running. Press Ctrl+C to stop all processes."

# Wait for all background processes
wait
